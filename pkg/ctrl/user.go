package ctrl

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/abiosoft/ishell/v2"
	"github.com/gaoxing520/errors"
	"github.com/olekukonko/tablewriter"
	"github.com/rs/zerolog"

	computehub "git.sho/ComputeHub/ComputeHub"
	"git.sho/ComputeHub/ComputeHub/pkg/models"
	"git.sho/ComputeHub/ComputeHub/pkg/service"
	cu "git.sho/ComputeHub/ComputeHub/utils"
	"git.sho/ComputeHub/HubCommon/pagination"
	"git.sho/ComputeHub/SuanLink"
	"git.sho/ComputeHub/SuanLink/pkg/config"
	"git.sho/ComputeHub/SuanLink/pkg/consts"
	"git.sho/ComputeHub/SuanLink/pkg/utils"
)

var (
	VoucherListFields = []string{
		"ID",
		"类型",
		"总金额(元)",
		"已用金额(元)",
		"剩余金额(元)",
		"状态",
		"资源池",
		"过期时间",
		"创建时间",
	}
)

func AddUserCmd(shell *ishell.Shell, ctl *Ctrl) {
	shell.AddCmd(&ishell.Cmd{
		Name:    "login",
		Help:    "用户登录",
		Aliases: []string{"user-login", "1-user-login"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.login)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "logout",
		Help:    "用户退出",
		Aliases: []string{"user-logout", "2-user-logout"},
		Func: func(c *ishell.Context) {
			shell.SetHistoryPath("")
			ctl.runShellCmd(c, ctl.logout)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "user-info",
		Help:    "获取当前用户信息",
		Aliases: []string{"3-user-info"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.userInfo)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "user-deposit",
		Help:    "当前用户为自己充值",
		Aliases: []string{"4-user-deposit"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.userDeposit)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "user-voucher-list",
		Help:    "查看当前用户的代金券列表",
		Aliases: []string{"5-user-voucher-list"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.voucherList)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "user-update",
		Help:    "更新当前用户设置",
		Aliases: []string{"6-user-update"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.userUpdate)
		},
	})
}

func (ctl *Ctrl) login(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	c.Println("开始进行用户验证")
	c.Println("	通过手机号验证")
	c.Printf("	请输入手机号码: ")

	cellphone := c.ReadLine()
	if cellphone == "" {
		return nil, suanlink.ErrRequest.With("手机号检查未通过: 空手机号").LogDebug()
	}

	if !utils.VerifyMobileFormat(cellphone) {
		return nil, suanlink.ErrRequest.With("手机号检查未通过: 格式错误: %s", cellphone).LogDebug()
	}

	var (
		ae     errors.AppError
		secret = ctl.config.Secret
	)

	if secret == "" {
		secret, ae = config.GenSecret(cellphone)
		if ae != nil {
			return nil, ae
		}
	}

	rc := ctl.rcWithSecret(secret)
	resp, err := rc.R().
		SetResult(&ApiResponse[service.CaptchaResp]{}).
		SetBody(service.GetCaptchaReq{
			Cellphone: cellphone,
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthCaptcha))
	if err != nil {
		return nil, suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	captchaResp := resp.Result().(*ApiResponse[service.CaptchaResp])
	if code := captchaResp.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("captcha resp data code: %d", code).LogError()
	}

	c.Println(captchaResp.Data.Content)
	c.Printf("请输入验证码: ")
	captcha := strings.TrimSpace(c.ReadLine())

	resp, err = rc.R().
		SetResult(&ApiResponse[service.SendCodeResp]{}).
		SetBody(service.SendCodeReq{
			Cellphone: cellphone,
			Token:     captchaResp.Data.Token,
			Captcha:   captcha,
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthSmsCode))
	if err != nil {
		return nil, suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	smsCodeResp := resp.Result().(*ApiResponse[service.SendCodeResp])
	if code := smsCodeResp.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("sms code resp data code: %d", code).LogError()
	}

	if !smsCodeResp.Data.Sent {
		return nil, suanlink.ErrApiRequest.With("send sms code failed").LogError()
	}

	c.Printf("请输入短信验证码: ")
	sms := strings.TrimSpace(c.ReadLine())

	now := time.Now()
	resp, err = rc.R().
		SetResult(&ApiResponse[service.LoginByCodeResp]{}).
		SetBody(service.LoginByCodeReq{
			Token:     captchaResp.Data.Token,
			Code:      sms,
			Cellphone: cellphone,
			MachineID: utils.MachineId(),
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthSmsLogin))
	if err != nil {
		return nil, suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	smsLoginResp := resp.Result().(*ApiResponse[service.LoginByCodeResp])
	if code := smsLoginResp.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("sms login resp data code: %d", code).LogError()
	}

	if smsLoginResp.Data.RefreshToken != "" && smsLoginResp.Data.AccessToken != "" {
		ctl.config.CellPhone = cellphone
		ctl.accessToken = smsLoginResp.Data.AccessToken
		ctl.config.RefreshToken = smsLoginResp.Data.RefreshToken
		ctl.config.RefreshTokenCreatedAt = now.Unix()
	}

	ate, rte, err := ctl.getTokensExpireSeconds()
	if err != nil {
		ate = computehub.DefaultAccessExpire
		rte = computehub.DefaultRefreshExpire
	}
	ctl.accessTokenExpireSeconds = ate
	ctl.refreshTokenExpireSeconds = rte
	ctl.accessTokenCreatedAt = now.Unix()
	ctl.refreshTokenCreatedAt = now.Unix()

	ctl.saveConfigToFile()

	return ctl.showUserInfo(c)
}

func (ctl *Ctrl) logout(c *ishell.Context) (any, error) {
	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	ctl.config = &config.Config{
		Debug:    false,
		LogLevel: zerolog.LevelInfoValue,
	}

	ctl.saveConfigToFile()
	c.Println("当前用户已退出登录")

	return nil, nil
}

func (ctl *Ctrl) refresh() errors.AppError {
	if ctl.config.RefreshToken == "" {
		return suanlink.ErrParameter.With("refresh token is empty").LogError()
	}

	now := time.Now()
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[service.RefreshTokenResp]{}).
		SetBody(service.RefreshTokenReq{
			RefreshToken: ctl.config.RefreshToken,
			MachineID:    utils.MachineId(),
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthRefreshToken))
	if err != nil {
		return suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[service.RefreshTokenResp])
	if code := result.Code; code != suanlink.Success.Code() {
		return suanlink.ErrApiRequest.With("refresh access token resp data code: %d", code).LogError()
	}
	ctl.accessToken = result.Data.AccessToken

	ate, rte, err := ctl.getTokensExpireSeconds()
	if err != nil {
		ate = computehub.DefaultAccessExpire
		rte = computehub.DefaultRefreshExpire
	}

	ctl.accessTokenExpireSeconds = ate
	ctl.refreshTokenExpireSeconds = rte
	ctl.accessTokenCreatedAt = now.Unix()

	return nil
}

func (ctl *Ctrl) userInfo(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查用户登录状态
	if _, err := ctl.prepare(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	return ctl.showUserInfo(c)
}

func (ctl *Ctrl) showUserInfo(c *ishell.Context) (any, error) {
	// 发送请求获取用户信息
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.User]{}).
		Get(ctl.config.EndPoint(consts.ApiUrlUserInfo))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.User])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("user info resp data code: %d", code).LogError()
	}

	// 显示用户信息
	user := result.Data
	c.Println()
	c.Printf("用户信息\n")
	c.Println("=" + strings.Repeat("=", 40))
	c.Printf("ID: %d\n", user.Id)
	c.Printf("用户名: %s\n", user.Name)
	c.Printf("昵称: %s\n", user.Nickname)
	c.Printf("手机号: %s\n", user.Cellphone)
	c.Printf("邮箱: %s\n", user.Email)
	c.Printf("部门: %s\n", user.Department)
	c.Printf("账户余额: %s 元\n", utils.FormatPriceCentsIntoYuan(user.BalanceInCents))
	c.Printf("透支额度: %s 元\n", utils.FormatPriceCentsIntoYuan(user.OverdraftLimitInCents))
	c.Printf("优先使用代金券: %s\n", ctl.formatBooleanSetting(user.UseVoucherFirstForPayment))
	c.Printf("角色类型: %s\n", user.RoleType)
	c.Printf("平台角色: %s\n", utils.FormatPlatformRole(user.PlatformRole))
	c.Printf("创建时间: %s\n", utils.FormatCommonTime(user.CreatedAt))
	c.Printf("更新时间: %s\n", utils.FormatCommonTime(user.UpdatedAt))

	return nil, nil
}

func (ctl *Ctrl) userDeposit(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查用户登录状态
	if _, err := ctl.prepare(c); err != nil {
		return nil, err
	}

	c.Println("用户账户充值")
	c.Println("=" + strings.Repeat("=", 30))

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	cfg, err := ctl.getConfig()
	if err != nil {
		return nil, err
	}

	if !cfg.OnlinePayment {
		return nil, fmt.Errorf("在线充值功能未启用")
	}

	if len(cfg.PaymentMethods) == 0 {
		return nil, fmt.Errorf("未找到有效的支付方式")
	}

	paymentMethodNames := make([]string, 0)
	paymentMethods := make([]string, 0)
	for _, method := range cfg.PaymentMethods {
		pm, err := utils.FormatPaymentMethod(method)
		if err != nil {
			return nil, err
		}
		paymentMethodNames = append(paymentMethodNames, pm)
		paymentMethods = append(paymentMethods, method)
	}

	choice := c.MultiChoice(paymentMethodNames, "请选择支付方式")

	// 获取充值金额
	c.Printf("充值金额(元): ")
	amountStr := strings.TrimSpace(c.ReadLine())
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil || amount <= 0 {
		c.Printf("错误: 无效的金额格式 '%s', 请输入正数\n", amountStr)
		return nil, nil
	}

	if se := ctl.refresh(); se != nil {
		return nil, se.With("function", "refresh").LogError()
	}
	paymentMethodName := paymentMethodNames[choice]

	// 构建请求数据
	depositData := &service.DepositUserReq{
		Uid:           0,                      // 服务端会从认证信息中获取
		AmountInCents: int64(amount * 100),    // 转换为分
		PaymentMethod: paymentMethods[choice], // 支付方式
	}

	// 发送充值请求
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[service.DepositUserResp]{}).
		SetBody(depositData).
		Post(ctl.config.EndPoint(consts.ApiUrlUserDeposit))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[service.DepositUserResp])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("user deposit resp data code: %d", code).LogError()
	}

	// 显示充值结果
	data := result.Data
	c.Println()
	c.Printf("充值请求提交成功!\n")
	c.Printf("您选择的支付方式: %s\n", paymentMethodName)
	c.Printf("充值金额: %.2f 元\n", amount)
	if data.CreatedOrder != nil {
		c.Printf("订单号: %s\n", data.CreatedOrder.No)
		c.Printf("订单ID: %d\n", data.CreatedOrder.ID)
	}
	if data.CodeURL != "" {
		utils.DisplayQRCode(data.CodeURL, paymentMethodName, c)
	}
	c.Println()

	return nil, nil
}

func (ctl *Ctrl) prepare(c *ishell.Context) (any, error) {
	if ctl.config.RefreshToken == "" || ctl.config.CellPhone == "" || ctl.config.RefreshTokenCreatedAt == 0 || ctl.config.RefreshTokenCreatedAt+computehub.DefaultRefreshExpire < time.Now().Unix() {
		return ctl.login(c)
	}

	if ctl.accessTokenCreatedAt+ctl.accessTokenExpireSeconds < time.Now().Unix() {
		ctl.refresh()
	}

	if len(ctl.pools) == 0 {
		pools, err := ctl.getPoolList()
		if err != nil {
			return nil, err
		}

		ctl.pools = pools
	}

	return nil, nil
}

// voucherList 获取当前用户的代金券列表
func (ctl *Ctrl) voucherList(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	var (
		params = &pagination.QueryParams{
			Page:  1,
			Limit: 25,
		}
		total uint
	)

	if _, err := ctl.prepare(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	for {
		table := tablewriter.NewWriter(os.Stdout)
		table.Header(VoucherListFields)

		if params.Page == 0 {
			params.Page = 1
		}

		if total > 0 && params.Page > (int(total)/params.Limit)+1 {
			break
		}

		resp, err := ctl.rc().R().
			SetResult(&ApiResponse[cu.QueryResult[*models.Voucher]]{}).
			SetBody(params).
			Post(ctl.config.EndPoint(consts.ApiUrlVoucherList))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}

		switch code := resp.StatusCode(); code {
		case http.StatusOK:
		case http.StatusUnauthorized:
			if se := ctl.refresh(); se != nil {
				return nil, se.With("function", "refresh").LogError()
			}
			continue
		default:
			return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
		}

		result := resp.Result().(*ApiResponse[cu.QueryResult[*models.Voucher]])
		if code := result.Code; code != suanlink.Success.Code() {
			return nil, suanlink.ErrApiRequest.With("voucher list resp data code: %d", code).LogError()
		}
		total = result.Data.Total

		for _, voucher := range result.Data.Records {
			table.Append(ctl.formatVoucherRow(voucher))
		}
		table.Render()

		if len(result.Data.Records) < params.Limit {
			break
		}

		c.Printf("Total(%d), Page(%d), Limit(%d), (N)ext, (P)revious, [Q]uit\n", result.Data.Total, params.Page, params.Limit)
		input := strings.ToLower(strings.TrimSpace(c.ReadLine()))
		switch input {
		case "", "n", "next":
			params.Page++
		case "p", "previous", "prev":
			if params.Page > 1 {
				params.Page--
			}
		case "q", "quit", "exit":
			return nil, nil
		default:
			c.Printf("无效输入，请输入 n/p/q\n")
		}
	}

	return nil, nil
}

// formatVoucherRow 格式化代金券行数据
func (ctl *Ctrl) formatVoucherRow(voucher *models.Voucher) []string {
	id := fmt.Sprintf("%d", voucher.Id)
	voucherType := ctl.formatVoucherType(voucher.Type)
	totalAmount := fmt.Sprintf("%.2f", float64(voucher.TotalAmountInCents)/100)
	usedAmount := fmt.Sprintf("%.2f", float64(voucher.UsedAmountInCents)/100)
	remainingAmount := fmt.Sprintf("%.2f", float64(voucher.TotalAmountInCents-voucher.UsedAmountInCents)/100)
	status := ctl.formatVoucherStatus(voucher)
	pool := voucher.Pool
	if pool == "" {
		pool = "通用"
	}
	expireTime := voucher.ExpiresAt.Format(consts.CommonDateTimeFormat)
	createTime := voucher.CreatedAt.Format(consts.CommonDateTimeFormat)

	return []string{
		id,
		voucherType,
		totalAmount,
		usedAmount,
		remainingAmount,
		status,
		pool,
		expireTime,
		createTime,
	}
}

// formatVoucherType 格式化代金券类型
func (ctl *Ctrl) formatVoucherType(voucherType string) string {
	switch voucherType {
	case models.VoucherTypeComputility:
		return "算力券"
	default:
		return voucherType
	}
}

// formatVoucherStatus 格式化代金券状态
func (ctl *Ctrl) formatVoucherStatus(voucher *models.Voucher) string {
	if voucher.MaxedOut {
		return "已用完"
	}
	if time.Now().After(voucher.ExpiresAt) {
		return "已过期"
	}
	return "可用"
}

// userUpdate 更新当前用户设置
func (ctl *Ctrl) userUpdate(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	if _, err := ctl.prepare(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// 获取当前用户信息
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.User]{}).
		Get(ctl.config.EndPoint(consts.ApiUrlUserInfo))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.User])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("user info resp data code: %d", code).LogError()
	}

	user := result.Data
	c.Println()
	c.Printf("当前用户设置\n")
	c.Println("=" + strings.Repeat("=", 40))
	c.Printf("优先使用代金券支付: %s\n", ctl.formatBooleanSetting(user.UseVoucherFirstForPayment))
	c.Println()

	// 询问是否要修改设置
	choice := c.MultiChoice([]string{
		"修改优先使用代金券支付设置",
		"取消",
	}, "请选择要执行的操作")

	if choice == 1 { // 取消
		return nil, nil
	}

	// 修改优先使用代金券支付设置
	newSetting := c.MultiChoice([]string{
		"启用 - 优先使用代金券支付",
		"禁用 - 优先使用余额支付",
	}, "请选择新的设置")

	useVoucherFirst := newSetting == 0

	// 构建更新请求
	updateReq := &service.UpdateUserReq{
		Uid:                       user.Id,
		UseVoucherFirstForPayment: &useVoucherFirst,
	}

	// 发送更新请求
	resp, err = ctl.rc().R().
		SetResult(&ApiResponse[models.User]{}).
		SetBody(updateReq).
		Post(ctl.config.EndPoint(fmt.Sprintf(consts.ApiUrlUserUpdate, user.Id)))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重试更新请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.User]{}).
			SetBody(updateReq).
			Post(ctl.config.EndPoint(fmt.Sprintf(consts.ApiUrlUserUpdate, user.Id)))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}
		if code := resp.StatusCode(); code != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	updateResult := resp.Result().(*ApiResponse[models.User])
	if code := updateResult.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("user update resp data code: %d", code).LogError()
	}

	c.Println()
	c.Printf("用户设置更新成功！\n")
	c.Printf("优先使用代金券支付: %s\n", ctl.formatBooleanSetting(useVoucherFirst))

	return nil, nil
}

// formatBooleanSetting 格式化布尔设置显示
func (ctl *Ctrl) formatBooleanSetting(value bool) string {
	if value {
		return "启用"
	}
	return "禁用"
}
