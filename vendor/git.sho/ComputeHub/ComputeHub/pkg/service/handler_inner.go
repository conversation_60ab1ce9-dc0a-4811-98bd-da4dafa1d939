package service

import (
	"fmt"

	"github.com/gin-gonic/gin"

	"devgitlab.lianoid.com/jasonni/eaas-common/log"
	hub "git.sho/ComputeHub/ComputeHub"
	"git.sho/ComputeHub/ComputeHub/utils"
)

func (s *ComputeHubService) HttpHandleReload(c *gin.Context) {
	log.Infof("ready to reload service")

	data := &utils.ServiceReloadReq{}
	if err := c.ShouldBindJSON(data); err != nil {
		err = fmt.Errorf("bind json data from post request error: %+v", err)
		log.Error(err)
		utils.AbortReqOnErr(c, hub.ParamsInvalidErr.FromError(err))
		return
	}
	log.Tracef("reload service data: (%+v)", data)

	if !data.ReloadPlatformsAndTokens && !data.ReloadUsers &&
		!data.ReloadProviders && !data.ReloadSystemConfigs && !data.ReloadBillingPlans {
		utils.AbortReqOnErr(c, hub.ParamsInvalidErr.FromStr("nothing need to be reloaded"))
		return
	}

	results := utils.ServiceReloadResp{}

	if data.ReloadPlatformsAndTokens {
		respChan := make(chan *hub.ComputeHubError, 1)
		s.serviceCmdChan <- ServiceCmdReq{Cmd: ServiceCmdReloadPlatformsAndTokens, RespChan: respChan}
		err := <-respChan
		results[string(ServiceCmdReloadPlatformsAndTokens)] = &utils.ApiResponse[any]{
			Code: err.Code(),
			Msg:  err.Error(),
			Data: nil,
		}
	}

	if data.ReloadUsers {
		respChan := make(chan *hub.ComputeHubError, 1)
		s.serviceCmdChan <- ServiceCmdReq{Cmd: ServiceCmdReloadUsers, RespChan: respChan}
		err := <-respChan
		results[string(ServiceCmdReloadUsers)] = &utils.ApiResponse[any]{
			Code: err.Code(),
			Msg:  err.Error(),
		}
	}

	if data.ReloadProviders {
		respChan := make(chan *hub.ComputeHubError, 1)
		s.serviceCmdChan <- ServiceCmdReq{Cmd: ServiceCmdReloadProviders, RespChan: respChan}
		err := <-respChan
		results[string(ServiceCmdReloadProviders)] = &utils.ApiResponse[any]{
			Code: err.Code(),
			Msg:  err.Error(),
		}
	}

	if data.ReloadSystemConfigs {
		respChan := make(chan *hub.ComputeHubError, 1)
		s.serviceCmdChan <- ServiceCmdReq{Cmd: ServiceCmdReloadSystemConfigs, RespChan: respChan}
		err := <-respChan
		results[string(ServiceCmdReloadSystemConfigs)] = &utils.ApiResponse[any]{
			Code: err.Code(),
			Msg:  err.Error(),
		}
	}

	if data.ReloadBillingPlans {
		respChan := make(chan *hub.ComputeHubError, 1)
		s.serviceCmdChan <- ServiceCmdReq{Cmd: ServiceCmdReloadBillingPlans, RespChan: respChan}
		err := <-respChan
		results[string(ServiceCmdReloadBillingPlans)] = &utils.ApiResponse[any]{
			Code: err.Code(),
			Msg:  err.Error(),
		}
	}

	log.Tracef("reload result: (%+v)", results)

	utils.RespondDataToHttpClient(c, results)
}
