package ctrl

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/abiosoft/ishell/v2"
	"github.com/olekukonko/tablewriter"

	"git.sho/ComputeHub/ComputeHub/pkg/models"
	"git.sho/ComputeHub/ComputeHub/pkg/service"
	cu "git.sho/ComputeHub/ComputeHub/utils"
	"git.sho/ComputeHub/SuanLink"
	"git.sho/ComputeHub/SuanLink/pkg/consts"
	"git.sho/ComputeHub/SuanLink/pkg/utils"

	"devgitlab.lianoid.com/jasonni/eaas-common/pagination"
)

var (
	AppListFields = []string{
		"ID",
		"名称",
		"状态",
		"区域",
		"GPU",
		"CPU核心",
		"内存(GB)",
		"存储(GB)",
		"镜像",
		"价格(元/小时)",
		"到期时间",
		"创建时间",
		"更新时间",
	}

	AppServiceFields = []string{
		"服务类型",
		"IP地址",
		"端口",
		"用户名",
		"密码",
	}

	AppSpecListSimpleFields = []string{
		"ID",
		"GPU",
		"CPU核心",
		"内存(GB)",
		"镜像",
		"价格(元/小时)",
		"计费方式",
		"经济模式",
		"标签",
	}

	AppCompletionListFields = []string{
		"ID",
		"名称",
		"状态",
		"区域",
	}

	AppOperationFields = []string{
		"操作",
		"描述",
	}
)

func AddAppCmd(shell *ishell.Shell, ctl *Ctrl) {
	shell.AddCmd(&ishell.Cmd{
		Name: "app-list",
		Help: "app-list - 查看应用实例列表",
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.appList)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "app-detail",
		Help:    "app-detail <id> - 获取指定ID的应用详情",
		Aliases: []string{"app-get"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.appDetail)
		},
		Completer: func(args []string) []string {
			return ctl.getAppIDsForCompletion()
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name: "app-create",
		Help: "app-create - 创建新应用",
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.appCreate)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name: "app-operate",
		Help: "app-operate <id> <action> - 操作应用 (start/start-with-simple-mode/stop/restart/release)",
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.appOperate)
		},
		Completer: func(args []string) []string {
			if len(args) == 0 {
				return ctl.getAppIDsForCompletion()
			} else if len(args) == 1 {
				return ctl.getAppOperationsForCompletion(args[0])
			}
			return []string{}
		},
	})
}

func (ctl *Ctrl) appList(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	var (
		params = &pagination.QueryParams{
			Page:  1,
			Limit: 25,
		}
		total uint
	)

	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	for {
		table := tablewriter.NewWriter(os.Stdout)
		table.Header(AppListFields)

		if params.Page == 0 {
			params.Page = 1
		}

		if total > 0 && params.Page > (int(total)/params.Limit)+1 {
			break
		}

		resp, err := ctl.rc().R().
			SetResult(&ApiResponse[cu.QueryResult[*models.App]]{}).
			SetBody(params).
			Post(ctl.config.EndPoint(consts.ApiUrlAppList))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}

		switch code := resp.StatusCode(); code {
		case http.StatusOK:
		case http.StatusUnauthorized:
			if se := ctl.refresh(); se != nil {
				return nil, se.With("function", "refresh").LogError()
			}
			continue
		default:
			return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
		}

		result := resp.Result().(*ApiResponse[cu.QueryResult[*models.App]])
		if code := result.Code; code != suanlink.Success.Code() {
			return nil, suanlink.ErrApiRequest.With("app list resp data code: %d", code).LogError()
		}
		total = result.Data.Total

		for _, app := range result.Data.Records {
			table.Append(ctl.formatAppRowFromModel(app))
		}

		table.Render()

		// 如果总数为0或者当前页已经是最后一页且数据不足一页，直接结束
		if total == 0 || (total <= uint(params.Limit) && params.Page == 1) {
			break
		}

		c.Printf("Total(%d), Page(%d), Limit(%d), (N)ext, (P)revious, [Q]uit: ", result.Data.Total, params.Page, params.Limit)
		switch input := strings.TrimSpace(c.ReadLine()); input {
		case "q", "Q":
			return nil, nil
		case "n", "N":
			params.Page += 1
			continue
		case "p", "P":
			params.Page -= 1
			continue
		default:
			params.Page += 1
			continue
		}
	}

	return nil, nil
}

func (ctl *Ctrl) formatAppRowFromModel(app *models.App) []string {
	id := fmt.Sprintf("%d", app.Id)
	name := app.Name
	mode := "标准模式"
	if app.RunningOnSimpleMode {
		mode = "经济模式"
	}

	state := fmt.Sprintf("%s", utils.FormatAppState(app.State))
	area := app.Area
	gpu := fmt.Sprintf("%s x%d", app.GpuModel, app.GpuCards)
	cpuCores := fmt.Sprintf("%d", app.CpuCores)
	memGB := fmt.Sprintf("%.1f", float64(app.MemInMB)/1024)
	storageGB := fmt.Sprintf("%.1f", float64(app.StorageInMB)/1024)
	image := app.Image

	price := app.PriceInCents
	if app.RunningOnSimpleMode {
		price = app.PriceInCentsOnSimpleMode
	}

	return []string{
		id,
		name,
		state,
		area,
		gpu,
		cpuCores,
		memGB,
		storageGB,
		image,
		fmt.Sprintf("%s - %s", utils.FormatPriceCentsIntoYuan(price), mode),
		utils.FormatCommonTime(app.ExpireAt),
		utils.FormatCommonTime(app.CreatedAt),
		utils.FormatCommonTime(app.UpdatedAt),
	}
}

func (ctl *Ctrl) appDetail(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查参数
	if len(c.Args) == 0 {
		c.Println("错误: 请提供应用ID")
		c.Println("用法: app-detail <id>")
		return nil, nil
	}

	// 解析ID参数
	idStr := c.Args[0]
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Printf("错误: 无效的ID格式 '%s', 请提供数字ID\n", idStr)
		return nil, nil
	}

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	app, err := ctl.getAppById(id)
	if err != nil {
		return nil, err
	}

	c.Println()
	c.Printf("应用详情 (ID: %d)\n", app.Id)
	c.Println("=" + strings.Repeat("=", 60))

	// 基本信息
	mode := "标准模式"
	if app.RunningOnSimpleMode {
		mode = "经济模式"
	}

	c.Printf("名称: %s\n", app.Name)
	c.Printf("状态: %s %s\n", mode, utils.FormatAppState(app.State))
	c.Printf("区域: %s\n", app.Area)
	c.Printf("经济模式: %s\n", utils.FormatSupport(app.SupportSimpleMode))
	c.Printf("GPU型号: %s\n", app.GpuModel)
	c.Printf("GPU卡数: %d\n", app.GpuCards)
	c.Printf("CPU核心: %d\n", app.CpuCores)
	c.Printf("内存: %.1f GB\n", float64(app.MemInMB)/1024)
	c.Printf("存储: %.1f GB\n", float64(app.StorageInMB)/1024)
	c.Printf("镜像: %s\n", app.Image)

	if app.SupportSimpleMode {
		c.Printf("价格: (标准模式 %s 元/小时), (经济模式 %s 元/小时)\n",
			utils.FormatPriceCentsIntoYuan(app.PriceInCents),
			utils.FormatPriceCentsIntoYuan(app.PriceInCentsOnSimpleMode),
		)
	} else {
		c.Printf("价格: (标准模式 %s 元/小时)\n", utils.FormatPriceCentsIntoYuan(app.PriceInCents))
	}

	c.Printf("到期时间: %s\n", utils.FormatCommonTime(app.ExpireAt))
	c.Printf("创建时间: %s\n", utils.FormatCommonTime(app.CreatedAt))

	// 显示服务信息
	ctl.displayAppServices(c, app.Services)

	return nil, nil
}
func (ctl *Ctrl) getAppById(id int) (*models.App, error) {
	// 发送请求获取App详情
	url := fmt.Sprintf(consts.ApiUrlAppDetail, id)
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.App]{}).
		Get(ctl.config.EndPoint(url))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.App]{}).
			Get(ctl.config.EndPoint(url))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.App])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("app detail resp data code: %d", code).LogError()
	}

	return &result.Data, nil
}

func (ctl *Ctrl) appCreate(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	c.Println("创建新应用")
	c.Println("=" + strings.Repeat("=", 30))

	// 获取计费方式
	billingChoice := c.MultiChoice([]string{
		"按小时预付费",
		"动态计费",
	}, "选择计费方式")

	var (
		billingMethod  string
		durationAmount uint
	)

	switch billingChoice {
	case 0:
		billingMethod = models.BillingMethodPrepayByHours
		c.Printf("预付费小时数: ")
		durationStr := c.ReadLine()
		duration, err := strconv.ParseUint(durationStr, 10, 64)
		if err != nil {
			c.Printf("错误: 无效的小时数格式 '%s'\n", durationStr)
			return nil, nil
		}
		durationAmount = uint(duration)
	case 1:
		billingMethod = models.BillingMethodDynamicPay
		durationAmount = 0
	default:
		c.Println("错误: 无效的选择")
		return nil, nil
	}

	// 获取应用名称
	c.Printf("应用名称: ")
	name := strings.TrimSpace(c.ReadLine())
	if name == "" {
		c.Println("错误: 应用名称不能为空")
		return nil, nil
	}

	// 选择应用规格
	selectedSpec, err := ctl.selectAppSpec(c, billingMethod)
	if err != nil {
		return nil, err
	}

	if selectedSpec == nil {
		c.Println("已取消创建应用")
		return nil, nil
	}
	specId := int(selectedSpec.Id)

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// 构建请求数据
	createData := &service.CreateNewAppData{
		Name:           name,
		AppSpecId:      specId,
		BillingMethod:  billingMethod,
		DurationAmount: durationAmount,
	}

	// 发送创建请求
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.App]{}).
		SetBody(createData).
		Post(ctl.config.EndPoint(consts.ApiUrlAppNew))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if err := ctl.refresh(); err != nil {
			return nil, err.With("function: %s", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.App]{}).
			SetBody(createData).
			Post(ctl.config.EndPoint(consts.ApiUrlAppNew))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.App])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("app create resp data code: %d", code).LogError()
	}

	// 显示创建成功的应用信息
	app := result.Data
	c.Println()
	c.Println("应用创建成功! ID: ", app.Id)
	c.Println("名称: ", app.Name)
	c.Println("状态: ", app.State)
	c.Println()

	return nil, nil
}

func (ctl *Ctrl) appOperate(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查参数
	if len(c.Args) < 2 {
		c.Println("错误: 请提供应用ID和操作类型")
		c.Println("用法: app-operate <id> <action>")
		c.Println("支持的操作: start, start-with-simple-mode, stop, restart, release")
		return nil, nil
	}

	// 解析ID参数
	idStr := c.Args[0]
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Printf("错误: 无效的ID格式 '%s', 请提供数字ID\n", idStr)
		return nil, nil
	}

	app, err := ctl.getAppById(id)
	if err != nil {
		return nil, err
	}

	// 验证操作类型
	action := strings.ToLower(c.Args[1])
	validActions := []string{"start", "start-with-simple-mode", "stop", "restart", "release"}
	isValidAction := false
	for _, validAction := range validActions {
		if action == validAction {
			isValidAction = true
			break
		}
	}
	if !isValidAction {
		c.Printf("错误: 无效的操作类型 '%s'\n", action)
		c.Println("支持的操作: start, stop, restart, release")
		return nil, nil
	}

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	payload := ""
	if action == "start-with-simple-mode" {
		if !app.SupportSimpleMode {
			return nil, suanlink.ErrApiRequest.With("app does not support simple mode").LogError()
		}
		payload = `{"startOnSimpleMode": true}`
		action = "start"
	}

	// 构建请求数据
	operateData := &service.OperateAppData{
		Type:        action,
		AppId:       uint(id),
		PayloadJson: payload,
	}

	// 发送操作请求
	url := fmt.Sprintf(consts.ApiUrlAppOperate, id)
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.App]{}).
		SetBody(operateData).
		Post(ctl.config.EndPoint(url))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.App]{}).
			SetBody(operateData).
			Post(ctl.config.EndPoint(url))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.App])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("app operate resp data code: %d", code).LogError()
	}
	c.Printf("应用 %d 操作 '%s' 执行成功\n", id, action)

	return nil, nil
}

// selectAppSpec 让用户从应用规格列表中选择一个规格
func (ctl *Ctrl) selectAppSpec(c *ishell.Context, billingMethod string) (*models.AppSpec, error) {
	var (
		params = &pagination.QueryParams{
			Page:  1,
			Limit: 20,
			Columns: []*pagination.Column{
				{Name: "billingMethods", Value: billingMethod},
			},
		}
		total    uint
		allSpecs []*models.AppSpec
	)

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	c.Println()
	c.Println("请选择应用规格:")
	c.Println("=" + strings.Repeat("=", 40))

	for {
		table := tablewriter.NewWriter(os.Stdout)
		table.Header(AppSpecListSimpleFields)

		if params.Page == 0 {
			params.Page = 1
		}

		if total > 0 && params.Page > (int(total)/params.Limit)+1 {
			break
		}

		resp, err := ctl.rc().R().
			SetResult(&ApiResponse[cu.QueryResult[*models.AppSpec]]{}).
			SetBody(params).
			Post(ctl.config.EndPoint(consts.ApiUrlAppSpecList))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}

		switch code := resp.StatusCode(); code {
		case http.StatusOK:
		case http.StatusUnauthorized:
			if se := ctl.refresh(); se != nil {
				return nil, se.With("function", "refresh").LogError()
			}
			continue
		default:
			return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
		}

		result := resp.Result().(*ApiResponse[cu.QueryResult[*models.AppSpec]])
		if code := result.Code; code != suanlink.Success.Code() {
			return nil, suanlink.ErrApiRequest.With("app spec list resp data code: %d", code).LogError()
		}
		total = result.Data.Total
		allSpecs = result.Data.Records

		// 显示规格列表
		for _, r := range allSpecs {
			tagsStr := ""
			if len(r.Tags) > 0 {
				tagsStr = strings.Join(r.Tags, ", ")
			}

			table.Append([]string{
				fmt.Sprintf("%d", r.Id),
				fmt.Sprintf("%s × %d", r.GpuModel, r.GpuCards),
				fmt.Sprintf("%d", r.CpuCores),
				fmt.Sprintf("%.1f", float64(r.MemInMB)/1024),
				r.ImageName,
				fmt.Sprintf("%.2f", float64(r.Price)/100),
				utils.FormatBillingMethods(r.BillingMethods),
				utils.FormatSupport(r.SupportSimpleMode),
				tagsStr,
			})
		}
		table.Render()

		c.Printf("Total(%d), Page(%d), Limit(%d), (N)ext, (P)revious, [Q]uit\n", result.Data.Total, params.Page, params.Limit)
		c.Printf("输入ID选择规格或翻页查找所需规格: ")

		if total == 0 {
			c.Println("没有可用的应用规格")
			return nil, nil
		}

		input := strings.TrimSpace(c.ReadLine())
		switch strings.ToLower(input) {
		case "q", "quit":
			return nil, nil
		case "n", "next", "": // 空输入（直接回车）等同于下一页
			params.Page += 1
			continue
		case "p", "previous", "prev":
			if params.Page > 1 {
				params.Page -= 1
			}
			continue
		default:
			// 尝试解析为ID
			if specId, err := strconv.Atoi(input); err == nil {
				// 在当前页的规格中查找对应的ID
				for _, spec := range allSpecs {
					if int(spec.Id) == specId {
						c.Printf("已选择规格: %s (ID: %d)\n", spec.ImageName, spec.Id)
						return spec, nil
					}
				}
				c.Printf("在当前页中未找到ID '%d' 的规格，请翻页查找或输入正确的ID\n", specId)
				continue
			} else {
				c.Printf("无效的输入 '%s', 请输入规格ID、n(下一页)、p(上一页) 或 q(退出)\n", input)
				continue
			}
		}
	}

	return nil, nil
}

// getAppIDsForCompletion 获取用户应用ID列表用于自动补全
func (ctl *Ctrl) getAppIDsForCompletion() []string {
	// 检查用户登录状态（检查 RefreshToken 和 CellPhone）
	if ctl.config.RefreshToken == "" || ctl.config.CellPhone == "" {
		return []string{}
	}

	params := &pagination.QueryParams{
		Page:  1,
		Limit: 20, // 限制显示数量，避免输出过多
	}

	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[cu.QueryResult[*models.App]]{}).
		SetBody(params).
		Post(ctl.config.EndPoint(consts.ApiUrlAppList))
	if err != nil {
		return []string{}
	}

	// 处理认证失败的情况，尝试刷新 token
	if resp.StatusCode() == http.StatusUnauthorized {
		if se := ctl.refresh(); se != nil {
			return []string{}
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[cu.QueryResult[*models.App]]{}).
			SetBody(params).
			Post(ctl.config.EndPoint(consts.ApiUrlAppList))
		if err != nil {
			return []string{}
		}
	}

	if resp.StatusCode() != http.StatusOK {
		return []string{}
	}

	result := resp.Result().(*ApiResponse[cu.QueryResult[*models.App]])
	if result.Code != suanlink.Success.Code() {
		return []string{}
	}

	// 先打印应用列表信息，帮助用户选择
	if len(result.Data.Records) > 0 {
		fmt.Println("\n可用的应用列表:")

		table := tablewriter.NewWriter(os.Stdout)
		table.Header(AppCompletionListFields)

		for _, app := range result.Data.Records {
			table.Append([]string{
				fmt.Sprintf("%d", app.Id),
				app.Name,
				utils.FormatAppState(app.State),
				app.Area,
			})
		}

		table.Render()
		fmt.Println()
	}

	// 返回纯净的应用ID列表用于补全
	var completions []string
	for _, app := range result.Data.Records {
		appID := fmt.Sprintf("%d", app.Id)
		completions = append(completions, appID)
	}

	return completions
}

// getAppOperationsForCompletion 获取应用操作选项用于自动补全
func (ctl *Ctrl) getAppOperationsForCompletion(appIDStr string) []string {
	// 检查用户登录状态
	if ctl.config.RefreshToken == "" || ctl.config.CellPhone == "" {
		return []string{}
	}

	// 解析应用ID
	appID, err := strconv.ParseUint(appIDStr, 10, 64)
	if err != nil {
		return []string{}
	}

	// 获取应用详情
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.App]{}).
		Get(ctl.config.EndPoint(fmt.Sprintf(consts.ApiUrlAppDetail, appID)))
	if err != nil {
		return []string{}
	}

	// 处理认证失败的情况
	if resp.StatusCode() == http.StatusUnauthorized {
		if se := ctl.refresh(); se != nil {
			return []string{}
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.App]{}).
			Get(ctl.config.EndPoint(fmt.Sprintf(consts.ApiUrlAppDetail, appID)))
		if err != nil {
			return []string{}
		}
	}

	if resp.StatusCode() != http.StatusOK {
		return []string{}
	}

	result := resp.Result().(*ApiResponse[models.App])
	if result.Code != suanlink.Success.Code() {
		return []string{}
	}

	app := result.Data

	// 根据应用状态和计费方式确定可用操作
	availableOps := ctl.getAvailableOperations(&app)

	if len(availableOps) == 0 {
		fmt.Println("\n当前应用状态下没有可用的操作")
		return []string{}
	}

	// 显示可用的操作选项
	fmt.Printf("\n应用 %s (状态: %s, 计费方式: %s) 可用的操作:\n",
		app.Name, utils.FormatAppState(app.State),
		utils.FormatBillingMethod(app.BillingMethod),
	)

	table := tablewriter.NewWriter(os.Stdout)
	table.Header(AppOperationFields)

	var operations []string
	for _, op := range availableOps {
		desc := utils.FormatOperationDescription(op)
		table.Append([]string{op, desc})
		operations = append(operations, op)
	}
	table.Render()

	return operations
}

// getAvailableOperations 根据应用状态和计费方式确定可用操作
func (ctl *Ctrl) getAvailableOperations(app *models.App) []string {
	var operations []string

	// 根据计费方式确定基本可用操作
	if app.BillingMethod == models.BillingMethodDynamicPay {
		// 动态付费应用
		switch app.State {
		case models.AppStateRunning:
			operations = append(operations, "stop")
		case models.AppStateCreated:
			operations = append(operations, "start")
		case models.AppStateStopped:
			operations = append(operations, "start", "start-with-simple-mode", "release")
		}
	} else {
		// 预付费应用只能续费
		operations = append(operations, "renew")
	}

	return operations
}

// displayAppServices 显示应用的服务信息
func (ctl *Ctrl) displayAppServices(c *ishell.Context, services map[string]models.Service) {
	if len(services) == 0 {
		c.Println("\n服务信息: 暂无可用服务")
		return
	}

	c.Println("\n服务信息:")
	c.Println("-" + strings.Repeat("-", 58))

	// 创建服务信息表格
	table := tablewriter.NewWriter(os.Stdout)
	table.Header(AppServiceFields)

	// 按服务类型排序显示
	serviceTypes := []string{models.ServiceNameSsh, models.ServiceNameVnc, models.ServiceNameRdp}
	for _, serviceType := range serviceTypes {
		if svc, exists := services[serviceType]; exists {
			table.Append([]string{
				utils.FormatServiceTypeName(serviceType),
				svc.Ip,
				fmt.Sprintf("%d", svc.Port),
				svc.Username,
				svc.Password,
			})
		}
	}
	table.Render()

	// 显示连接提示
	c.Println("\n连接提示:")
	for serviceType, svc := range services {
		switch serviceType {
		case models.ServiceNameSsh:
			c.Printf("  SSH连接: ssh %s@%s -p %d\n", svc.Username, svc.Ip, svc.Port)
		case models.ServiceNameVnc:
			c.Printf("  VNC连接: %s:%d (密码: %s)\n", svc.Ip, svc.Port, svc.Password)
		case models.ServiceNameRdp:
			c.Printf("  RDP连接: %s:%d (用户: %s, 密码: %s)\n", svc.Ip, svc.Port, svc.Username, svc.Password)
		}
	}
}
