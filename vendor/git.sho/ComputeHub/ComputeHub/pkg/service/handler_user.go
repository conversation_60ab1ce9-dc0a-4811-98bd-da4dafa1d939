package service

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"

	"devgitlab.lianoid.com/jasonni/eaas-common/log"
	"devgitlab.lianoid.com/jasonni/eaas-common/pagination"
	hub "git.sho/ComputeHub/ComputeHub"
	"git.sho/ComputeHub/ComputeHub/pkg/models"
	"git.sho/ComputeHub/ComputeHub/utils"
)

func (s *ComputeHubService) HttpHandleRegisterUser(c *gin.Context) {}
func (s *ComputeHubService) HttpHandleAuthUser(c *gin.Context)     {}

type VerifyUserResp struct {
	OnlinePayment             bool  `json:"onlinePayment"`
	UserBalanceInCents        int64 `json:"userBalanceInCents"`
	UserOverdraftLimitInCents int64 `json:"userOverdraftLimitInCents"`
}

// TODO： remove this handler, use HttpHandleConfig instead
func (s *ComputeHubService) HttpHandleVerifyUser(c *gin.Context) {
	user := c.MustGet(utils.AuthKeyUser).(*models.User)

	platform := &models.Platform{}
	if err := s.db.Model(&models.Platform{}).Where("key = ?", user.PlatformKey).First(platform).Error; err != nil {
		utils.AbortReqOnErr(c, hub.QueryDatabaseErr.FromError(err))
		return
	}

	resp := &VerifyUserResp{
		OnlinePayment:             true,
		UserBalanceInCents:        user.BalanceInCents,
		UserOverdraftLimitInCents: user.OverdraftLimitInCents,
	}
	if platform.OnlinePayment != nil && !(*platform.OnlinePayment) {
		resp.OnlinePayment = false
	}
	utils.RespondDataToHttpClient(c, resp)
}

type DepositUserReq struct {
	Uid           uint  `json:"uid"` // models.User.Id
	AmountInCents int64 `json:"amountInCents"`
}

type DepositUserResp struct {
	CreatedOrder *models.Order `json:"createdOrder"`
	CodeURL      string        `json:"codeURL"`
}

func (s *ComputeHubService) HttpHandleDepositToUserAccount(c *gin.Context) {
	log.Infof("ready to deposit to user account")

	uid := c.MustGet(utils.AuthKeyUid).(uint)
	user := c.MustGet(utils.AuthKeyUser).(*models.User)

	req := &DepositUserReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		log.Error(err)
		utils.RespondErrorToHttpClient(c, hub.ParamsInvalidErr.FromError(err))
		return
	}
	if uid != user.Id {
		err := hub.PermissionDeniedErr.FromStr(fmt.Sprintf("user (id: %d) is not allowed to deposit to account of user (id: %d)",
			uid, req.Uid))
		log.Error(err)
		utils.RespondErrorToHttpClient(c, err)
		return
	}
	if req.AmountInCents <= 0 {
		err := hub.PermissionDeniedErr.FromStr(fmt.Sprintf("amount invalid, should larger than 0, got (%d)", req.AmountInCents))
		log.Error(err)
		utils.RespondErrorToHttpClient(c, err)
		return
	}

	if createdOrder, codeURL, cErr := s.createOrder(c, user, &models.GoodsDetail{
		Type:      models.GoodsTypeUserAccount,
		Request:   models.GoodsRequestUserDeposit,
		GoodsID:   int(user.Id),
		GoodsName: fmt.Sprintf("用户 (%s) 账户充值", user.Nickname),
		// GoodsParams: map[string]string{},
		Quantity:  1,
		UnitPrice: req.AmountInCents,
	}); cErr != nil {
		utils.RespondErrorToHttpClient(c, cErr)
		return
	} else {
		utils.RespondDataToHttpClient(c, &DepositUserResp{
			CreatedOrder: createdOrder,
			CodeURL:      codeURL,
		})
		return
	}
}

func (s *ComputeHubService) createOrder(
	c *gin.Context, user *models.User, goods *models.GoodsDetail,
) (*models.Order, string, *hub.ComputeHubError) {
	log.Tracef("goods: (%+v)", goods)

	platform := &models.Platform{}
	if err := s.db.Model(&models.Platform{}).Where("key = ?", user.PlatformKey).First(platform).Error; err != nil {
		log.Error(err)
		return nil, "", hub.QueryDatabaseErr.FromError(err)
	}

	if len(platform.PaymentMethods) == 0 {
		err := hub.CreateOrderErr.FromStr(fmt.Sprintf("platform (%s) does not support any payment method", user.PlatformKey))
		log.Error(err)
		return nil, "", err
	}

	order := models.Order{
		Description:   goods.GoodsName,
		CreatorID:     int(user.Id),
		PlatformKey:   user.PlatformKey,
		CreatorUser:   *user,
		Creator:       user.ToUserInfo(),
		PaymentMethod: platform.PaymentMethods[0], // TODO: support multiple payment methods
		GoodsDetail:   []models.GoodsDetail{*goods},
	}

	log.Tracef("ready to create order on wechat pay: (%+v)", order)

	if createdOrder, codeURL, err := s.paymentService.CreateOrder(c, &order, models.OrderNoPrefixUserDepositPayment); err != nil {
		log.Error(err)
		return nil, "", err
	} else {
		log.Tracef("order created OK (%+v), codeURL: (%s)", createdOrder, codeURL)
		return createdOrder, codeURL, nil
	}
}

func (s *ComputeHubService) processingCompletedOrder(orderNo string) {
	log.Infof("ready to process completed order (%s)", orderNo)

	order := &models.Order{}
	shouldRefund := false
	defer func() {
		if shouldRefund {
			log.Infof("ready to refund for order (no: %s)", orderNo)
			if err := s.refund(order); err != nil {
				log.Errorf("failed to refund for order (%+v), %v", order, err)
			}
		}
	}()

	if err := s.db.Model(&models.Order{}).Where("no = ?", orderNo).First(order).Error; err != nil {
		log.Errorf("failed to load order by order no (%s), %v", orderNo, err)
		// shouldRefund = true
		return
	} else {
		log.Tracef("order loaded: (%+v)", order)

		// TODO
		// assume order has only 1 goodsDetail
		goodsDetail := order.GoodsDetail[0]
		switch goodsDetail.Type {
		case models.GoodsTypeUserAccount:
			switch goodsDetail.Request {
			case models.GoodsRequestUserDeposit:
				if err := s.depositUserAccount(order); err != nil {
					log.Errorf("failed to deposit account for user (%d), %v", order.CreatorID, err)
					shouldRefund = true
					return
				}
			default:
				log.Errorf("unexpected error, unknown Goods.Request (%s)", goodsDetail.Request)
				shouldRefund = true
				return
			}
		default:
			log.Errorf("unexpected error, unknown GoodsType (%s)", goodsDetail.Type)
			shouldRefund = true
			return
		}
	}
}

func (s *ComputeHubService) depositUserAccount(order *models.Order) error {
	amountInCents := order.GoodsDetail[0].Quantity * order.GoodsDetail[0].UnitPrice

	tx := s.db.Begin()
	user := &models.User{}
	if err := tx.Model(&models.User{}).Where("id = ?", order.CreatorID).First(user).Error; err != nil {
		tx.Rollback()
		log.Errorf("failed to load user by id (%d), %v", order.CreatorID, err)
		return err
	}
	originalBalanceInCents := user.BalanceInCents

	if err := models.DoBilling(models.BillTypeUserAccountDeposit, &amountInCents, user, nil, &s.users, s.sysCfgs, tx); err != nil {
		log.Panicf("failed to do billing for order (%+v), %v", order, err)
	}

	order.Status = models.OrderStatusCompleted
	order.UpdatedAt = time.Now()
	if err := tx.Save(order).Error; err != nil {
		tx.Rollback()
		log.Panicf("update Order (%+v) failed, %v", order, err)
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		log.Panicf("failed to commit transaction for order (%+v), %v", order, err)
	}

	log.Infof("deposit account for user (%d) OK, original balance in cents (%d), update to (%d)",
		user.Id, originalBalanceInCents, user.BalanceInCents)

	return nil
}

func (s *ComputeHubService) refund(order *models.Order) error {
	goodsDetail := order.GoodsDetail[0]
	reason := "系统错误"
	switch goodsDetail.Type {
	case models.GoodsTypeUserAccount:
		switch goodsDetail.Request {
		case models.GoodsRequestUserDeposit:
			reason = "用户账户充值失败"
		}
	}

	refundOrder := models.RefundOrder{
		PaymentTradeNo: order.PaymentTradeNo,
		OrderNo:        order.No,
		PlatformKey:    order.PlatformKey,
		RefundAmount:   order.Amount,
		Total:          order.Amount,
		Reason:         reason,
		CreatorID:      order.CreatorID,
	}

	createdRefundOrder, err := s.paymentService.CreateRefundOrder(nil, &refundOrder, models.OrderNoPrefixUserDepositRefund)
	if err != nil {
		return err
	}
	log.Infof("created refund order: (%+v)", createdRefundOrder)

	return nil
}

// do nothing when deposit failed and refund ok
func (s *ComputeHubService) processingRefundedOrder(refundOrderNo string) {
	log.Infof("ready to process refunded order (%s)", refundOrderNo)

	refundOrder := &models.RefundOrder{}
	if err := s.db.Model(&models.RefundOrder{}).Where("no = ?", refundOrderNo).First(refundOrder).Error; err != nil {
		log.Panicf("failed to load refunded order by order no (%s), %v", refundOrderNo, err)
	}
}

func (s *ComputeHubService) HttpHandleGetUserById(c *gin.Context) {
	uid := c.MustGet(utils.AuthKeyUid).(uint)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		log.Error(err)
		utils.AbortReqOnErr(c, hub.ParamsInvalidErr)
		return
	}

	if !utils.IsUserRoleAdmin(c) {
		if uid != uint(id) {
			err := hub.PermissionDeniedErr.FromStr(fmt.Sprintf("user (id: %d) is not allowed to get user (id: %d)", uid, id))
			log.Error(err)
			utils.AbortReqOnErr(c, err)
			return
		}
	}

	user := &models.User{}
	if err := s.db.Model(&models.User{}).Where("id = ?", uid).First(user).Error; err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err := hub.UserNotFoundErr.FromStr(fmt.Sprintf("user (%d) is not found, %s", uid, err))
			log.Error(err)
			utils.AbortReqOnErr(c, err)
			return
		} else {
			err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("query user by id (%d) failed, %s", uid, err))
			log.Error(err)
			utils.AbortReqOnErr(c, err)
			return
		}
	}

	utils.RespondDataToHttpClient(c, user.ToUserInfo())
}

func (s *ComputeHubService) HttpHandleGetUserInfo(c *gin.Context) {
	uid := c.MustGet(utils.AuthKeyUid).(uint)

	user := &models.User{}
	if err := s.db.Model(&models.User{}).Where("id = ?", uid).First(user).Error; err != nil {
		err := hub.PermissionDeniedErr.FromStr(fmt.Sprintf("query user by id (%d) failed, will deny user, %s", uid, err))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}

	utils.RespondDataToHttpClient(c, user.ToUserInfo())
}

var userInfoToUserField = map[string]string{
	"id":                    "id",
	"name":                  "name",
	"nickname":              "nickname",
	"cellphone":             "cellphone",
	"email":                 "email",
	"department":            "department",
	"roleType":              "roleType",
	"uid":                   "platformUid",
	"identity":              "platformIdentity",
	"role":                  "platformRole",
	"balanceInCents":        "balanceInCents",
	"overdraftLimitInCents": "overdraftLimitInCents",
	"createdAt":             "createdAt",
	"updatedAt":             "updatedAt",
}

func (s *ComputeHubService) HttpHandleGetUsers(c *gin.Context) {
	log.Infof("ready to get user list")

	uid := c.MustGet(utils.AuthKeyUid).(uint)
	if !utils.IsUserRoleAdmin(c) {
		err := hub.PermissionDeniedErr.FromStr(fmt.Sprintf("user (%d) is not allowed to get user list", uid))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}

	pageParams := &pagination.QueryParams{}
	if err := c.ShouldBindJSON(pageParams); err != nil {
		err = fmt.Errorf("bind json data from request error: %+v", err)
		log.Error(err)
		utils.AbortReqOnErr(c, hub.ParamsInvalidErr.FromError(err))
		return
	}
	log.Debugf("pageParams: %+v", pageParams)

	// mapping field from models.UserInfo to models.User
	if pageParams.OrderBy != "" {
		if orderBy, ok := userInfoToUserField[pageParams.OrderBy]; !ok {
			err := fmt.Errorf("cannot mapping column name (%s) to field name in models.User", pageParams.OrderBy)
			log.Error(err)
			utils.AbortReqOnErr(c, hub.ParamsInvalidErr.FromError(err))
			return
		} else {
			pageParams.OrderBy = orderBy
		}
	}
	columns := []*pagination.Column{}
	for _, colInUserInfo := range pageParams.Columns {
		if colNameInUser, ok := userInfoToUserField[colInUserInfo.Name]; !ok {
			err := fmt.Errorf("cannot mapping column name (%s) to field name in models.User", colInUserInfo.Name)
			log.Error(err)
			utils.AbortReqOnErr(c, hub.ParamsInvalidErr.FromError(err))
			return
		} else {
			column := *colInUserInfo
			column.Name = colNameInUser
			columns = append(columns, &column)
		}
	}
	pageParams.Columns = columns

	if pageParams.Order == "" && pageParams.OrderBy == "" {
		pageParams.Order = pagination.SortOrderDesc
	}

	model := &pagination.Model{
		DefaultOrderBy: "id",
		ObjModel:       &models.User{},
		TimeFieldName:  "CreatedAt",
	}
	pageParams.SetModel(model)

	user := c.MustGet(utils.AuthKeyUser).(*models.User)
	db := s.db.Model(&models.User{}).Where("platform_key = ?", user.PlatformKey)

	users := []*models.User{}
	if err := pageParams.LoadRecords(db, &users); err != nil {
		err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("load records failed, %s", err))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}

	total := uint(0)
	if err := db.Count(&total).Error; err != nil {
		err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("load record count failed, %s", err))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}

	filteredTotal := uint(0)
	query := "count(id)"
	if err := pageParams.Select(db, query, &filteredTotal); err != nil {
		err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("load filtered record count failed, %s", err))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}

	userInfoList := []*models.UserInfo{}
	for _, u := range users {
		userInfoList = append(userInfoList, u.ToUserInfo())
	}

	utils.RespondDataToHttpClient(c, &utils.QueryResult[*models.UserInfo]{
		Records:       userInfoList,
		Total:         total,
		FilteredTotal: filteredTotal,
	})
}

type UpdateUserReq struct {
	Uid                       uint  `json:"uid"`
	UseVoucherFirstForPayment *bool `json:"useVoucherFirstForPayment"`
}

func (r *UpdateUserReq) Validate() error {
	if r.UseVoucherFirstForPayment == nil {
		return fmt.Errorf("no field need to be updated")
	}

	// add more validation rules

	return nil
}

func (s *ComputeHubService) HttpHandleUpdateUser(c *gin.Context) {
	uid := c.MustGet(utils.AuthKeyUid).(uint)
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		log.Error(err)
		utils.AbortReqOnErr(c, hub.ParamsInvalidErr)
		return
	}
	if !utils.IsUserRoleAdmin(c) {
		if uid != uint(id) {
			err := hub.PermissionDeniedErr.FromStr(fmt.Sprintf("user (id: %d) is not allowed to update user (id: %d)", uid, id))
			log.Error(err)
			utils.AbortReqOnErr(c, err)
			return
		}
	}

	req := &UpdateUserReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		log.Error(err)
		utils.RespondErrorToHttpClient(c, hub.ParamsInvalidErr.FromError(err))
		return
	}
	if err := req.Validate(); err != nil {
		log.Error(err)
		utils.RespondErrorToHttpClient(c, hub.ParamsInvalidErr.FromError(err))
		return
	}
	if req.Uid != uint(id) {
		err := fmt.Errorf("uid (%d) in URL not matched with uid (%d) in request body", id, req.Uid)
		log.Error(err)
		utils.RespondErrorToHttpClient(c, hub.ParamsInvalidErr.FromError(err))
		return
	}
	log.Infof("ready to update user (%d) with (%+v)", uid, req)

	user := &models.User{}
	if err := s.db.Model(&models.User{}).Where("id = ?", uid).First(user).Error; err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err := hub.UserNotFoundErr.FromStr(fmt.Sprintf("user (%d) is not found, %s", uid, err))
			log.Error(err)
			utils.RespondErrorToHttpClient(c, err)
			return
		}
	}

	updateMap := map[string]any{}
	if req.UseVoucherFirstForPayment != nil {
		updateMap["use_voucher_first_for_payment"] = *req.UseVoucherFirstForPayment
	}

	if len(updateMap) == 0 {
		log.Panicf("nothing need to be updated, should check UpdateUserReq.Validate")
	}

	now := time.Now()
	updateMap["updated_at"] = now
	if err := s.db.Model(&models.User{}).Where("id = ?", uid).Updates(updateMap).Error; err != nil {
		log.Error(err)
		utils.RespondErrorToHttpClient(c, hub.QueryDatabaseErr.FromError(err))
		return
	}

	user.UseVoucherFirstForPayment = *req.UseVoucherFirstForPayment
	user.UpdatedAt = now
	utils.RespondDataToHttpClient(c, user)
}
