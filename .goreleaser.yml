version: 2

project_name: suanlink

archives:
  - id: suanlink
    name_template: '{{ .ProjectName }}_{{ .Tag }}_{{ .Os }}_{{ .Arch }}_{{ .Now.Format "20060102150405" }}'
    files:
      - src: README.MD

builds:
  - id: suanlink
    main: ./cmd/suanlink/main.go
    targets:
      - darwin_arm64
      - darwin_amd64
      - linux_amd64
      - linux_arm64
      - windows_amd64
    binary: '{{ .ProjectName }}'
    command: build
    builder: go
    flags:
      - -trimpath
    ldflags:
      - -s -w -X main.version={{ .ShortCommit }} -X main.tag={{ .Tag }} -X main.buildDate={{ .Now.Format "20060102150405" }}
    env:
      - CGO_ENABLED=0
