### 流程图

#### 用户认证

```mermaid
sequenceDiagram
    participant User as 用户
    participant Web as hub-user-web
    participant <PERSON><PERSON> as CAS SSO
    participant AuthCtrl as compute-authctrl (本地)
    participant Comptehub as comptehub (公网)

        User->>+Web: 点击统一认证
        Web->>+Cas: redirect CAS login
        User->>Cas: login
        Cas-->>Web: redirect /auth/callback/sso?ticket=
        Web->>+AuthCtrl: GET /auth/callback/sso?ticket=
        Note over Cas: ...省略ticket验证...
        AuthCtrl->>+Comptehub: POST /api/v1/auth/users/sync
    alt exist
        Comptehub-->>AuthCtrl: user info
    else not exist
        Comptehub->>Comptehub: create user
        Comptehub-->>-AuthCtrl: user info
    end
        AuthCtrl->>AuthCtrl: create session
        AuthCtrl-->>-Web: redirect /
        Web-->>-User: 登录成功
```

#### 已认证用户访问api

```mermaid
sequenceDiagram
    participant User as 用户
    participant Web as hub-user-web
    participant Reverse as reverseproxy
    participant AuthCtrl as compute-authctrl (本地)
    participant Computehub as comptehub (公网)

        User->>+Web: 访问页面
        Web->>+Reverse: 发起api请求
        Reverse->>+AuthCtrl: GET /api/inner/auth/user
        AuthCtrl->>AuthCtrl: check cookie and get user from cache
        AuthCtrl-->>-Reverse: user info and sign
        Reverse->>+Computehub: proxy api with sign header
        Computehub-->>-Reverse: return data
        Reverse-->>-Web: return data
        Web-->>-User: 展示信息

```

#### 管理员用户认证 (Deprecated)

```mermaid
sequenceDiagram
    participant User as 用户
    participant Web as hub-user-web
    participant AuthCtrl as compute-authctrl (本地)
    participant Comptehub as comptehub (公网)

        User->>+Web: 账户密码登录
        Web->>+AuthCtrl: POST /auth/login
        AuthCtrl->>+Comptehub: POST /api/v1/auth/users/credentials
        Comptehub-->>-AuthCtrl: user info
        AuthCtrl->>AuthCtrl: create session
        AuthCtrl-->>-Web: redirect /
        Web-->>-User: 登录成功

```

#### 短信验证码登录及 JWT 授权流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant CLI as hub-user-cli
    participant Service as Computehub
    participant SMSP as 短信服务

    note right of CLI: 认证阶段的 CLI 请求需携带 x-cli-auth header

    User->>CLI: 请求获取验证码
    note right of CLI: POST /auth/captcha { cellphone } + x-cli-auth
    CLI->>Service: 转发请求
    Service-->>CLI: 返回 { token, content: captcha ASCII }

    User->>CLI: 输入 captcha 并发送短信验证码
    note right of CLI: POST /auth/sms-code { token, captcha, cellphone } + x-cli-auth
    CLI->>Service: 转发请求
    Service->>SMSP: SendMessage(cellphone, code)
    SMSP-->>Service: 返回发送结果
    Service-->>CLI: { sent: true }

    User->>CLI: 输入短信 code + machineId 登录
    note right of CLI: POST /auth/sms-login { token, code, machineId } + x-cli-auth
    CLI->>Service: 转发请求
    Service->>Service: ValidateSMSCode -> Issue JWT
    Service-->>CLI: 返回 { accessToken, refreshToken }

    CLI->>Service: 带 Bearer AccessToken 发起请求
    note right of Service: GET /api/v1/... + Authorization: Bearer <accessToken>
    Service->>Service: AuthJwtMiddleware.Parse(accessToken)
    Service-->>CLI: 验证通过或拒绝 (200/401)

    User->>CLI: 请求刷新 Access Token
    note right of CLI: POST /auth/refresh-token { refreshToken, machineId } + x-cli-auth
    CLI->>Service: 转发请求
    Service->>Service: ParseRefresh -> Validate machineId -> Issue new AccessToken
    Service-->>CLI: 返回 { accessToken }
```