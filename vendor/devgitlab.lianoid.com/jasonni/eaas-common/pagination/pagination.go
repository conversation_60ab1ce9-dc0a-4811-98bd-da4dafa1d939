package pagination

import (
	"database/sql"
	"fmt"
	"reflect"
	"time"

	db2 "devgitlab.lianoid.com/jasonni/eaas-common/db"
	"devgitlab.lianoid.com/jasonni/eaas-common/log"
	"devgitlab.lianoid.com/jasonni/eaas-common/utils"
	"github.com/jinzhu/gorm"
)

type SortOrder = string

const (
	SortOrderAsc  = "asc"
	SortOrderDesc = "desc"

	ColumnTypeTimeDuration = "timeDuration"
)

type Column struct {
	Name        string `json:"name"`
	Value       string `json:"value"`
	Regex       bool   `json:"regex"`
	Like        bool   `json:"like"`
	ICase       bool   `json:"iCase"`
	InvertMatch bool   `json:"invertMatch"`
	Type        string `json:"type"`
}

type QueryParams struct {
	Page    int       `json:"page"`
	Limit   int       `json:"limit"`
	Order   SortOrder `json:"order"`
	OrderBy string    `json:"orderBy"`
	Columns []*Column `json:"columns"`

	StartAt uint64 `json:"startAt"`
	EndAt   uint64 `json:"endAt"`

	Model *Model `json:"-"`
}

type Model struct {
	DefaultOrderBy string
	ObjModel       interface{}

	// TimeFieldName is not json name (createdAt) or column name (created_at) in db
	TimeFieldName string
}

func (params *QueryParams) SetModel(m *Model) {
	m.DefaultOrderBy = gorm.ToColumnName(m.DefaultOrderBy)
	params.Model = m
}

func (params *QueryParams) convertColumnNames() {
	params.OrderBy = gorm.ToColumnName(params.OrderBy)
	for _, col := range params.Columns {
		col.Name = gorm.ToColumnName(col.Name)
	}
}

func (params *QueryParams) Validate() error {
	if params.Model.DefaultOrderBy == "" {
		return fmt.Errorf("QueryParams.Model.DefaultOrderBy is required")
	}

	params.convertColumnNames()

	m := GetColumnsMap(params.Model.ObjModel)
	columnNameMap := map[string]bool{}
	for _, col := range params.Columns {
		if col.Name == "" {
			return fmt.Errorf("column name is required")
		}

		if _, ok := m[col.Name]; !ok {
			return fmt.Errorf("unknown column name (%s)", col.Name)
		}

		if _, ok := columnNameMap[col.Name]; ok {
			return fmt.Errorf("duplicated column name (%s)", col.Name)
		}
		columnNameMap[col.Name] = true

		if col.InvertMatch && col.Like {
			return fmt.Errorf("InvertMatch cannot be true when use LIKE operator")
		}
	}

	if (params.Order != SortOrderAsc) && (params.Order != SortOrderDesc) {
		params.Order = SortOrderAsc
	}
	if params.OrderBy == "" {
		params.OrderBy = params.Model.DefaultOrderBy
	}

	if params.StartAt < 0 || params.EndAt < 0 {
		return fmt.Errorf("invalid request: startAt(%d) / endAt(%d)", params.StartAt, params.EndAt)
	}
	if params.StartAt != 0 && params.EndAt != 0 && params.StartAt > params.EndAt {
		return fmt.Errorf("incorrect values for startAt (%d) / endAt(%d), endAt shouldn't less than startAt",
			params.StartAt, params.EndAt)
	}
	if _, ok := m[params.OrderBy]; !ok {
		return fmt.Errorf("unknown orderby column name (%s)", params.OrderBy)
	}

	return nil
}

func (params *QueryParams) processColumn(db *gorm.DB, col *Column) (*gorm.DB, error) {
	switch col.Type {
	case ColumnTypeTimeDuration:
		epochTime := IsEpochTimeField(utils.DBColumn2StructFieldName(col.Name), params.Model.ObjModel)
		var err error
		db, err = db2.SetTimePeriod(db, col.Value, col.Name, epochTime)
		if err != nil {
			log.Errorf("db2.SetTimePeriod err: %v", err)
			return nil, err
		}
	default:
		if col.Value != "" {
			matchOperator := "="
			if col.Regex {
				matchOperator = "~"
			}
			if col.Like {
				if col.ICase {
					matchOperator = "ILIKE"
				} else {
					matchOperator = "LIKE"
				}
				col.Value = fmt.Sprintf("%%%s%%", col.Value)
			} else if col.InvertMatch {
				matchOperator = fmt.Sprintf("!%s", matchOperator)
			}
			condition := fmt.Sprintf("%s %s ?", col.Name, matchOperator)
			db = db.Where(condition, col.Value)
		}
	}
	return db, nil
}

func (params *QueryParams) setConditions(db *gorm.DB) (*gorm.DB, error) {
	meta := params.Model

	for _, col := range params.Columns {
		var err error
		db, err = params.processColumn(db, col)
		if err != nil {
			log.Error(err)
			return nil, err
		}
	}

	if meta.TimeFieldName != "" {
		startAt := fmt.Sprintf("%d", params.StartAt)
		endAt := fmt.Sprintf("%d", params.EndAt)
		if !IsEpochTimeField(meta.TimeFieldName, meta.ObjModel) {
			startAt = time.Unix(int64(params.StartAt), 0).Format("2006-01-02 15:04:05")
			endAt = time.Unix(int64(params.EndAt), 0).Format("2006-01-02 15:04:05")
		}

		if params.StartAt > 0 {
			condition := fmt.Sprintf("%s >= ?", gorm.ToColumnName(meta.TimeFieldName))
			db = db.Where(condition, startAt)
		}
		if params.EndAt > 0 {
			condition := fmt.Sprintf("%s <= ?", gorm.ToColumnName(meta.TimeFieldName))
			db = db.Where(condition, endAt)
		}
	}
	return db, nil
}

func (params *QueryParams) LoadRecords(db *gorm.DB, out interface{}) error {
	var err error
	db, err = params.apply(db)
	if err != nil {
		log.Error(err)
		return err
	}
	return db.Find(out).Error
}

func (params *QueryParams) apply(db *gorm.DB) (*gorm.DB, error) {
	if err := params.Validate(); err != nil {
		log.Error(err)
		return nil, err
	}

	var err error
	db, err = params.setConditions(db)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	if params.OrderBy != "" {
		if params.OrderBy == params.Model.DefaultOrderBy {
			db = db.Order(fmt.Sprintf("%s %s", params.OrderBy, params.Order))
		} else {
			db = db.Order(fmt.Sprintf("%s %s, %s %s",
				params.OrderBy, params.Order, params.Model.DefaultOrderBy, SortOrderAsc))
		}
	}
	if params.Limit != 0 && params.Page != 0 {
		offset := (params.Page - 1) * params.Limit
		db = db.Limit(params.Limit).Offset(offset)
	}
	return db, nil
}

func (params *QueryParams) GetSqlRows(db *gorm.DB) (*sql.Rows, error) {
	var err error
	db, err = params.apply(db)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return db.Rows()
}

func (params *QueryParams) Select(db *gorm.DB, query string, out ...interface{}) error {
	if err := params.Validate(); err != nil {
		log.Error(err)
		return err
	}

	var err error
	db, err = params.setConditions(db)
	if err != nil {
		log.Error(err)
		return err
	}

	return db.Model(params.Model.ObjModel).
		Select(query).Row().Scan(out...)
}

func GetColumnsMap(obj interface{}) map[string]bool {
	typ := reflect.TypeOf(obj)
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	val := reflect.ValueOf(obj)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	m := map[string]bool{}
	for idx := 0; idx < typ.NumField(); idx++ {
		col := gorm.ToColumnName(typ.Field(idx).Name)
		m[col] = true
	}
	return m
}

// IsEpochTimeField : if the field type is time.Time, EpochTimeInDB should be false
func IsEpochTimeField(fieldName string, obj interface{}) bool {
	typ := reflect.TypeOf(obj)
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	field, ok := typ.FieldByName(fieldName)
	if !ok {
		log.Panicf(fmt.Sprintf("fieldName %s not found in model %+v", fieldName, obj))
	}
	return field.Type.Name() != "Time"
}
