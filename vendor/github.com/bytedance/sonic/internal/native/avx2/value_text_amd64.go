// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_value = []byte{
	// .p2align 5, 0x00
	// LCPI0_0
	0x20, // .byte 32
	0x00, //0x00000001 .byte 0
	0x00, //0x00000002 .byte 0
	0x00, //0x00000003 .byte 0
	0x00, //0x00000004 .byte 0
	0x00, //0x00000005 .byte 0
	0x00, //0x00000006 .byte 0
	0x00, //0x00000007 .byte 0
	0x00, //0x00000008 .byte 0
	0x09, //0x00000009 .byte 9
	0x0a, //0x0000000a .byte 10
	0x00, //0x0000000b .byte 0
	0x00, //0x0000000c .byte 0
	0x0d, //0x0000000d .byte 13
	0x00, //0x0000000e .byte 0
	0x00, //0x0000000f .byte 0
	0x20, //0x00000010 .byte 32
	0x00, //0x00000011 .byte 0
	0x00, //0x00000012 .byte 0
	0x00, //0x00000013 .byte 0
	0x00, //0x00000014 .byte 0
	0x00, //0x00000015 .byte 0
	0x00, //0x00000016 .byte 0
	0x00, //0x00000017 .byte 0
	0x00, //0x00000018 .byte 0
	0x09, //0x00000019 .byte 9
	0x0a, //0x0000001a .byte 10
	0x00, //0x0000001b .byte 0
	0x00, //0x0000001c .byte 0
	0x0d, //0x0000001d .byte 13
	0x00, //0x0000001e .byte 0
	0x00, //0x0000001f .byte 0
	//0x00000020 LCPI0_1
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000020 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_2
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000050 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000060 LCPI0_3
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000060 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, //0x00000070 QUAD $0x2020202020202020; QUAD $0x2020202020202020  // .space 16, '                '
	//0x00000080 LCPI0_4
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000080 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000090 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x000000a0 LCPI0_5
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000a0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x000000b0 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000000c0 LCPI0_6
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000c0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000000d0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000000e0 LCPI0_7
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000e0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000000f0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x00000100 LCPI0_8
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000100 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000110 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000120 LCPI0_9
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000120 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x00000130 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x00000140 LCPI0_10
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000140 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x00000150 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x00000160 LCPI0_22
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000160 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000168 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000170 .quad 1
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000178 .quad 1
	//0x00000180 .p2align 4, 0x00
	//0x00000180 LCPI0_11
	0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, 0x2f, //0x00000180 QUAD $0x2f2f2f2f2f2f2f2f; QUAD $0x2f2f2f2f2f2f2f2f  // .space 16, '////////////////'
	//0x00000190 LCPI0_12
	0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, 0x3a, //0x00000190 QUAD $0x3a3a3a3a3a3a3a3a; QUAD $0x3a3a3a3a3a3a3a3a  // .space 16, '::::::::::::::::'
	//0x000001a0 LCPI0_13
	0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, 0x2b, //0x000001a0 QUAD $0x2b2b2b2b2b2b2b2b; QUAD $0x2b2b2b2b2b2b2b2b  // .space 16, '++++++++++++++++'
	//0x000001b0 LCPI0_14
	0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, 0x2d, //0x000001b0 QUAD $0x2d2d2d2d2d2d2d2d; QUAD $0x2d2d2d2d2d2d2d2d  // .space 16, '----------------'
	//0x000001c0 LCPI0_15
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x000001c0 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x000001d0 LCPI0_16
	0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, //0x000001d0 QUAD $0x2e2e2e2e2e2e2e2e; QUAD $0x2e2e2e2e2e2e2e2e  // .space 16, '................'
	//0x000001e0 LCPI0_17
	0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, 0x45, //0x000001e0 QUAD $0x4545454545454545; QUAD $0x4545454545454545  // .space 16, 'EEEEEEEEEEEEEEEE'
	//0x000001f0 LCPI0_18
	0x00, 0x00, 0x30, 0x43, //0x000001f0 .long 1127219200
	0x00, 0x00, 0x30, 0x45, //0x000001f4 .long 1160773632
	0x00, 0x00, 0x00, 0x00, //0x000001f8 .long 0
	0x00, 0x00, 0x00, 0x00, //0x000001fc .long 0
	//0x00000200 LCPI0_19
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x43, //0x00000200 .quad 0x4330000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x45, //0x00000208 .quad 0x4530000000000000
	//0x00000210 .p2align 3, 0x00
	//0x00000210 LCPI0_20
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x00000210 .quad 0x430c6bf526340000
	//0x00000218 LCPI0_21
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0xc3, //0x00000218 .quad 0xc30c6bf526340000
	//0x00000220 LCPI0_23
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000220 .quad 1
	//0x00000228 LCPI0_24
	0x10, 0x27, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000228 .quad 10000
	//0x00000230 LCPI0_25
	0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00000230 .quad 10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000238 .p2align 4, 0x90
	//0x00000240 _value
	0x55, //0x00000240 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000241 movq         %rsp, %rbp
	0x41, 0x57, //0x00000244 pushq        %r15
	0x41, 0x56, //0x00000246 pushq        %r14
	0x41, 0x55, //0x00000248 pushq        %r13
	0x41, 0x54, //0x0000024a pushq        %r12
	0x53, //0x0000024c pushq        %rbx
	0x48, 0x83, 0xec, 0x50, //0x0000024d subq         $80, %rsp
	0x49, 0x89, 0xcd, //0x00000251 movq         %rcx, %r13
	0x49, 0x89, 0xf6, //0x00000254 movq         %rsi, %r14
	0x48, 0x39, 0xf2, //0x00000257 cmpq         %rsi, %rdx
	0x0f, 0x83, 0x27, 0x00, 0x00, 0x00, //0x0000025a jae          LBB0_5
	0x8a, 0x04, 0x17, //0x00000260 movb         (%rdi,%rdx), %al
	0x3c, 0x0d, //0x00000263 cmpb         $13, %al
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00000265 je           LBB0_5
	0x3c, 0x20, //0x0000026b cmpb         $32, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000026d je           LBB0_5
	0x8d, 0x48, 0xf7, //0x00000273 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x00000276 cmpb         $1, %cl
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x00000279 jbe          LBB0_5
	0x49, 0x89, 0xd1, //0x0000027f movq         %rdx, %r9
	0xe9, 0x68, 0x01, 0x00, 0x00, //0x00000282 jmp          LBB0_32
	//0x00000287 LBB0_5
	0x4c, 0x8d, 0x4a, 0x01, //0x00000287 leaq         $1(%rdx), %r9
	0x4d, 0x39, 0xf1, //0x0000028b cmpq         %r14, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x0000028e jae          LBB0_9
	0x42, 0x8a, 0x04, 0x0f, //0x00000294 movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x00000298 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x0000029a je           LBB0_9
	0x3c, 0x20, //0x000002a0 cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002a2 je           LBB0_9
	0x8d, 0x48, 0xf7, //0x000002a8 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x000002ab cmpb         $1, %cl
	0x0f, 0x87, 0x3b, 0x01, 0x00, 0x00, //0x000002ae ja           LBB0_32
	//0x000002b4 LBB0_9
	0x4c, 0x8d, 0x4a, 0x02, //0x000002b4 leaq         $2(%rdx), %r9
	0x4d, 0x39, 0xf1, //0x000002b8 cmpq         %r14, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x000002bb jae          LBB0_13
	0x42, 0x8a, 0x04, 0x0f, //0x000002c1 movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x000002c5 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000002c7 je           LBB0_13
	0x3c, 0x20, //0x000002cd cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002cf je           LBB0_13
	0x8d, 0x48, 0xf7, //0x000002d5 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x000002d8 cmpb         $1, %cl
	0x0f, 0x87, 0x0e, 0x01, 0x00, 0x00, //0x000002db ja           LBB0_32
	//0x000002e1 LBB0_13
	0x4c, 0x8d, 0x4a, 0x03, //0x000002e1 leaq         $3(%rdx), %r9
	0x4d, 0x39, 0xf1, //0x000002e5 cmpq         %r14, %r9
	0x0f, 0x83, 0x20, 0x00, 0x00, 0x00, //0x000002e8 jae          LBB0_17
	0x42, 0x8a, 0x04, 0x0f, //0x000002ee movb         (%rdi,%r9), %al
	0x3c, 0x0d, //0x000002f2 cmpb         $13, %al
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000002f4 je           LBB0_17
	0x3c, 0x20, //0x000002fa cmpb         $32, %al
	0x0f, 0x84, 0x0c, 0x00, 0x00, 0x00, //0x000002fc je           LBB0_17
	0x8d, 0x48, 0xf7, //0x00000302 leal         $-9(%rax), %ecx
	0x80, 0xf9, 0x01, //0x00000305 cmpb         $1, %cl
	0x0f, 0x87, 0xe1, 0x00, 0x00, 0x00, //0x00000308 ja           LBB0_32
	//0x0000030e LBB0_17
	0x4c, 0x8d, 0x4a, 0x04, //0x0000030e leaq         $4(%rdx), %r9
	0x4d, 0x39, 0xf1, //0x00000312 cmpq         %r14, %r9
	0x0f, 0x83, 0xb6, 0x00, 0x00, 0x00, //0x00000315 jae          LBB0_29
	0x4c, 0x89, 0xf0, //0x0000031b movq         %r14, %rax
	0x4c, 0x29, 0xc8, //0x0000031e subq         %r9, %rax
	0x48, 0x83, 0xf8, 0x20, //0x00000321 cmpq         $32, %rax
	0x0f, 0x82, 0x08, 0x15, 0x00, 0x00, //0x00000325 jb           LBB0_321
	0x48, 0xc7, 0xc0, 0xfc, 0xff, 0xff, 0xff, //0x0000032b movq         $-4, %rax
	0x48, 0x29, 0xd0, //0x00000332 subq         %rdx, %rax
	0xc5, 0xfe, 0x6f, 0x05, 0xc3, 0xfc, 0xff, 0xff, //0x00000335 vmovdqu      $-829(%rip), %ymm0  /* LCPI0_0+0(%rip) */
	0x90, 0x90, 0x90, //0x0000033d .p2align 4, 0x90
	//0x00000340 LBB0_20
	0xc4, 0xa1, 0x7e, 0x6f, 0x0c, 0x0f, //0x00000340 vmovdqu      (%rdi,%r9), %ymm1
	0xc4, 0xe2, 0x7d, 0x00, 0xd1, //0x00000346 vpshufb      %ymm1, %ymm0, %ymm2
	0xc5, 0xf5, 0x74, 0xca, //0x0000034b vpcmpeqb     %ymm2, %ymm1, %ymm1
	0xc5, 0xfd, 0xd7, 0xc9, //0x0000034f vpmovmskb    %ymm1, %ecx
	0x83, 0xf9, 0xff, //0x00000353 cmpl         $-1, %ecx
	0x0f, 0x85, 0x7d, 0x00, 0x00, 0x00, //0x00000356 jne          LBB0_30
	0x49, 0x83, 0xc1, 0x20, //0x0000035c addq         $32, %r9
	0x49, 0x8d, 0x0c, 0x06, //0x00000360 leaq         (%r14,%rax), %rcx
	0x48, 0x83, 0xc1, 0xe0, //0x00000364 addq         $-32, %rcx
	0x48, 0x83, 0xc0, 0xe0, //0x00000368 addq         $-32, %rax
	0x48, 0x83, 0xf9, 0x1f, //0x0000036c cmpq         $31, %rcx
	0x0f, 0x87, 0xca, 0xff, 0xff, 0xff, //0x00000370 ja           LBB0_20
	0x49, 0x89, 0xf9, //0x00000376 movq         %rdi, %r9
	0x49, 0x29, 0xc1, //0x00000379 subq         %rax, %r9
	0x4c, 0x01, 0xf0, //0x0000037c addq         %r14, %rax
	0x48, 0x85, 0xc0, //0x0000037f testq        %rax, %rax
	0x0f, 0x84, 0x38, 0x00, 0x00, 0x00, //0x00000382 je           LBB0_28
	//0x00000388 LBB0_23
	0x4d, 0x8d, 0x14, 0x01, //0x00000388 leaq         (%r9,%rax), %r10
	0x31, 0xc9, //0x0000038c xorl         %ecx, %ecx
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x0000038e movabsq      $4294977024, %rbx
	//0x00000398 LBB0_24
	0x41, 0x0f, 0xbe, 0x34, 0x09, //0x00000398 movsbl       (%r9,%rcx), %esi
	0x83, 0xfe, 0x20, //0x0000039d cmpl         $32, %esi
	0x0f, 0x87, 0x48, 0x0f, 0x00, 0x00, //0x000003a0 ja           LBB0_260
	0x48, 0x0f, 0xa3, 0xf3, //0x000003a6 btq          %rsi, %rbx
	0x0f, 0x83, 0x3e, 0x0f, 0x00, 0x00, //0x000003aa jae          LBB0_260
	0x48, 0x83, 0xc1, 0x01, //0x000003b0 addq         $1, %rcx
	0x48, 0x39, 0xc8, //0x000003b4 cmpq         %rcx, %rax
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x000003b7 jne          LBB0_24
	0x4d, 0x89, 0xd1, //0x000003bd movq         %r10, %r9
	//0x000003c0 LBB0_28
	0x49, 0x29, 0xf9, //0x000003c0 subq         %rdi, %r9
	0x4d, 0x39, 0xf1, //0x000003c3 cmpq         %r14, %r9
	0x0f, 0x82, 0x1f, 0x00, 0x00, 0x00, //0x000003c6 jb           LBB0_31
	0xe9, 0x2c, 0x0f, 0x00, 0x00, //0x000003cc jmp          LBB0_261
	//0x000003d1 LBB0_29
	0x4c, 0x89, 0xca, //0x000003d1 movq         %r9, %rdx
	0xe9, 0x24, 0x0f, 0x00, 0x00, //0x000003d4 jmp          LBB0_261
	//0x000003d9 LBB0_30
	0xf7, 0xd1, //0x000003d9 notl         %ecx
	0x44, 0x0f, 0xbc, 0xc9, //0x000003db bsfl         %ecx, %r9d
	0x49, 0x29, 0xc1, //0x000003df subq         %rax, %r9
	0x4d, 0x39, 0xf1, //0x000003e2 cmpq         %r14, %r9
	0x0f, 0x83, 0x12, 0x0f, 0x00, 0x00, //0x000003e5 jae          LBB0_261
	//0x000003eb LBB0_31
	0x42, 0x8a, 0x04, 0x0f, //0x000003eb movb         (%rdi,%r9), %al
	//0x000003ef LBB0_32
	0x0f, 0xbe, 0xc8, //0x000003ef movsbl       %al, %ecx
	0x83, 0xf9, 0x7d, //0x000003f2 cmpl         $125, %ecx
	0x0f, 0x87, 0x1f, 0x07, 0x00, 0x00, //0x000003f5 ja           LBB0_131
	0x49, 0x8d, 0x51, 0x01, //0x000003fb leaq         $1(%r9), %rdx
	0x4e, 0x8d, 0x3c, 0x0f, //0x000003ff leaq         (%rdi,%r9), %r15
	0x48, 0x8d, 0x35, 0xea, 0x2e, 0x00, 0x00, //0x00000403 leaq         $12010(%rip), %rsi  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x0c, 0x8e, //0x0000040a movslq       (%rsi,%rcx,4), %rcx
	0x48, 0x01, 0xf1, //0x0000040e addq         %rsi, %rcx
	0x48, 0x89, 0x7d, 0xc8, //0x00000411 movq         %rdi, $-56(%rbp)
	0xff, 0xe1, //0x00000415 jmpq         *%rcx
	//0x00000417 LBB0_34
	0x41, 0xf6, 0xc0, 0x02, //0x00000417 testb        $2, %r8b
	0x0f, 0x85, 0x73, 0x00, 0x00, 0x00, //0x0000041b jne          LBB0_41
	0x4d, 0x8b, 0x45, 0x20, //0x00000421 movq         $32(%r13), %r8
	0x49, 0x8b, 0x45, 0x28, //0x00000425 movq         $40(%r13), %rax
	0x48, 0x89, 0x45, 0xc0, //0x00000429 movq         %rax, $-64(%rbp)
	0x49, 0xc7, 0x45, 0x00, 0x09, 0x00, 0x00, 0x00, //0x0000042d movq         $9, (%r13)
	0xc5, 0xf9, 0xef, 0xc0, //0x00000435 vpxor        %xmm0, %xmm0, %xmm0
	0xc4, 0xc1, 0x7a, 0x7f, 0x45, 0x08, //0x00000439 vmovdqu      %xmm0, $8(%r13)
	0x4d, 0x89, 0x4d, 0x18, //0x0000043f movq         %r9, $24(%r13)
	0x4d, 0x39, 0xf1, //0x00000443 cmpq         %r14, %r9
	0x0f, 0x83, 0xcc, 0x0c, 0x00, 0x00, //0x00000446 jae          LBB0_232
	0x41, 0x8a, 0x37, //0x0000044c movb         (%r15), %sil
	0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, //0x0000044f movl         $1, %r12d
	0x89, 0xf3, //0x00000455 movl         %esi, %ebx
	0x4c, 0x89, 0xc9, //0x00000457 movq         %r9, %rcx
	0x40, 0x80, 0xfe, 0x2d, //0x0000045a cmpb         $45, %sil
	0x0f, 0x85, 0x15, 0x00, 0x00, 0x00, //0x0000045e jne          LBB0_39
	0x4c, 0x39, 0xf2, //0x00000464 cmpq         %r14, %rdx
	0x0f, 0x83, 0xab, 0x0c, 0x00, 0x00, //0x00000467 jae          LBB0_232
	0x8a, 0x1c, 0x17, //0x0000046d movb         (%rdi,%rdx), %bl
	0x41, 0xbc, 0xff, 0xff, 0xff, 0xff, //0x00000470 movl         $-1, %r12d
	0x48, 0x89, 0xd1, //0x00000476 movq         %rdx, %rcx
	//0x00000479 LBB0_39
	0x8d, 0x43, 0xd0, //0x00000479 leal         $-48(%rbx), %eax
	0x3c, 0x0a, //0x0000047c cmpb         $10, %al
	0x0f, 0x82, 0x1c, 0x04, 0x00, 0x00, //0x0000047e jb           LBB0_98
	//0x00000484 LBB0_40
	0x49, 0xc7, 0x45, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x00000484 movq         $-2, (%r13)
	0x49, 0x89, 0xcb, //0x0000048c movq         %rcx, %r11
	0xe9, 0x74, 0x0e, 0x00, 0x00, //0x0000048f jmp          LBB0_263
	//0x00000494 LBB0_41
	0x4d, 0x29, 0xce, //0x00000494 subq         %r9, %r14
	0x31, 0xf6, //0x00000497 xorl         %esi, %esi
	0x3c, 0x2d, //0x00000499 cmpb         $45, %al
	0x40, 0x0f, 0x94, 0xc6, //0x0000049b sete         %sil
	0x4d, 0x8d, 0x1c, 0x37, //0x0000049f leaq         (%r15,%rsi), %r11
	0x49, 0x29, 0xf6, //0x000004a3 subq         %rsi, %r14
	0x0f, 0x84, 0xd9, 0x23, 0x00, 0x00, //0x000004a6 je           LBB0_563
	0x41, 0x8a, 0x03, //0x000004ac movb         (%r11), %al
	0x8d, 0x48, 0xd0, //0x000004af leal         $-48(%rax), %ecx
	0x80, 0xf9, 0x09, //0x000004b2 cmpb         $9, %cl
	0x0f, 0x87, 0xf2, 0x07, 0x00, 0x00, //0x000004b5 ja           LBB0_161
	0x3c, 0x30, //0x000004bb cmpb         $48, %al
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x000004bd jne          LBB0_47
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x000004c3 movl         $1, %eax
	0x49, 0x83, 0xfe, 0x01, //0x000004c8 cmpq         $1, %r14
	0x0f, 0x84, 0x9d, 0x07, 0x00, 0x00, //0x000004cc je           LBB0_157
	0x41, 0x8a, 0x4b, 0x01, //0x000004d2 movb         $1(%r11), %cl
	0x80, 0xc1, 0xd2, //0x000004d6 addb         $-46, %cl
	0x80, 0xf9, 0x37, //0x000004d9 cmpb         $55, %cl
	0x0f, 0x87, 0x8d, 0x07, 0x00, 0x00, //0x000004dc ja           LBB0_157
	0x0f, 0xb6, 0xc9, //0x000004e2 movzbl       %cl, %ecx
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000004e5 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xca, //0x000004ef btq          %rcx, %rdx
	0x0f, 0x83, 0x76, 0x07, 0x00, 0x00, //0x000004f3 jae          LBB0_157
	//0x000004f9 LBB0_47
	0x4c, 0x89, 0x7d, 0xb8, //0x000004f9 movq         %r15, $-72(%rbp)
	0x49, 0x83, 0xfe, 0x20, //0x000004fd cmpq         $32, %r14
	0x48, 0x89, 0x75, 0xc0, //0x00000501 movq         %rsi, $-64(%rbp)
	0x49, 0xc7, 0xc4, 0xff, 0xff, 0xff, 0xff, //0x00000505 movq         $-1, %r12
	0x0f, 0x82, 0xa1, 0x23, 0x00, 0x00, //0x0000050c jb           LBB0_566
	0x31, 0xc0, //0x00000512 xorl         %eax, %eax
	0xc5, 0xfe, 0x6f, 0x05, 0x64, 0xfb, 0xff, 0xff, //0x00000514 vmovdqu      $-1180(%rip), %ymm0  /* LCPI0_4+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x7c, 0xfb, 0xff, 0xff, //0x0000051c vmovdqu      $-1156(%rip), %ymm1  /* LCPI0_5+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x94, 0xfb, 0xff, 0xff, //0x00000524 vmovdqu      $-1132(%rip), %ymm2  /* LCPI0_6+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x1d, 0xac, 0xfb, 0xff, 0xff, //0x0000052c vmovdqu      $-1108(%rip), %ymm3  /* LCPI0_7+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x25, 0xc4, 0xfb, 0xff, 0xff, //0x00000534 vmovdqu      $-1084(%rip), %ymm4  /* LCPI0_8+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x2d, 0xdc, 0xfb, 0xff, 0xff, //0x0000053c vmovdqu      $-1060(%rip), %ymm5  /* LCPI0_9+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x35, 0xf4, 0xfb, 0xff, 0xff, //0x00000544 vmovdqu      $-1036(%rip), %ymm6  /* LCPI0_10+0(%rip) */
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x0000054c movq         $-1, %r15
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00000553 movq         $-1, %r10
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x0000055a .p2align 4, 0x90
	//0x00000560 LBB0_49
	0xc4, 0xc1, 0x7e, 0x6f, 0x3c, 0x03, //0x00000560 vmovdqu      (%r11,%rax), %ymm7
	0xc5, 0x45, 0x64, 0xc0, //0x00000566 vpcmpgtb     %ymm0, %ymm7, %ymm8
	0xc5, 0x75, 0x64, 0xcf, //0x0000056a vpcmpgtb     %ymm7, %ymm1, %ymm9
	0xc4, 0x41, 0x35, 0xdb, 0xc0, //0x0000056e vpand        %ymm8, %ymm9, %ymm8
	0xc5, 0x45, 0x74, 0xca, //0x00000573 vpcmpeqb     %ymm2, %ymm7, %ymm9
	0xc5, 0x45, 0x74, 0xd3, //0x00000577 vpcmpeqb     %ymm3, %ymm7, %ymm10
	0xc4, 0x41, 0x2d, 0xeb, 0xc9, //0x0000057b vpor         %ymm9, %ymm10, %ymm9
	0xc5, 0x45, 0xdb, 0xd4, //0x00000580 vpand        %ymm4, %ymm7, %ymm10
	0xc5, 0x2d, 0x74, 0xd6, //0x00000584 vpcmpeqb     %ymm6, %ymm10, %ymm10
	0xc5, 0xc5, 0x74, 0xfd, //0x00000588 vpcmpeqb     %ymm5, %ymm7, %ymm7
	0xc5, 0xfd, 0xd7, 0xf7, //0x0000058c vpmovmskb    %ymm7, %esi
	0xc4, 0xc1, 0x7d, 0xd7, 0xd2, //0x00000590 vpmovmskb    %ymm10, %edx
	0xc4, 0xc1, 0x7d, 0xd7, 0xd9, //0x00000595 vpmovmskb    %ymm9, %ebx
	0xc5, 0xbd, 0xeb, 0xff, //0x0000059a vpor         %ymm7, %ymm8, %ymm7
	0xc4, 0x41, 0x2d, 0xeb, 0xc1, //0x0000059e vpor         %ymm9, %ymm10, %ymm8
	0xc5, 0xbd, 0xeb, 0xff, //0x000005a3 vpor         %ymm7, %ymm8, %ymm7
	0xc5, 0xfd, 0xd7, 0xcf, //0x000005a7 vpmovmskb    %ymm7, %ecx
	0x48, 0xf7, 0xd1, //0x000005ab notq         %rcx
	0x4c, 0x0f, 0xbc, 0xc1, //0x000005ae bsfq         %rcx, %r8
	0x41, 0x83, 0xf8, 0x20, //0x000005b2 cmpl         $32, %r8d
	0x0f, 0x84, 0x14, 0x00, 0x00, 0x00, //0x000005b6 je           LBB0_51
	0xbf, 0xff, 0xff, 0xff, 0xff, //0x000005bc movl         $-1, %edi
	0x44, 0x89, 0xc1, //0x000005c1 movl         %r8d, %ecx
	0xd3, 0xe7, //0x000005c4 shll         %cl, %edi
	0xf7, 0xd7, //0x000005c6 notl         %edi
	0x21, 0xfe, //0x000005c8 andl         %edi, %esi
	0x21, 0xfa, //0x000005ca andl         %edi, %edx
	0x21, 0xdf, //0x000005cc andl         %ebx, %edi
	0x89, 0xfb, //0x000005ce movl         %edi, %ebx
	//0x000005d0 LBB0_51
	0x8d, 0x4e, 0xff, //0x000005d0 leal         $-1(%rsi), %ecx
	0x21, 0xf1, //0x000005d3 andl         %esi, %ecx
	0x0f, 0x85, 0xda, 0x09, 0x00, 0x00, //0x000005d5 jne          LBB0_203
	0x8d, 0x4a, 0xff, //0x000005db leal         $-1(%rdx), %ecx
	0x21, 0xd1, //0x000005de andl         %edx, %ecx
	0x0f, 0x85, 0xcf, 0x09, 0x00, 0x00, //0x000005e0 jne          LBB0_203
	0x8d, 0x4b, 0xff, //0x000005e6 leal         $-1(%rbx), %ecx
	0x21, 0xd9, //0x000005e9 andl         %ebx, %ecx
	0x0f, 0x85, 0xc4, 0x09, 0x00, 0x00, //0x000005eb jne          LBB0_203
	0x85, 0xf6, //0x000005f1 testl        %esi, %esi
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x000005f3 je           LBB0_57
	0x0f, 0xbc, 0xce, //0x000005f9 bsfl         %esi, %ecx
	0x49, 0x83, 0xfa, 0xff, //0x000005fc cmpq         $-1, %r10
	0x0f, 0x85, 0x8a, 0x0c, 0x00, 0x00, //0x00000600 jne          LBB0_256
	0x48, 0x01, 0xc1, //0x00000606 addq         %rax, %rcx
	0x49, 0x89, 0xca, //0x00000609 movq         %rcx, %r10
	//0x0000060c LBB0_57
	0x85, 0xd2, //0x0000060c testl        %edx, %edx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x0000060e je           LBB0_60
	0x0f, 0xbc, 0xca, //0x00000614 bsfl         %edx, %ecx
	0x49, 0x83, 0xff, 0xff, //0x00000617 cmpq         $-1, %r15
	0x0f, 0x85, 0x6f, 0x0c, 0x00, 0x00, //0x0000061b jne          LBB0_256
	0x48, 0x01, 0xc1, //0x00000621 addq         %rax, %rcx
	0x49, 0x89, 0xcf, //0x00000624 movq         %rcx, %r15
	//0x00000627 LBB0_60
	0x85, 0xdb, //0x00000627 testl        %ebx, %ebx
	0x0f, 0x84, 0x13, 0x00, 0x00, 0x00, //0x00000629 je           LBB0_63
	0x0f, 0xbc, 0xcb, //0x0000062f bsfl         %ebx, %ecx
	0x49, 0x83, 0xfc, 0xff, //0x00000632 cmpq         $-1, %r12
	0x0f, 0x85, 0x54, 0x0c, 0x00, 0x00, //0x00000636 jne          LBB0_256
	0x48, 0x01, 0xc1, //0x0000063c addq         %rax, %rcx
	0x49, 0x89, 0xcc, //0x0000063f movq         %rcx, %r12
	//0x00000642 LBB0_63
	0x41, 0x83, 0xf8, 0x20, //0x00000642 cmpl         $32, %r8d
	0x0f, 0x85, 0x46, 0x02, 0x00, 0x00, //0x00000646 jne          LBB0_97
	0x49, 0x83, 0xc6, 0xe0, //0x0000064c addq         $-32, %r14
	0x48, 0x83, 0xc0, 0x20, //0x00000650 addq         $32, %rax
	0x49, 0x83, 0xfe, 0x1f, //0x00000654 cmpq         $31, %r14
	0x0f, 0x87, 0x02, 0xff, 0xff, 0xff, //0x00000658 ja           LBB0_49
	0xc5, 0xf8, 0x77, //0x0000065e vzeroupper   
	0x4c, 0x01, 0xd8, //0x00000661 addq         %r11, %rax
	0x49, 0x89, 0xc0, //0x00000664 movq         %rax, %r8
	0x4c, 0x89, 0x6d, 0xa8, //0x00000667 movq         %r13, $-88(%rbp)
	0x49, 0x83, 0xfe, 0x10, //0x0000066b cmpq         $16, %r14
	0x0f, 0x82, 0x7b, 0x01, 0x00, 0x00, //0x0000066f jb           LBB0_84
	//0x00000675 LBB0_66
	0x4d, 0x89, 0xc5, //0x00000675 movq         %r8, %r13
	0x4d, 0x29, 0xdd, //0x00000678 subq         %r11, %r13
	0x31, 0xc0, //0x0000067b xorl         %eax, %eax
	0xc5, 0x7a, 0x6f, 0x05, 0xfb, 0xfa, 0xff, 0xff, //0x0000067d vmovdqu      $-1285(%rip), %xmm8  /* LCPI0_11+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x0d, 0x03, 0xfb, 0xff, 0xff, //0x00000685 vmovdqu      $-1277(%rip), %xmm9  /* LCPI0_12+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x15, 0x0b, 0xfb, 0xff, 0xff, //0x0000068d vmovdqu      $-1269(%rip), %xmm10  /* LCPI0_13+0(%rip) */
	0xc5, 0x7a, 0x6f, 0x1d, 0x13, 0xfb, 0xff, 0xff, //0x00000695 vmovdqu      $-1261(%rip), %xmm11  /* LCPI0_14+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x25, 0x1b, 0xfb, 0xff, 0xff, //0x0000069d vmovdqu      $-1253(%rip), %xmm4  /* LCPI0_15+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x2d, 0x23, 0xfb, 0xff, 0xff, //0x000006a5 vmovdqu      $-1245(%rip), %xmm5  /* LCPI0_16+0(%rip) */
	0xc5, 0xfa, 0x6f, 0x35, 0x2b, 0xfb, 0xff, 0xff, //0x000006ad vmovdqu      $-1237(%rip), %xmm6  /* LCPI0_17+0(%rip) */
	0x4c, 0x89, 0x5d, 0xb0, //0x000006b5 movq         %r11, $-80(%rbp)
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000006b9 .p2align 4, 0x90
	//0x000006c0 LBB0_67
	0xc4, 0xc1, 0x7a, 0x6f, 0x3c, 0x00, //0x000006c0 vmovdqu      (%r8,%rax), %xmm7
	0xc4, 0xc1, 0x41, 0x64, 0xc0, //0x000006c6 vpcmpgtb     %xmm8, %xmm7, %xmm0
	0xc5, 0xb1, 0x64, 0xcf, //0x000006cb vpcmpgtb     %xmm7, %xmm9, %xmm1
	0xc5, 0xf9, 0xdb, 0xc1, //0x000006cf vpand        %xmm1, %xmm0, %xmm0
	0xc5, 0xa9, 0x74, 0xcf, //0x000006d3 vpcmpeqb     %xmm7, %xmm10, %xmm1
	0xc5, 0xa1, 0x74, 0xd7, //0x000006d7 vpcmpeqb     %xmm7, %xmm11, %xmm2
	0xc5, 0xe9, 0xeb, 0xc9, //0x000006db vpor         %xmm1, %xmm2, %xmm1
	0xc5, 0xc1, 0xdb, 0xd4, //0x000006df vpand        %xmm4, %xmm7, %xmm2
	0xc5, 0xe9, 0x74, 0xd6, //0x000006e3 vpcmpeqb     %xmm6, %xmm2, %xmm2
	0xc5, 0xc1, 0x74, 0xfd, //0x000006e7 vpcmpeqb     %xmm5, %xmm7, %xmm7
	0xc5, 0xe9, 0xeb, 0xdf, //0x000006eb vpor         %xmm7, %xmm2, %xmm3
	0xc5, 0xf1, 0xeb, 0xc0, //0x000006ef vpor         %xmm0, %xmm1, %xmm0
	0xc5, 0xe1, 0xeb, 0xc0, //0x000006f3 vpor         %xmm0, %xmm3, %xmm0
	0xc5, 0xf9, 0xd7, 0xf7, //0x000006f7 vpmovmskb    %xmm7, %esi
	0xc5, 0x79, 0xd7, 0xda, //0x000006fb vpmovmskb    %xmm2, %r11d
	0xc5, 0xf9, 0xd7, 0xd9, //0x000006ff vpmovmskb    %xmm1, %ebx
	0xc5, 0xf9, 0xd7, 0xc8, //0x00000703 vpmovmskb    %xmm0, %ecx
	0xf7, 0xd1, //0x00000707 notl         %ecx
	0x0f, 0xbc, 0xc9, //0x00000709 bsfl         %ecx, %ecx
	0x4c, 0x89, 0xd7, //0x0000070c movq         %r10, %rdi
	0x83, 0xf9, 0x10, //0x0000070f cmpl         $16, %ecx
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000712 je           LBB0_69
	0xba, 0xff, 0xff, 0xff, 0xff, //0x00000718 movl         $-1, %edx
	0xd3, 0xe2, //0x0000071d shll         %cl, %edx
	0xf7, 0xd2, //0x0000071f notl         %edx
	0x21, 0xd6, //0x00000721 andl         %edx, %esi
	0x41, 0x21, 0xd3, //0x00000723 andl         %edx, %r11d
	0x21, 0xda, //0x00000726 andl         %ebx, %edx
	0x89, 0xd3, //0x00000728 movl         %edx, %ebx
	//0x0000072a LBB0_69
	0x44, 0x8d, 0x56, 0xff, //0x0000072a leal         $-1(%rsi), %r10d
	0x41, 0x21, 0xf2, //0x0000072e andl         %esi, %r10d
	0x0f, 0x85, 0x80, 0x0f, 0x00, 0x00, //0x00000731 jne          LBB0_308
	0x41, 0x8d, 0x53, 0xff, //0x00000737 leal         $-1(%r11), %edx
	0x44, 0x21, 0xda, //0x0000073b andl         %r11d, %edx
	0x0f, 0x85, 0xd3, 0x0e, 0x00, 0x00, //0x0000073e jne          LBB0_298
	0x8d, 0x53, 0xff, //0x00000744 leal         $-1(%rbx), %edx
	0x21, 0xda, //0x00000747 andl         %ebx, %edx
	0x0f, 0x85, 0xc8, 0x0e, 0x00, 0x00, //0x00000749 jne          LBB0_298
	0x85, 0xf6, //0x0000074f testl        %esi, %esi
	0x0f, 0x84, 0x49, 0x00, 0x00, 0x00, //0x00000751 je           LBB0_76
	0x0f, 0xbc, 0xf6, //0x00000757 bsfl         %esi, %esi
	0x48, 0x83, 0xff, 0xff, //0x0000075a cmpq         $-1, %rdi
	0x0f, 0x85, 0x4c, 0x0f, 0x00, 0x00, //0x0000075e jne          LBB0_307
	0x4c, 0x01, 0xee, //0x00000764 addq         %r13, %rsi
	0x48, 0x01, 0xc6, //0x00000767 addq         %rax, %rsi
	0x49, 0x89, 0xf2, //0x0000076a movq         %rsi, %r10
	0x45, 0x85, 0xdb, //0x0000076d testl        %r11d, %r11d
	0x0f, 0x84, 0x3a, 0x00, 0x00, 0x00, //0x00000770 je           LBB0_75
	//0x00000776 LBB0_77
	0x41, 0x0f, 0xbc, 0xf3, //0x00000776 bsfl         %r11d, %esi
	0x49, 0x83, 0xff, 0xff, //0x0000077a cmpq         $-1, %r15
	0x0f, 0x85, 0x2c, 0x0f, 0x00, 0x00, //0x0000077e jne          LBB0_307
	0x4c, 0x01, 0xee, //0x00000784 addq         %r13, %rsi
	0x48, 0x01, 0xc6, //0x00000787 addq         %rax, %rsi
	0x49, 0x89, 0xf7, //0x0000078a movq         %rsi, %r15
	0x4c, 0x8b, 0x5d, 0xb0, //0x0000078d movq         $-80(%rbp), %r11
	0x85, 0xdb, //0x00000791 testl        %ebx, %ebx
	0x0f, 0x85, 0x23, 0x00, 0x00, 0x00, //0x00000793 jne          LBB0_79
	0xe9, 0x34, 0x00, 0x00, 0x00, //0x00000799 jmp          LBB0_81
	0x90, 0x90, //0x0000079e .p2align 4, 0x90
	//0x000007a0 LBB0_76
	0x49, 0x89, 0xfa, //0x000007a0 movq         %rdi, %r10
	0x45, 0x85, 0xdb, //0x000007a3 testl        %r11d, %r11d
	0x0f, 0x85, 0xca, 0xff, 0xff, 0xff, //0x000007a6 jne          LBB0_77
	0x90, 0x90, 0x90, 0x90, //0x000007ac .p2align 4, 0x90
	//0x000007b0 LBB0_75
	0x4c, 0x8b, 0x5d, 0xb0, //0x000007b0 movq         $-80(%rbp), %r11
	0x85, 0xdb, //0x000007b4 testl        %ebx, %ebx
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x000007b6 je           LBB0_81
	//0x000007bc LBB0_79
	0x0f, 0xbc, 0xf3, //0x000007bc bsfl         %ebx, %esi
	0x49, 0x83, 0xfc, 0xff, //0x000007bf cmpq         $-1, %r12
	0x0f, 0x85, 0x4b, 0x10, 0x00, 0x00, //0x000007c3 jne          LBB0_320
	0x4c, 0x01, 0xee, //0x000007c9 addq         %r13, %rsi
	0x48, 0x01, 0xc6, //0x000007cc addq         %rax, %rsi
	0x49, 0x89, 0xf4, //0x000007cf movq         %rsi, %r12
	//0x000007d2 LBB0_81
	0x83, 0xf9, 0x10, //0x000007d2 cmpl         $16, %ecx
	0x0f, 0x85, 0x08, 0x04, 0x00, 0x00, //0x000007d5 jne          LBB0_143
	0x49, 0x83, 0xc6, 0xf0, //0x000007db addq         $-16, %r14
	0x48, 0x83, 0xc0, 0x10, //0x000007df addq         $16, %rax
	0x49, 0x83, 0xfe, 0x0f, //0x000007e3 cmpq         $15, %r14
	0x0f, 0x87, 0xd3, 0xfe, 0xff, 0xff, //0x000007e7 ja           LBB0_67
	0x49, 0x01, 0xc0, //0x000007ed addq         %rax, %r8
	//0x000007f0 LBB0_84
	0x4c, 0x89, 0xd7, //0x000007f0 movq         %r10, %rdi
	0x4d, 0x85, 0xf6, //0x000007f3 testq        %r14, %r14
	0x4c, 0x8b, 0x6d, 0xa8, //0x000007f6 movq         $-88(%rbp), %r13
	0x0f, 0x84, 0xf7, 0x03, 0x00, 0x00, //0x000007fa je           LBB0_145
	0x4f, 0x8d, 0x14, 0x30, //0x00000800 leaq         (%r8,%r14), %r10
	0x4c, 0x89, 0xc3, //0x00000804 movq         %r8, %rbx
	0x4c, 0x29, 0xdb, //0x00000807 subq         %r11, %rbx
	0x31, 0xc0, //0x0000080a xorl         %eax, %eax
	0x48, 0x8d, 0x15, 0xd9, 0x2c, 0x00, 0x00, //0x0000080c leaq         $11481(%rip), %rdx  /* LJTI0_1+0(%rip) */
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x00000813 jmp          LBB0_89
	//0x00000818 LBB0_86
	0x48, 0x83, 0xff, 0xff, //0x00000818 cmpq         $-1, %rdi
	0x0f, 0x85, 0xb6, 0x0a, 0x00, 0x00, //0x0000081c jne          LBB0_259
	0x48, 0x8d, 0x3c, 0x03, //0x00000822 leaq         (%rbx,%rax), %rdi
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000826 .p2align 4, 0x90
	//0x00000830 LBB0_88
	0x48, 0x83, 0xc0, 0x01, //0x00000830 addq         $1, %rax
	0x49, 0x39, 0xc6, //0x00000834 cmpq         %rax, %r14
	0x0f, 0x84, 0x70, 0x07, 0x00, 0x00, //0x00000837 je           LBB0_202
	//0x0000083d LBB0_89
	0x41, 0x0f, 0xbe, 0x34, 0x00, //0x0000083d movsbl       (%r8,%rax), %esi
	0x8d, 0x4e, 0xd0, //0x00000842 leal         $-48(%rsi), %ecx
	0x83, 0xf9, 0x0a, //0x00000845 cmpl         $10, %ecx
	0x0f, 0x82, 0xe2, 0xff, 0xff, 0xff, //0x00000848 jb           LBB0_88
	0x8d, 0x4e, 0xd5, //0x0000084e leal         $-43(%rsi), %ecx
	0x83, 0xf9, 0x1a, //0x00000851 cmpl         $26, %ecx
	0x0f, 0x87, 0x1c, 0x00, 0x00, 0x00, //0x00000854 ja           LBB0_94
	0x48, 0x63, 0x0c, 0x8a, //0x0000085a movslq       (%rdx,%rcx,4), %rcx
	0x48, 0x01, 0xd1, //0x0000085e addq         %rdx, %rcx
	0xff, 0xe1, //0x00000861 jmpq         *%rcx
	//0x00000863 LBB0_92
	0x49, 0x83, 0xfc, 0xff, //0x00000863 cmpq         $-1, %r12
	0x0f, 0x85, 0x6b, 0x0a, 0x00, 0x00, //0x00000867 jne          LBB0_259
	0x4c, 0x8d, 0x24, 0x03, //0x0000086d leaq         (%rbx,%rax), %r12
	0xe9, 0xba, 0xff, 0xff, 0xff, //0x00000871 jmp          LBB0_88
	//0x00000876 LBB0_94
	0x83, 0xfe, 0x65, //0x00000876 cmpl         $101, %esi
	0x0f, 0x85, 0x75, 0x03, 0x00, 0x00, //0x00000879 jne          LBB0_144
	//0x0000087f LBB0_95
	0x49, 0x83, 0xff, 0xff, //0x0000087f cmpq         $-1, %r15
	0x0f, 0x85, 0x4f, 0x0a, 0x00, 0x00, //0x00000883 jne          LBB0_259
	0x4c, 0x8d, 0x3c, 0x03, //0x00000889 leaq         (%rbx,%rax), %r15
	0xe9, 0x9e, 0xff, 0xff, 0xff, //0x0000088d jmp          LBB0_88
	//0x00000892 LBB0_97
	0x49, 0x01, 0xc0, //0x00000892 addq         %rax, %r8
	0x4d, 0x01, 0xd8, //0x00000895 addq         %r11, %r8
	0xc5, 0xf8, 0x77, //0x00000898 vzeroupper   
	0xe9, 0x5a, 0x03, 0x00, 0x00, //0x0000089b jmp          LBB0_146
	//0x000008a0 LBB0_98
	0x80, 0xfb, 0x30, //0x000008a0 cmpb         $48, %bl
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x000008a3 jne          LBB0_102
	0x4c, 0x8d, 0x59, 0x01, //0x000008a9 leaq         $1(%rcx), %r11
	0x4c, 0x39, 0xf1, //0x000008ad cmpq         %r14, %rcx
	0x0f, 0x83, 0x52, 0x0a, 0x00, 0x00, //0x000008b0 jae          LBB0_263
	0x48, 0x8b, 0x45, 0xc8, //0x000008b6 movq         $-56(%rbp), %rax
	0x42, 0x8a, 0x04, 0x18, //0x000008ba movb         (%rax,%r11), %al
	0x04, 0xd2, //0x000008be addb         $-46, %al
	0x3c, 0x37, //0x000008c0 cmpb         $55, %al
	0x0f, 0x87, 0x40, 0x0a, 0x00, 0x00, //0x000008c2 ja           LBB0_263
	0x0f, 0xb6, 0xc0, //0x000008c8 movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x000008cb movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x000008d5 btq          %rax, %rdx
	0x0f, 0x83, 0x29, 0x0a, 0x00, 0x00, //0x000008d9 jae          LBB0_263
	//0x000008df LBB0_102
	0x40, 0x88, 0x75, 0xa4, //0x000008df movb         %sil, $-92(%rbp)
	0xb0, 0x01, //0x000008e3 movb         $1, %al
	0x89, 0x45, 0xb0, //0x000008e5 movl         %eax, $-80(%rbp)
	0x4c, 0x39, 0xf1, //0x000008e8 cmpq         %r14, %rcx
	0x0f, 0x83, 0x6c, 0x05, 0x00, 0x00, //0x000008eb jae          LBB0_179
	0xbe, 0xd0, 0xff, 0xff, 0xff, //0x000008f1 movl         $4294967248, %esi
	0x48, 0x83, 0xc1, 0x01, //0x000008f6 addq         $1, %rcx
	0x31, 0xd2, //0x000008fa xorl         %edx, %edx
	0x31, 0xc0, //0x000008fc xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x000008fe xorl         %r10d, %r10d
	0x4c, 0x8b, 0x5d, 0xc8, //0x00000901 movq         $-56(%rbp), %r11
	//0x00000905 LBB0_104
	0x83, 0xf8, 0x12, //0x00000905 cmpl         $18, %eax
	0x0f, 0x8f, 0x15, 0x00, 0x00, 0x00, //0x00000908 jg           LBB0_106
	0x4b, 0x8d, 0x3c, 0x92, //0x0000090e leaq         (%r10,%r10,4), %rdi
	0x0f, 0xb6, 0xdb, //0x00000912 movzbl       %bl, %ebx
	0x01, 0xf3, //0x00000915 addl         %esi, %ebx
	0x4c, 0x8d, 0x14, 0x7b, //0x00000917 leaq         (%rbx,%rdi,2), %r10
	0x83, 0xc0, 0x01, //0x0000091b addl         $1, %eax
	0xe9, 0x03, 0x00, 0x00, 0x00, //0x0000091e jmp          LBB0_107
	//0x00000923 LBB0_106
	0x83, 0xc2, 0x01, //0x00000923 addl         $1, %edx
	//0x00000926 LBB0_107
	0x49, 0x39, 0xce, //0x00000926 cmpq         %rcx, %r14
	0x0f, 0x84, 0x52, 0x06, 0x00, 0x00, //0x00000929 je           LBB0_199
	0x41, 0x0f, 0xb6, 0x1c, 0x0b, //0x0000092f movzbl       (%r11,%rcx), %ebx
	0x8d, 0x7b, 0xd0, //0x00000934 leal         $-48(%rbx), %edi
	0x48, 0x83, 0xc1, 0x01, //0x00000937 addq         $1, %rcx
	0x40, 0x80, 0xff, 0x0a, //0x0000093b cmpb         $10, %dil
	0x0f, 0x82, 0xc0, 0xff, 0xff, 0xff, //0x0000093f jb           LBB0_104
	0x80, 0xfb, 0x2e, //0x00000945 cmpb         $46, %bl
	0x0f, 0x85, 0x6f, 0x06, 0x00, 0x00, //0x00000948 jne          LBB0_204
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x0000094e movq         $8, (%r13)
	0x4c, 0x39, 0xf1, //0x00000956 cmpq         %r14, %rcx
	0x0f, 0x83, 0xb9, 0x07, 0x00, 0x00, //0x00000959 jae          LBB0_232
	0x41, 0x8a, 0x1c, 0x0b, //0x0000095f movb         (%r11,%rcx), %bl
	0x80, 0xc3, 0xd0, //0x00000963 addb         $-48, %bl
	0x80, 0xfb, 0x0a, //0x00000966 cmpb         $10, %bl
	0x0f, 0x83, 0x15, 0xfb, 0xff, 0xff, //0x00000969 jae          LBB0_40
	0xc7, 0x45, 0xb0, 0x00, 0x00, 0x00, 0x00, //0x0000096f movl         $0, $-80(%rbp)
	0xe9, 0x46, 0x06, 0x00, 0x00, //0x00000976 jmp          LBB0_205
	//0x0000097b LBB0_113
	0x41, 0xf6, 0xc0, 0x20, //0x0000097b testb        $32, %r8b
	0x0f, 0x85, 0x3c, 0x03, 0x00, 0x00, //0x0000097f jne          LBB0_162
	0x4c, 0x39, 0xf2, //0x00000985 cmpq         %r14, %rdx
	0x0f, 0x84, 0xf9, 0x25, 0x00, 0x00, //0x00000988 je           LBB0_645
	0x4d, 0x89, 0xf7, //0x0000098e movq         %r14, %r15
	0x49, 0x29, 0xd7, //0x00000991 subq         %rdx, %r15
	0x49, 0x83, 0xff, 0x40, //0x00000994 cmpq         $64, %r15
	0x0f, 0x82, 0xf5, 0x25, 0x00, 0x00, //0x00000998 jb           LBB0_646
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x0000099e movq         $-2, %rax
	0x4c, 0x29, 0xc8, //0x000009a5 subq         %r9, %rax
	0x49, 0x83, 0xc1, 0x01, //0x000009a8 addq         $1, %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x000009ac movq         $-1, %r8
	0x45, 0x31, 0xe4, //0x000009b3 xorl         %r12d, %r12d
	0xc5, 0xfe, 0x6f, 0x05, 0x62, 0xf6, 0xff, 0xff, //0x000009b6 vmovdqu      $-2462(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x7a, 0xf6, 0xff, 0xff, //0x000009be vmovdqu      $-2438(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0x49, 0xba, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000009c6 movabsq      $-6148914691236517206, %r10
	0x49, 0xbb, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x000009d0 movabsq      $6148914691236517205, %r11
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000009da .p2align 4, 0x90
	//0x000009e0 LBB0_117
	0x48, 0x8b, 0x4d, 0xc8, //0x000009e0 movq         $-56(%rbp), %rcx
	0xc4, 0xa1, 0x7e, 0x6f, 0x14, 0x09, //0x000009e4 vmovdqu      (%rcx,%r9), %ymm2
	0xc4, 0xa1, 0x7e, 0x6f, 0x5c, 0x09, 0x20, //0x000009ea vmovdqu      $32(%rcx,%r9), %ymm3
	0xc5, 0xed, 0x74, 0xe0, //0x000009f1 vpcmpeqb     %ymm0, %ymm2, %ymm4
	0xc5, 0xfd, 0xd7, 0xcc, //0x000009f5 vpmovmskb    %ymm4, %ecx
	0xc5, 0xe5, 0x74, 0xe0, //0x000009f9 vpcmpeqb     %ymm0, %ymm3, %ymm4
	0xc5, 0xfd, 0xd7, 0xfc, //0x000009fd vpmovmskb    %ymm4, %edi
	0xc5, 0xed, 0x74, 0xd1, //0x00000a01 vpcmpeqb     %ymm1, %ymm2, %ymm2
	0xc5, 0xfd, 0xd7, 0xf2, //0x00000a05 vpmovmskb    %ymm2, %esi
	0xc5, 0xe5, 0x74, 0xd1, //0x00000a09 vpcmpeqb     %ymm1, %ymm3, %ymm2
	0xc5, 0xfd, 0xd7, 0xda, //0x00000a0d vpmovmskb    %ymm2, %ebx
	0x48, 0xc1, 0xe7, 0x20, //0x00000a11 shlq         $32, %rdi
	0x48, 0x09, 0xf9, //0x00000a15 orq          %rdi, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00000a18 shlq         $32, %rbx
	0x48, 0x09, 0xde, //0x00000a1c orq          %rbx, %rsi
	0x0f, 0x85, 0x30, 0x00, 0x00, 0x00, //0x00000a1f jne          LBB0_121
	0x4d, 0x85, 0xe4, //0x00000a25 testq        %r12, %r12
	0x0f, 0x85, 0x41, 0x00, 0x00, 0x00, //0x00000a28 jne          LBB0_123
	0x45, 0x31, 0xe4, //0x00000a2e xorl         %r12d, %r12d
	0x48, 0x85, 0xc9, //0x00000a31 testq        %rcx, %rcx
	0x0f, 0x85, 0x7b, 0x00, 0x00, 0x00, //0x00000a34 jne          LBB0_125
	//0x00000a3a LBB0_120
	0x49, 0x83, 0xc7, 0xc0, //0x00000a3a addq         $-64, %r15
	0x48, 0x83, 0xc0, 0xc0, //0x00000a3e addq         $-64, %rax
	0x49, 0x83, 0xc1, 0x40, //0x00000a42 addq         $64, %r9
	0x49, 0x83, 0xff, 0x3f, //0x00000a46 cmpq         $63, %r15
	0x0f, 0x87, 0x90, 0xff, 0xff, 0xff, //0x00000a4a ja           LBB0_117
	0xe9, 0xef, 0x0d, 0x00, 0x00, //0x00000a50 jmp          LBB0_322
	//0x00000a55 LBB0_121
	0x4c, 0x89, 0x6d, 0xa8, //0x00000a55 movq         %r13, $-88(%rbp)
	0x49, 0x83, 0xf8, 0xff, //0x00000a59 cmpq         $-1, %r8
	0x0f, 0x85, 0x10, 0x00, 0x00, 0x00, //0x00000a5d jne          LBB0_124
	0x4c, 0x0f, 0xbc, 0xc6, //0x00000a63 bsfq         %rsi, %r8
	0x4d, 0x01, 0xc8, //0x00000a67 addq         %r9, %r8
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000a6a jmp          LBB0_124
	//0x00000a6f LBB0_123
	0x4c, 0x89, 0x6d, 0xa8, //0x00000a6f movq         %r13, $-88(%rbp)
	//0x00000a73 LBB0_124
	0x4c, 0x89, 0xe7, //0x00000a73 movq         %r12, %rdi
	0x48, 0xf7, 0xd7, //0x00000a76 notq         %rdi
	0x48, 0x21, 0xf7, //0x00000a79 andq         %rsi, %rdi
	0x4c, 0x8d, 0x2c, 0x3f, //0x00000a7c leaq         (%rdi,%rdi), %r13
	0x4d, 0x09, 0xe5, //0x00000a80 orq          %r12, %r13
	0x4c, 0x89, 0xeb, //0x00000a83 movq         %r13, %rbx
	0x48, 0xf7, 0xd3, //0x00000a86 notq         %rbx
	0x48, 0x21, 0xf3, //0x00000a89 andq         %rsi, %rbx
	0x4c, 0x21, 0xd3, //0x00000a8c andq         %r10, %rbx
	0x45, 0x31, 0xe4, //0x00000a8f xorl         %r12d, %r12d
	0x48, 0x01, 0xfb, //0x00000a92 addq         %rdi, %rbx
	0x41, 0x0f, 0x92, 0xc4, //0x00000a95 setb         %r12b
	0x48, 0x01, 0xdb, //0x00000a99 addq         %rbx, %rbx
	0x4c, 0x31, 0xdb, //0x00000a9c xorq         %r11, %rbx
	0x4c, 0x21, 0xeb, //0x00000a9f andq         %r13, %rbx
	0x48, 0xf7, 0xd3, //0x00000aa2 notq         %rbx
	0x48, 0x21, 0xd9, //0x00000aa5 andq         %rbx, %rcx
	0x4c, 0x8b, 0x6d, 0xa8, //0x00000aa8 movq         $-88(%rbp), %r13
	0x48, 0x85, 0xc9, //0x00000aac testq        %rcx, %rcx
	0x0f, 0x84, 0x85, 0xff, 0xff, 0xff, //0x00000aaf je           LBB0_120
	//0x00000ab5 LBB0_125
	0x4c, 0x0f, 0xbc, 0xd9, //0x00000ab5 bsfq         %rcx, %r11
	0x49, 0x29, 0xc3, //0x00000ab9 subq         %rax, %r11
	0xe9, 0x94, 0x04, 0x00, 0x00, //0x00000abc jmp          LBB0_197
	//0x00000ac1 LBB0_126
	0x45, 0x85, 0xc0, //0x00000ac1 testl        %r8d, %r8d
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000ac4 movq         $-2, %rax
	0xb9, 0x0b, 0x00, 0x00, 0x00, //0x00000acb movl         $11, %ecx
	0xe9, 0xe7, 0x00, 0x00, 0x00, //0x00000ad0 jmp          LBB0_141
	//0x00000ad5 LBB0_127
	0x49, 0x8d, 0x4e, 0xfd, //0x00000ad5 leaq         $-3(%r14), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000ad9 movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x00000ae0 cmpq         %rcx, %r9
	0x0f, 0x83, 0xb8, 0x04, 0x00, 0x00, //0x00000ae3 jae          LBB0_268
	0x41, 0x8b, 0x0f, //0x00000ae9 movl         (%r15), %ecx
	0x81, 0xf9, 0x6e, 0x75, 0x6c, 0x6c, //0x00000aec cmpl         $1819047278, %ecx
	0x0f, 0x85, 0x75, 0x03, 0x00, 0x00, //0x00000af2 jne          LBB0_180
	0x49, 0x83, 0xc1, 0x04, //0x00000af8 addq         $4, %r9
	0xb8, 0x02, 0x00, 0x00, 0x00, //0x00000afc movl         $2, %eax
	0xe9, 0x98, 0x04, 0x00, 0x00, //0x00000b01 jmp          LBB0_267
	//0x00000b06 LBB0_130
	0x45, 0x85, 0xc0, //0x00000b06 testl        %r8d, %r8d
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000b09 movq         $-2, %rax
	0xb9, 0x0d, 0x00, 0x00, 0x00, //0x00000b10 movl         $13, %ecx
	0xe9, 0xa2, 0x00, 0x00, 0x00, //0x00000b15 jmp          LBB0_141
	//0x00000b1a LBB0_131
	0x49, 0xc7, 0x45, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x00000b1a movq         $-2, (%r13)
	0x4d, 0x89, 0xcb, //0x00000b22 movq         %r9, %r11
	0xe9, 0xde, 0x07, 0x00, 0x00, //0x00000b25 jmp          LBB0_263
	//0x00000b2a LBB0_132
	0x45, 0x85, 0xc0, //0x00000b2a testl        %r8d, %r8d
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000b2d movq         $-2, %rax
	0xb9, 0x0a, 0x00, 0x00, 0x00, //0x00000b34 movl         $10, %ecx
	0xe9, 0x7e, 0x00, 0x00, 0x00, //0x00000b39 jmp          LBB0_141
	//0x00000b3e LBB0_133
	0x49, 0xc7, 0x45, 0x00, 0x05, 0x00, 0x00, 0x00, //0x00000b3e movq         $5, (%r13)
	0xe9, 0xba, 0x07, 0x00, 0x00, //0x00000b46 jmp          LBB0_262
	//0x00000b4b LBB0_134
	0x49, 0x8d, 0x4e, 0xfc, //0x00000b4b leaq         $-4(%r14), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b4f movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x00000b56 cmpq         %rcx, %r9
	0x0f, 0x83, 0x42, 0x04, 0x00, 0x00, //0x00000b59 jae          LBB0_268
	0x8b, 0x0c, 0x17, //0x00000b5f movl         (%rdi,%rdx), %ecx
	0x81, 0xf9, 0x61, 0x6c, 0x73, 0x65, //0x00000b62 cmpl         $1702063201, %ecx
	0x0f, 0x85, 0x34, 0x03, 0x00, 0x00, //0x00000b68 jne          LBB0_184
	0x49, 0x83, 0xc1, 0x05, //0x00000b6e addq         $5, %r9
	0xb8, 0x04, 0x00, 0x00, 0x00, //0x00000b72 movl         $4, %eax
	0xe9, 0x22, 0x04, 0x00, 0x00, //0x00000b77 jmp          LBB0_267
	//0x00000b7c LBB0_137
	0x49, 0x8d, 0x4e, 0xfd, //0x00000b7c leaq         $-3(%r14), %rcx
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000b80 movq         $-1, %rax
	0x49, 0x39, 0xc9, //0x00000b87 cmpq         %rcx, %r9
	0x0f, 0x83, 0x11, 0x04, 0x00, 0x00, //0x00000b8a jae          LBB0_268
	0x41, 0x8b, 0x0f, //0x00000b90 movl         (%r15), %ecx
	0x81, 0xf9, 0x74, 0x72, 0x75, 0x65, //0x00000b93 cmpl         $1702195828, %ecx
	0x0f, 0x85, 0x43, 0x03, 0x00, 0x00, //0x00000b99 jne          LBB0_188
	0x49, 0x83, 0xc1, 0x04, //0x00000b9f addq         $4, %r9
	0xb8, 0x03, 0x00, 0x00, 0x00, //0x00000ba3 movl         $3, %eax
	0xe9, 0xf1, 0x03, 0x00, 0x00, //0x00000ba8 jmp          LBB0_267
	//0x00000bad LBB0_140
	0x45, 0x85, 0xc0, //0x00000bad testl        %r8d, %r8d
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000bb0 movq         $-2, %rax
	0xb9, 0x0c, 0x00, 0x00, 0x00, //0x00000bb7 movl         $12, %ecx
	//0x00000bbc LBB0_141
	0x48, 0x0f, 0x49, 0xc8, //0x00000bbc cmovnsq      %rax, %rcx
	0x49, 0x89, 0x4d, 0x00, //0x00000bc0 movq         %rcx, (%r13)
	0x41, 0xc1, 0xf8, 0x1f, //0x00000bc4 sarl         $31, %r8d
	0x41, 0xf7, 0xd0, //0x00000bc8 notl         %r8d
	0x49, 0x63, 0xc0, //0x00000bcb movslq       %r8d, %rax
	0x48, 0x01, 0xc2, //0x00000bce addq         %rax, %rdx
	0xe9, 0x2f, 0x07, 0x00, 0x00, //0x00000bd1 jmp          LBB0_262
	//0x00000bd6 LBB0_142
	0x49, 0xc7, 0x45, 0x00, 0x06, 0x00, 0x00, 0x00, //0x00000bd6 movq         $6, (%r13)
	0xe9, 0x22, 0x07, 0x00, 0x00, //0x00000bde jmp          LBB0_262
	//0x00000be3 LBB0_143
	0x89, 0xc9, //0x00000be3 movl         %ecx, %ecx
	0x49, 0x01, 0xc8, //0x00000be5 addq         %rcx, %r8
	0x49, 0x01, 0xc0, //0x00000be8 addq         %rax, %r8
	0x4c, 0x8b, 0x6d, 0xa8, //0x00000beb movq         $-88(%rbp), %r13
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00000bef jmp          LBB0_146
	//0x00000bf4 LBB0_144
	0x49, 0x01, 0xc0, //0x00000bf4 addq         %rax, %r8
	//0x00000bf7 LBB0_145
	0x49, 0x89, 0xfa, //0x00000bf7 movq         %rdi, %r10
	//0x00000bfa LBB0_146
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000bfa movq         $-1, %rax
	0x4d, 0x85, 0xd2, //0x00000c01 testq        %r10, %r10
	0x0f, 0x84, 0x9d, 0x00, 0x00, 0x00, //0x00000c04 je           LBB0_160
	0x4d, 0x85, 0xe4, //0x00000c0a testq        %r12, %r12
	0x0f, 0x84, 0x94, 0x00, 0x00, 0x00, //0x00000c0d je           LBB0_160
	0x4d, 0x85, 0xff, //0x00000c13 testq        %r15, %r15
	0x0f, 0x84, 0x8b, 0x00, 0x00, 0x00, //0x00000c16 je           LBB0_160
	0x4d, 0x29, 0xd8, //0x00000c1c subq         %r11, %r8
	0x49, 0x8d, 0x40, 0xff, //0x00000c1f leaq         $-1(%r8), %rax
	0x49, 0x39, 0xc2, //0x00000c23 cmpq         %rax, %r10
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00000c26 je           LBB0_155
	0x49, 0x39, 0xc4, //0x00000c2c cmpq         %rax, %r12
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x00000c2f je           LBB0_155
	0x49, 0x39, 0xc7, //0x00000c35 cmpq         %rax, %r15
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00000c38 je           LBB0_155
	0x4d, 0x85, 0xe4, //0x00000c3e testq        %r12, %r12
	0x0f, 0x8e, 0xf3, 0x01, 0x00, 0x00, //0x00000c41 jle          LBB0_176
	0x49, 0x8d, 0x44, 0x24, 0xff, //0x00000c47 leaq         $-1(%r12), %rax
	0x49, 0x39, 0xc7, //0x00000c4c cmpq         %rax, %r15
	0x0f, 0x84, 0xe5, 0x01, 0x00, 0x00, //0x00000c4f je           LBB0_176
	0x49, 0xf7, 0xd4, //0x00000c55 notq         %r12
	0x4c, 0x89, 0xe0, //0x00000c58 movq         %r12, %rax
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00000c5b jmp          LBB0_156
	//0x00000c60 LBB0_155
	0x49, 0xf7, 0xd8, //0x00000c60 negq         %r8
	0x4c, 0x89, 0xc0, //0x00000c63 movq         %r8, %rax
	//0x00000c66 LBB0_156
	0x48, 0x85, 0xc0, //0x00000c66 testq        %rax, %rax
	0x0f, 0x88, 0x38, 0x00, 0x00, 0x00, //0x00000c69 js           LBB0_160
	//0x00000c6f LBB0_157
	0x49, 0x01, 0xc3, //0x00000c6f addq         %rax, %r11
	0x4c, 0x2b, 0x5d, 0xc8, //0x00000c72 subq         $-56(%rbp), %r11
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00000c76 movabsq      $-9223372036854775808, %rax
	0x48, 0x83, 0xc0, 0xfe, //0x00000c80 addq         $-2, %rax
	0x49, 0x39, 0xc1, //0x00000c84 cmpq         %rax, %r9
	0x0f, 0x86, 0x09, 0x00, 0x00, 0x00, //0x00000c87 jbe          LBB0_159
	0x4d, 0x89, 0x4d, 0x00, //0x00000c8d movq         %r9, (%r13)
	0xe9, 0x72, 0x06, 0x00, 0x00, //0x00000c91 jmp          LBB0_263
	//0x00000c96 LBB0_159
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00000c96 movq         $8, (%r13)
	0x4d, 0x89, 0x4d, 0x18, //0x00000c9e movq         %r9, $24(%r13)
	0xe9, 0x61, 0x06, 0x00, 0x00, //0x00000ca2 jmp          LBB0_263
	//0x00000ca7 LBB0_160
	0x48, 0xf7, 0xd0, //0x00000ca7 notq         %rax
	0x49, 0x01, 0xc3, //0x00000caa addq         %rax, %r11
	//0x00000cad LBB0_161
	0x49, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00000cad movq         $-2, %r9
	0x4c, 0x2b, 0x5d, 0xc8, //0x00000cb4 subq         $-56(%rbp), %r11
	0x4d, 0x89, 0x4d, 0x00, //0x00000cb8 movq         %r9, (%r13)
	0xe9, 0x47, 0x06, 0x00, 0x00, //0x00000cbc jmp          LBB0_263
	//0x00000cc1 LBB0_162
	0x4c, 0x39, 0xf2, //0x00000cc1 cmpq         %r14, %rdx
	0x0f, 0x84, 0xbd, 0x22, 0x00, 0x00, //0x00000cc4 je           LBB0_645
	0x4d, 0x89, 0xeb, //0x00000cca movq         %r13, %r11
	0x4d, 0x89, 0xf7, //0x00000ccd movq         %r14, %r15
	0x49, 0x29, 0xd7, //0x00000cd0 subq         %rdx, %r15
	0x49, 0x83, 0xff, 0x40, //0x00000cd3 cmpq         $64, %r15
	0x0f, 0x82, 0xda, 0x22, 0x00, 0x00, //0x00000cd7 jb           LBB0_648
	0x49, 0xc7, 0xc4, 0xfe, 0xff, 0xff, 0xff, //0x00000cdd movq         $-2, %r12
	0x4d, 0x29, 0xcc, //0x00000ce4 subq         %r9, %r12
	0x49, 0x83, 0xc1, 0x01, //0x00000ce7 addq         $1, %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000ceb movq         $-1, %r8
	0x45, 0x31, 0xed, //0x00000cf2 xorl         %r13d, %r13d
	0xc5, 0xfe, 0x6f, 0x05, 0x23, 0xf3, 0xff, 0xff, //0x00000cf5 vmovdqu      $-3293(%rip), %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x0d, 0x3b, 0xf3, 0xff, 0xff, //0x00000cfd vmovdqu      $-3269(%rip), %ymm1  /* LCPI0_2+0(%rip) */
	0xc5, 0xfe, 0x6f, 0x15, 0x53, 0xf3, 0xff, 0xff, //0x00000d05 vmovdqu      $-3245(%rip), %ymm2  /* LCPI0_3+0(%rip) */
	0xc5, 0xe5, 0x76, 0xdb, //0x00000d0d vpcmpeqd     %ymm3, %ymm3, %ymm3
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000d11 .p2align 4, 0x90
	//0x00000d20 LBB0_165
	0x48, 0x8b, 0x45, 0xc8, //0x00000d20 movq         $-56(%rbp), %rax
	0xc4, 0xa1, 0x7e, 0x6f, 0x24, 0x08, //0x00000d24 vmovdqu      (%rax,%r9), %ymm4
	0xc4, 0xa1, 0x7e, 0x6f, 0x6c, 0x08, 0x20, //0x00000d2a vmovdqu      $32(%rax,%r9), %ymm5
	0xc5, 0xdd, 0x74, 0xf0, //0x00000d31 vpcmpeqb     %ymm0, %ymm4, %ymm6
	0xc5, 0xfd, 0xd7, 0xce, //0x00000d35 vpmovmskb    %ymm6, %ecx
	0xc5, 0xd5, 0x74, 0xf0, //0x00000d39 vpcmpeqb     %ymm0, %ymm5, %ymm6
	0xc5, 0xfd, 0xd7, 0xfe, //0x00000d3d vpmovmskb    %ymm6, %edi
	0xc5, 0xdd, 0x74, 0xf1, //0x00000d41 vpcmpeqb     %ymm1, %ymm4, %ymm6
	0xc5, 0xfd, 0xd7, 0xf6, //0x00000d45 vpmovmskb    %ymm6, %esi
	0xc5, 0xd5, 0x74, 0xf1, //0x00000d49 vpcmpeqb     %ymm1, %ymm5, %ymm6
	0xc5, 0xfd, 0xd7, 0xde, //0x00000d4d vpmovmskb    %ymm6, %ebx
	0xc5, 0xed, 0x64, 0xf5, //0x00000d51 vpcmpgtb     %ymm5, %ymm2, %ymm6
	0xc5, 0xd5, 0x64, 0xeb, //0x00000d55 vpcmpgtb     %ymm3, %ymm5, %ymm5
	0xc5, 0xcd, 0xdb, 0xed, //0x00000d59 vpand        %ymm5, %ymm6, %ymm5
	0xc5, 0xfd, 0xd7, 0xc5, //0x00000d5d vpmovmskb    %ymm5, %eax
	0x48, 0xc1, 0xe7, 0x20, //0x00000d61 shlq         $32, %rdi
	0x48, 0x09, 0xf9, //0x00000d65 orq          %rdi, %rcx
	0x48, 0xc1, 0xe3, 0x20, //0x00000d68 shlq         $32, %rbx
	0x48, 0xc1, 0xe0, 0x20, //0x00000d6c shlq         $32, %rax
	0x48, 0x09, 0xde, //0x00000d70 orq          %rbx, %rsi
	0x0f, 0x85, 0x4c, 0x00, 0x00, 0x00, //0x00000d73 jne          LBB0_171
	0x4d, 0x85, 0xed, //0x00000d79 testq        %r13, %r13
	0x0f, 0x85, 0x54, 0x00, 0x00, 0x00, //0x00000d7c jne          LBB0_173
	0x45, 0x31, 0xed, //0x00000d82 xorl         %r13d, %r13d
	//0x00000d85 LBB0_168
	0xc5, 0xed, 0x64, 0xec, //0x00000d85 vpcmpgtb     %ymm4, %ymm2, %ymm5
	0xc5, 0xdd, 0x64, 0xe3, //0x00000d89 vpcmpgtb     %ymm3, %ymm4, %ymm4
	0xc5, 0xd5, 0xdb, 0xe4, //0x00000d8d vpand        %ymm4, %ymm5, %ymm4
	0xc5, 0xfd, 0xd7, 0xf4, //0x00000d91 vpmovmskb    %ymm4, %esi
	0x48, 0x09, 0xf0, //0x00000d95 orq          %rsi, %rax
	0x48, 0x85, 0xc9, //0x00000d98 testq        %rcx, %rcx
	0x0f, 0x85, 0x83, 0x00, 0x00, 0x00, //0x00000d9b jne          LBB0_174
	0x48, 0x85, 0xc0, //0x00000da1 testq        %rax, %rax
	0x0f, 0x85, 0x83, 0x24, 0x00, 0x00, //0x00000da4 jne          LBB0_686
	0x49, 0x83, 0xc7, 0xc0, //0x00000daa addq         $-64, %r15
	0x49, 0x83, 0xc4, 0xc0, //0x00000dae addq         $-64, %r12
	0x49, 0x83, 0xc1, 0x40, //0x00000db2 addq         $64, %r9
	0x49, 0x83, 0xff, 0x3f, //0x00000db6 cmpq         $63, %r15
	0x0f, 0x87, 0x60, 0xff, 0xff, 0xff, //0x00000dba ja           LBB0_165
	0xe9, 0xde, 0x0a, 0x00, 0x00, //0x00000dc0 jmp          LBB0_327
	//0x00000dc5 LBB0_171
	0x49, 0x83, 0xf8, 0xff, //0x00000dc5 cmpq         $-1, %r8
	0x0f, 0x85, 0x07, 0x00, 0x00, 0x00, //0x00000dc9 jne          LBB0_173
	0x4c, 0x0f, 0xbc, 0xc6, //0x00000dcf bsfq         %rsi, %r8
	0x4d, 0x01, 0xc8, //0x00000dd3 addq         %r9, %r8
	//0x00000dd6 LBB0_173
	0x4c, 0x89, 0xef, //0x00000dd6 movq         %r13, %rdi
	0x48, 0xf7, 0xd7, //0x00000dd9 notq         %rdi
	0x48, 0x21, 0xf7, //0x00000ddc andq         %rsi, %rdi
	0x4c, 0x8d, 0x14, 0x3f, //0x00000ddf leaq         (%rdi,%rdi), %r10
	0x4d, 0x09, 0xea, //0x00000de3 orq          %r13, %r10
	0x4c, 0x89, 0xd3, //0x00000de6 movq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x00000de9 notq         %rbx
	0x48, 0x21, 0xf3, //0x00000dec andq         %rsi, %rbx
	0x48, 0xbe, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000def movabsq      $-6148914691236517206, %rsi
	0x48, 0x21, 0xf3, //0x00000df9 andq         %rsi, %rbx
	0x45, 0x31, 0xed, //0x00000dfc xorl         %r13d, %r13d
	0x48, 0x01, 0xfb, //0x00000dff addq         %rdi, %rbx
	0x41, 0x0f, 0x92, 0xc5, //0x00000e02 setb         %r13b
	0x48, 0x01, 0xdb, //0x00000e06 addq         %rbx, %rbx
	0x48, 0xbe, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000e09 movabsq      $6148914691236517205, %rsi
	0x48, 0x31, 0xf3, //0x00000e13 xorq         %rsi, %rbx
	0x4c, 0x21, 0xd3, //0x00000e16 andq         %r10, %rbx
	0x48, 0xf7, 0xd3, //0x00000e19 notq         %rbx
	0x48, 0x21, 0xd9, //0x00000e1c andq         %rbx, %rcx
	0xe9, 0x61, 0xff, 0xff, 0xff, //0x00000e1f jmp          LBB0_168
	//0x00000e24 LBB0_174
	0x48, 0x0f, 0xbc, 0xc9, //0x00000e24 bsfq         %rcx, %rcx
	0x48, 0x85, 0xc0, //0x00000e28 testq        %rax, %rax
	0x0f, 0x84, 0x0d, 0x01, 0x00, 0x00, //0x00000e2b je           LBB0_194
	0x48, 0x0f, 0xbc, 0xc0, //0x00000e31 bsfq         %rax, %rax
	0xe9, 0x09, 0x01, 0x00, 0x00, //0x00000e35 jmp          LBB0_195
	//0x00000e3a LBB0_176
	0x4c, 0x89, 0xd0, //0x00000e3a movq         %r10, %rax
	0x4c, 0x09, 0xf8, //0x00000e3d orq          %r15, %rax
	0x0f, 0x99, 0xc0, //0x00000e40 setns        %al
	0x0f, 0x88, 0xd9, 0x00, 0x00, 0x00, //0x00000e43 js           LBB0_193
	0x4d, 0x39, 0xfa, //0x00000e49 cmpq         %r15, %r10
	0x0f, 0x8c, 0xd0, 0x00, 0x00, 0x00, //0x00000e4c jl           LBB0_193
	0x49, 0xf7, 0xd2, //0x00000e52 notq         %r10
	0x4c, 0x89, 0xd0, //0x00000e55 movq         %r10, %rax
	0xe9, 0x09, 0xfe, 0xff, 0xff, //0x00000e58 jmp          LBB0_156
	//0x00000e5d LBB0_179
	0x31, 0xd2, //0x00000e5d xorl         %edx, %edx
	0x31, 0xc0, //0x00000e5f xorl         %eax, %eax
	0x45, 0x31, 0xd2, //0x00000e61 xorl         %r10d, %r10d
	0x4c, 0x8b, 0x5d, 0xc8, //0x00000e64 movq         $-56(%rbp), %r11
	0xe9, 0x54, 0x01, 0x00, 0x00, //0x00000e68 jmp          LBB0_205
	//0x00000e6d LBB0_180
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000e6d movq         $-2, %rax
	0x80, 0xf9, 0x6e, //0x00000e74 cmpb         $110, %cl
	0x0f, 0x85, 0x21, 0x01, 0x00, 0x00, //0x00000e77 jne          LBB0_267
	0x42, 0x80, 0x7c, 0x0f, 0x01, 0x75, //0x00000e7d cmpb         $117, $1(%rdi,%r9)
	0x0f, 0x85, 0x08, 0x01, 0x00, 0x00, //0x00000e83 jne          LBB0_201
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x00000e89 cmpb         $108, $2(%rdi,%r9)
	0x0f, 0x85, 0x05, 0x01, 0x00, 0x00, //0x00000e8f jne          LBB0_266
	0x31, 0xc9, //0x00000e95 xorl         %ecx, %ecx
	0x41, 0x80, 0x7c, 0x39, 0x03, 0x6c, //0x00000e97 cmpb         $108, $3(%r9,%rdi)
	0xe9, 0x70, 0x00, 0x00, 0x00, //0x00000e9d jmp          LBB0_192
	//0x00000ea2 LBB0_184
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000ea2 movq         $-2, %rax
	0x80, 0xf9, 0x61, //0x00000ea9 cmpb         $97, %cl
	0x0f, 0x85, 0xd7, 0x00, 0x00, 0x00, //0x00000eac jne          LBB0_200
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x6c, //0x00000eb2 cmpb         $108, $2(%rdi,%r9)
	0x0f, 0x85, 0xdc, 0x00, 0x00, 0x00, //0x00000eb8 jne          LBB0_266
	0x42, 0x80, 0x7c, 0x0f, 0x03, 0x73, //0x00000ebe cmpb         $115, $3(%rdi,%r9)
	0x0f, 0x85, 0x53, 0x04, 0x00, 0x00, //0x00000ec4 jne          LBB0_265
	0x31, 0xc9, //0x00000eca xorl         %ecx, %ecx
	0x41, 0x80, 0x7c, 0x39, 0x04, 0x65, //0x00000ecc cmpb         $101, $4(%r9,%rdi)
	0x0f, 0x94, 0xc1, //0x00000ed2 sete         %cl
	0x4e, 0x8d, 0x34, 0x09, //0x00000ed5 leaq         (%rcx,%r9), %r14
	0x49, 0x83, 0xc6, 0x04, //0x00000ed9 addq         $4, %r14
	0xe9, 0xbf, 0x00, 0x00, 0x00, //0x00000edd jmp          LBB0_268
	//0x00000ee2 LBB0_188
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000ee2 movq         $-2, %rax
	0x80, 0xf9, 0x74, //0x00000ee9 cmpb         $116, %cl
	0x0f, 0x85, 0xac, 0x00, 0x00, 0x00, //0x00000eec jne          LBB0_267
	0x42, 0x80, 0x7c, 0x0f, 0x01, 0x72, //0x00000ef2 cmpb         $114, $1(%rdi,%r9)
	0x0f, 0x85, 0x93, 0x00, 0x00, 0x00, //0x00000ef8 jne          LBB0_201
	0x42, 0x80, 0x7c, 0x0f, 0x02, 0x75, //0x00000efe cmpb         $117, $2(%rdi,%r9)
	0x0f, 0x85, 0x90, 0x00, 0x00, 0x00, //0x00000f04 jne          LBB0_266
	0x31, 0xc9, //0x00000f0a xorl         %ecx, %ecx
	0x41, 0x80, 0x7c, 0x39, 0x03, 0x65, //0x00000f0c cmpb         $101, $3(%r9,%rdi)
	//0x00000f12 LBB0_192
	0x0f, 0x94, 0xc1, //0x00000f12 sete         %cl
	0x4e, 0x8d, 0x34, 0x09, //0x00000f15 leaq         (%rcx,%r9), %r14
	0x49, 0x83, 0xc6, 0x03, //0x00000f19 addq         $3, %r14
	0xe9, 0x7f, 0x00, 0x00, 0x00, //0x00000f1d jmp          LBB0_268
	//0x00000f22 LBB0_193
	0x49, 0x8d, 0x4f, 0xff, //0x00000f22 leaq         $-1(%r15), %rcx
	0x49, 0x39, 0xca, //0x00000f26 cmpq         %rcx, %r10
	0x49, 0xf7, 0xd7, //0x00000f29 notq         %r15
	0x4d, 0x0f, 0x45, 0xf8, //0x00000f2c cmovneq      %r8, %r15
	0x84, 0xc0, //0x00000f30 testb        %al, %al
	0x4d, 0x0f, 0x44, 0xf8, //0x00000f32 cmoveq       %r8, %r15
	0x4c, 0x89, 0xf8, //0x00000f36 movq         %r15, %rax
	0xe9, 0x28, 0xfd, 0xff, 0xff, //0x00000f39 jmp          LBB0_156
	//0x00000f3e LBB0_194
	0xb8, 0x40, 0x00, 0x00, 0x00, //0x00000f3e movl         $64, %eax
	//0x00000f43 LBB0_195
	0x4d, 0x89, 0xdd, //0x00000f43 movq         %r11, %r13
	0x48, 0x39, 0xc8, //0x00000f46 cmpq         %rcx, %rax
	0x0f, 0x82, 0xe1, 0x22, 0x00, 0x00, //0x00000f49 jb           LBB0_687
	0x49, 0x89, 0xcb, //0x00000f4f movq         %rcx, %r11
	0x4d, 0x29, 0xe3, //0x00000f52 subq         %r12, %r11
	//0x00000f55 LBB0_197
	0x4d, 0x85, 0xdb, //0x00000f55 testq        %r11, %r11
	0x0f, 0x88, 0xd9, 0x22, 0x00, 0x00, //0x00000f58 js           LBB0_688
	0x49, 0x89, 0x55, 0x10, //0x00000f5e movq         %rdx, $16(%r13)
	0x49, 0xc7, 0x45, 0x00, 0x07, 0x00, 0x00, 0x00, //0x00000f62 movq         $7, (%r13)
	0x4d, 0x39, 0xd8, //0x00000f6a cmpq         %r11, %r8
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000f6d movq         $-1, %rax
	0x49, 0x0f, 0x4c, 0xc0, //0x00000f74 cmovlq       %r8, %rax
	0x49, 0x89, 0x45, 0x18, //0x00000f78 movq         %rax, $24(%r13)
	0xe9, 0x87, 0x03, 0x00, 0x00, //0x00000f7c jmp          LBB0_263
	//0x00000f81 LBB0_199
	0x4c, 0x89, 0xf1, //0x00000f81 movq         %r14, %rcx
	0xe9, 0x38, 0x00, 0x00, 0x00, //0x00000f84 jmp          LBB0_205
	//0x00000f89 LBB0_200
	0x49, 0x89, 0xd6, //0x00000f89 movq         %rdx, %r14
	0xe9, 0x10, 0x00, 0x00, 0x00, //0x00000f8c jmp          LBB0_268
	//0x00000f91 LBB0_201
	0x49, 0x83, 0xc1, 0x01, //0x00000f91 addq         $1, %r9
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00000f95 jmp          LBB0_267
	//0x00000f9a LBB0_266
	0x49, 0x83, 0xc1, 0x02, //0x00000f9a addq         $2, %r9
	//0x00000f9e LBB0_267
	0x4d, 0x89, 0xce, //0x00000f9e movq         %r9, %r14
	//0x00000fa1 LBB0_268
	0x49, 0x89, 0x45, 0x00, //0x00000fa1 movq         %rax, (%r13)
	0x4d, 0x89, 0xf3, //0x00000fa5 movq         %r14, %r11
	0xe9, 0x5b, 0x03, 0x00, 0x00, //0x00000fa8 jmp          LBB0_263
	//0x00000fad LBB0_202
	0x4d, 0x89, 0xd0, //0x00000fad movq         %r10, %r8
	0xe9, 0x42, 0xfc, 0xff, 0xff, //0x00000fb0 jmp          LBB0_145
	//0x00000fb5 LBB0_203
	0x0f, 0xbc, 0xc9, //0x00000fb5 bsfl         %ecx, %ecx
	0xe9, 0xd5, 0x02, 0x00, 0x00, //0x00000fb8 jmp          LBB0_257
	//0x00000fbd LBB0_204
	0x48, 0x83, 0xc1, 0xff, //0x00000fbd addq         $-1, %rcx
	//0x00000fc1 LBB0_205
	0x31, 0xf6, //0x00000fc1 xorl         %esi, %esi
	0x85, 0xd2, //0x00000fc3 testl        %edx, %edx
	0x40, 0x0f, 0x9f, 0xc6, //0x00000fc5 setg         %sil
	0x89, 0x75, 0x98, //0x00000fc9 movl         %esi, $-104(%rbp)
	0x4d, 0x85, 0xd2, //0x00000fcc testq        %r10, %r10
	0x4c, 0x89, 0x7d, 0xb8, //0x00000fcf movq         %r15, $-72(%rbp)
	0x0f, 0x85, 0x45, 0x00, 0x00, 0x00, //0x00000fd3 jne          LBB0_214
	0x85, 0xd2, //0x00000fd9 testl        %edx, %edx
	0x0f, 0x85, 0x3d, 0x00, 0x00, 0x00, //0x00000fdb jne          LBB0_214
	0x4c, 0x39, 0xf1, //0x00000fe1 cmpq         %r14, %rcx
	0x0f, 0x83, 0x2d, 0x00, 0x00, 0x00, //0x00000fe4 jae          LBB0_212
	0x41, 0x89, 0xcf, //0x00000fea movl         %ecx, %r15d
	0x45, 0x29, 0xf7, //0x00000fed subl         %r14d, %r15d
	0x31, 0xc0, //0x00000ff0 xorl         %eax, %eax
	0x31, 0xd2, //0x00000ff2 xorl         %edx, %edx
	//0x00000ff4 LBB0_209
	0x41, 0x80, 0x3c, 0x0b, 0x30, //0x00000ff4 cmpb         $48, (%r11,%rcx)
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x00000ff9 jne          LBB0_213
	0x48, 0x83, 0xc1, 0x01, //0x00000fff addq         $1, %rcx
	0x83, 0xc2, 0xff, //0x00001003 addl         $-1, %edx
	0x49, 0x39, 0xce, //0x00001006 cmpq         %rcx, %r14
	0x0f, 0x85, 0xe5, 0xff, 0xff, 0xff, //0x00001009 jne          LBB0_209
	0x45, 0x31, 0xd2, //0x0000100f xorl         %r10d, %r10d
	0xe9, 0x26, 0x01, 0x00, 0x00, //0x00001012 jmp          LBB0_235
	//0x00001017 LBB0_212
	0x31, 0xd2, //0x00001017 xorl         %edx, %edx
	0x31, 0xc0, //0x00001019 xorl         %eax, %eax
	//0x0000101b LBB0_213
	0x45, 0x31, 0xd2, //0x0000101b xorl         %r10d, %r10d
	//0x0000101e LBB0_214
	0x4c, 0x39, 0xf1, //0x0000101e cmpq         %r14, %rcx
	0x0f, 0x83, 0x48, 0x00, 0x00, 0x00, //0x00001021 jae          LBB0_220
	0x83, 0xf8, 0x12, //0x00001027 cmpl         $18, %eax
	0x0f, 0x8f, 0x3f, 0x00, 0x00, 0x00, //0x0000102a jg           LBB0_220
	0xbe, 0xd0, 0xff, 0xff, 0xff, //0x00001030 movl         $4294967248, %esi
	//0x00001035 LBB0_217
	0x41, 0x0f, 0xb6, 0x1c, 0x0b, //0x00001035 movzbl       (%r11,%rcx), %ebx
	0x8d, 0x7b, 0xd0, //0x0000103a leal         $-48(%rbx), %edi
	0x40, 0x80, 0xff, 0x09, //0x0000103d cmpb         $9, %dil
	0x0f, 0x87, 0x28, 0x00, 0x00, 0x00, //0x00001041 ja           LBB0_220
	0x4b, 0x8d, 0x3c, 0x92, //0x00001047 leaq         (%r10,%r10,4), %rdi
	0x01, 0xf3, //0x0000104b addl         %esi, %ebx
	0x4c, 0x8d, 0x14, 0x7b, //0x0000104d leaq         (%rbx,%rdi,2), %r10
	0x83, 0xc2, 0xff, //0x00001051 addl         $-1, %edx
	0x48, 0x83, 0xc1, 0x01, //0x00001054 addq         $1, %rcx
	0x4c, 0x39, 0xf1, //0x00001058 cmpq         %r14, %rcx
	0x0f, 0x83, 0x0e, 0x00, 0x00, 0x00, //0x0000105b jae          LBB0_220
	0x8d, 0x78, 0x01, //0x00001061 leal         $1(%rax), %edi
	0x83, 0xf8, 0x12, //0x00001064 cmpl         $18, %eax
	0x89, 0xf8, //0x00001067 movl         %edi, %eax
	0x0f, 0x8c, 0xc6, 0xff, 0xff, 0xff, //0x00001069 jl           LBB0_217
	//0x0000106f LBB0_220
	0x4c, 0x39, 0xf1, //0x0000106f cmpq         %r14, %rcx
	0x0f, 0x83, 0xb0, 0x00, 0x00, 0x00, //0x00001072 jae          LBB0_233
	0x41, 0x8a, 0x04, 0x0b, //0x00001078 movb         (%r11,%rcx), %al
	0x8d, 0x70, 0xd0, //0x0000107c leal         $-48(%rax), %esi
	0x40, 0x80, 0xfe, 0x09, //0x0000107f cmpb         $9, %sil
	0x0f, 0x87, 0x2b, 0x00, 0x00, 0x00, //0x00001083 ja           LBB0_226
	0x49, 0x8d, 0x76, 0xff, //0x00001089 leaq         $-1(%r14), %rsi
	//0x0000108d LBB0_223
	0x48, 0x39, 0xce, //0x0000108d cmpq         %rcx, %rsi
	0x0f, 0x84, 0x9d, 0x00, 0x00, 0x00, //0x00001090 je           LBB0_234
	0x41, 0x0f, 0xb6, 0x44, 0x0b, 0x01, //0x00001096 movzbl       $1(%r11,%rcx), %eax
	0x8d, 0x78, 0xd0, //0x0000109c leal         $-48(%rax), %edi
	0x48, 0x83, 0xc1, 0x01, //0x0000109f addq         $1, %rcx
	0x40, 0x80, 0xff, 0x09, //0x000010a3 cmpb         $9, %dil
	0x0f, 0x86, 0xe0, 0xff, 0xff, 0xff, //0x000010a7 jbe          LBB0_223
	0xc7, 0x45, 0x98, 0x01, 0x00, 0x00, 0x00, //0x000010ad movl         $1, $-104(%rbp)
	//0x000010b4 LBB0_226
	0x0c, 0x20, //0x000010b4 orb          $32, %al
	0x3c, 0x65, //0x000010b6 cmpb         $101, %al
	0x0f, 0x85, 0x6a, 0x00, 0x00, 0x00, //0x000010b8 jne          LBB0_233
	0x48, 0x8d, 0x71, 0x01, //0x000010be leaq         $1(%rcx), %rsi
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x000010c2 movq         $8, (%r13)
	0x4c, 0x39, 0xf6, //0x000010ca cmpq         %r14, %rsi
	0x0f, 0x83, 0x45, 0x00, 0x00, 0x00, //0x000010cd jae          LBB0_232
	0x41, 0x8a, 0x1c, 0x33, //0x000010d3 movb         (%r11,%rsi), %bl
	0x80, 0xfb, 0x2d, //0x000010d7 cmpb         $45, %bl
	0x0f, 0x84, 0x0f, 0x00, 0x00, 0x00, //0x000010da je           LBB0_230
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000010e0 movl         $1, %r11d
	0x80, 0xfb, 0x2b, //0x000010e6 cmpb         $43, %bl
	0x0f, 0x85, 0x30, 0x05, 0x00, 0x00, //0x000010e9 jne          LBB0_299
	//0x000010ef LBB0_230
	0x48, 0x83, 0xc1, 0x02, //0x000010ef addq         $2, %rcx
	0x4c, 0x39, 0xf1, //0x000010f3 cmpq         %r14, %rcx
	0x0f, 0x83, 0x1c, 0x00, 0x00, 0x00, //0x000010f6 jae          LBB0_232
	0x31, 0xc0, //0x000010fc xorl         %eax, %eax
	0x80, 0xfb, 0x2b, //0x000010fe cmpb         $43, %bl
	0x0f, 0x94, 0xc0, //0x00001101 sete         %al
	0x44, 0x8d, 0x1c, 0x00, //0x00001104 leal         (%rax,%rax), %r11d
	0x41, 0x83, 0xc3, 0xff, //0x00001108 addl         $-1, %r11d
	0x48, 0x8b, 0x45, 0xc8, //0x0000110c movq         $-56(%rbp), %rax
	0x8a, 0x1c, 0x08, //0x00001110 movb         (%rax,%rcx), %bl
	0xe9, 0x0a, 0x05, 0x00, 0x00, //0x00001113 jmp          LBB0_300
	//0x00001118 LBB0_232
	0x49, 0xc7, 0x45, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001118 movq         $-1, (%r13)
	0x4d, 0x89, 0xf3, //0x00001120 movq         %r14, %r11
	0xe9, 0xe0, 0x01, 0x00, 0x00, //0x00001123 jmp          LBB0_263
	//0x00001128 LBB0_233
	0x41, 0x89, 0xd7, //0x00001128 movl         %edx, %r15d
	0x49, 0x89, 0xcb, //0x0000112b movq         %rcx, %r11
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x0000112e jmp          LBB0_236
	//0x00001133 LBB0_234
	0xc7, 0x45, 0x98, 0x01, 0x00, 0x00, 0x00, //0x00001133 movl         $1, $-104(%rbp)
	0x41, 0x89, 0xd7, //0x0000113a movl         %edx, %r15d
	//0x0000113d LBB0_235
	0x4d, 0x89, 0xf3, //0x0000113d movq         %r14, %r11
	//0x00001140 LBB0_236
	0x80, 0x7d, 0xb0, 0x00, //0x00001140 cmpb         $0, $-80(%rbp)
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00001144 je           LBB0_241
	0x45, 0x85, 0xff, //0x0000114a testl        %r15d, %r15d
	0x0f, 0x85, 0x25, 0x00, 0x00, 0x00, //0x0000114d jne          LBB0_240
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001153 movabsq      $-9223372036854775808, %rax
	0x49, 0x63, 0xcc, //0x0000115d movslq       %r12d, %rcx
	0x4d, 0x85, 0xd2, //0x00001160 testq        %r10, %r10
	0x0f, 0x89, 0x34, 0x01, 0x00, 0x00, //0x00001163 jns          LBB0_258
	0x4c, 0x89, 0xd2, //0x00001169 movq         %r10, %rdx
	0x48, 0x21, 0xca, //0x0000116c andq         %rcx, %rdx
	0x48, 0x39, 0xc2, //0x0000116f cmpq         %rax, %rdx
	0x0f, 0x84, 0x25, 0x01, 0x00, 0x00, //0x00001172 je           LBB0_258
	//0x00001178 LBB0_240
	0x49, 0xc7, 0x45, 0x00, 0x08, 0x00, 0x00, 0x00, //0x00001178 movq         $8, (%r13)
	//0x00001180 LBB0_241
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001180 movabsq      $-9223372036854775808, %rdi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x0000118a movabsq      $4503599627370495, %rax
	0x48, 0x8d, 0x50, 0x01, //0x00001194 leaq         $1(%rax), %rdx
	0x49, 0x39, 0xd2, //0x00001198 cmpq         %rdx, %r10
	0x0f, 0x83, 0xc3, 0x00, 0x00, 0x00, //0x0000119b jae          LBB0_253
	0xc4, 0xc1, 0xf9, 0x6e, 0xc2, //0x000011a1 vmovq        %r10, %xmm0
	0xc5, 0xf9, 0x62, 0x05, 0x42, 0xf0, 0xff, 0xff, //0x000011a6 vpunpckldq   $-4030(%rip), %xmm0, %xmm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0x4a, 0xf0, 0xff, 0xff, //0x000011ae vsubpd       $-4022(%rip), %xmm0, %xmm0  /* LCPI0_19+0(%rip) */
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x000011b6 vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x000011bc vaddsd       %xmm0, %xmm1, %xmm0
	0x41, 0xc1, 0xec, 0x1f, //0x000011c0 shrl         $31, %r12d
	0x49, 0xc1, 0xe4, 0x3f, //0x000011c4 shlq         $63, %r12
	0xc4, 0xc1, 0xf9, 0x6e, 0xcc, //0x000011c8 vmovq        %r12, %xmm1
	0xc5, 0xf9, 0x56, 0xc1, //0x000011cd vorpd        %xmm1, %xmm0, %xmm0
	0x45, 0x85, 0xff, //0x000011d1 testl        %r15d, %r15d
	0x0f, 0x84, 0x4c, 0x1d, 0x00, 0x00, //0x000011d4 je           LBB0_640
	0x4d, 0x85, 0xd2, //0x000011da testq        %r10, %r10
	0x0f, 0x84, 0x43, 0x1d, 0x00, 0x00, //0x000011dd je           LBB0_640
	0x41, 0x8d, 0x47, 0xff, //0x000011e3 leal         $-1(%r15), %eax
	0x83, 0xf8, 0x24, //0x000011e7 cmpl         $36, %eax
	0x0f, 0x87, 0x55, 0x00, 0x00, 0x00, //0x000011ea ja           LBB0_251
	0x44, 0x89, 0xf8, //0x000011f0 movl         %r15d, %eax
	0x41, 0x83, 0xff, 0x17, //0x000011f3 cmpl         $23, %r15d
	0x0f, 0x8c, 0x15, 0x00, 0x00, 0x00, //0x000011f7 jl           LBB0_247
	0x41, 0x8d, 0x47, 0xea, //0x000011fd leal         $-22(%r15), %eax
	0x48, 0x8d, 0x0d, 0x58, 0x23, 0x00, 0x00, //0x00001201 leaq         $9048(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x04, 0xc1, //0x00001208 vmulsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xb8, 0x16, 0x00, 0x00, 0x00, //0x0000120d movl         $22, %eax
	//0x00001212 LBB0_247
	0xc5, 0xf9, 0x2e, 0x05, 0xf6, 0xef, 0xff, 0xff, //0x00001212 vucomisd     $-4106(%rip), %xmm0  /* LCPI0_20+0(%rip) */
	0x0f, 0x87, 0x06, 0x01, 0x00, 0x00, //0x0000121a ja           LBB0_269
	0xc5, 0xfb, 0x10, 0x0d, 0xf0, 0xef, 0xff, 0xff, //0x00001220 vmovsd       $-4112(%rip), %xmm1  /* LCPI0_21+0(%rip) */
	0xc5, 0xf9, 0x2e, 0xc8, //0x00001228 vucomisd     %xmm0, %xmm1
	0x0f, 0x87, 0xf4, 0x00, 0x00, 0x00, //0x0000122c ja           LBB0_269
	0x89, 0xc0, //0x00001232 movl         %eax, %eax
	0x48, 0x8d, 0x0d, 0x25, 0x23, 0x00, 0x00, //0x00001234 leaq         $8997(%rip), %rcx  /* _P10_TAB+0(%rip) */
	0xc5, 0xfb, 0x59, 0x04, 0xc1, //0x0000123b vmulsd       (%rcx,%rax,8), %xmm0, %xmm0
	0xe9, 0xe1, 0x1c, 0x00, 0x00, //0x00001240 jmp          LBB0_640
	//0x00001245 LBB0_251
	0x41, 0x83, 0xff, 0xea, //0x00001245 cmpl         $-22, %r15d
	0x0f, 0x82, 0x15, 0x00, 0x00, 0x00, //0x00001249 jb           LBB0_253
	0x41, 0xf7, 0xdf, //0x0000124f negl         %r15d
	0x48, 0x8d, 0x05, 0x07, 0x23, 0x00, 0x00, //0x00001252 leaq         $8967(%rip), %rax  /* _P10_TAB+0(%rip) */
	0xc4, 0xa1, 0x7b, 0x5e, 0x04, 0xf8, //0x00001259 vdivsd       (%rax,%r15,8), %xmm0, %xmm0
	0xe9, 0xc2, 0x1c, 0x00, 0x00, //0x0000125f jmp          LBB0_640
	//0x00001264 LBB0_253
	0x48, 0x89, 0x55, 0x90, //0x00001264 movq         %rdx, $-112(%rbp)
	0x41, 0x8d, 0x87, 0x5c, 0x01, 0x00, 0x00, //0x00001268 leal         $348(%r15), %eax
	0x3d, 0xb7, 0x02, 0x00, 0x00, //0x0000126f cmpl         $695, %eax
	0x0f, 0x87, 0xcd, 0x01, 0x00, 0x00, //0x00001274 ja           LBB0_280
	0x4d, 0x85, 0xd2, //0x0000127a testq        %r10, %r10
	0x0f, 0x84, 0xb7, 0x00, 0x00, 0x00, //0x0000127d je           LBB0_270
	//0x00001283 LBB0_255
	0x49, 0x0f, 0xbd, 0xca, //0x00001283 bsrq         %r10, %rcx
	0x48, 0x83, 0xf1, 0x3f, //0x00001287 xorq         $63, %rcx
	0xe9, 0xaf, 0x00, 0x00, 0x00, //0x0000128b jmp          LBB0_271
	//0x00001290 LBB0_256
	0x89, 0xc9, //0x00001290 movl         %ecx, %ecx
	//0x00001292 LBB0_257
	0x48, 0xf7, 0xd0, //0x00001292 notq         %rax
	0x48, 0x29, 0xc8, //0x00001295 subq         %rcx, %rax
	0xe9, 0xc9, 0xf9, 0xff, 0xff, //0x00001298 jmp          LBB0_156
	//0x0000129d LBB0_258
	0xc4, 0xc1, 0xf9, 0x6e, 0xc2, //0x0000129d vmovq        %r10, %xmm0
	0x4c, 0x0f, 0xaf, 0xd1, //0x000012a2 imulq        %rcx, %r10
	0x4d, 0x89, 0x55, 0x10, //0x000012a6 movq         %r10, $16(%r13)
	0xc5, 0xf9, 0x62, 0x05, 0x3e, 0xef, 0xff, 0xff, //0x000012aa vpunpckldq   $-4290(%rip), %xmm0, %xmm0  /* LCPI0_18+0(%rip) */
	0xc5, 0xf9, 0x5c, 0x05, 0x46, 0xef, 0xff, 0xff, //0x000012b2 vsubpd       $-4282(%rip), %xmm0, %xmm0  /* LCPI0_19+0(%rip) */
	0xc4, 0xe3, 0x79, 0x05, 0xc8, 0x01, //0x000012ba vpermilpd    $1, %xmm0, %xmm1
	0xc5, 0xf3, 0x58, 0xc0, //0x000012c0 vaddsd       %xmm0, %xmm1, %xmm0
	0x48, 0x21, 0xc8, //0x000012c4 andq         %rcx, %rax
	0xc4, 0xe1, 0xf9, 0x7e, 0xc1, //0x000012c7 vmovq        %xmm0, %rcx
	0x48, 0x09, 0xc1, //0x000012cc orq          %rax, %rcx
	0x49, 0x89, 0x4d, 0x08, //0x000012cf movq         %rcx, $8(%r13)
	0xe9, 0x30, 0x00, 0x00, 0x00, //0x000012d3 jmp          LBB0_263
	//0x000012d8 LBB0_259
	0x48, 0x8b, 0x4d, 0xb8, //0x000012d8 movq         $-72(%rbp), %rcx
	0x48, 0x03, 0x4d, 0xc0, //0x000012dc addq         $-64(%rbp), %rcx
	0x4c, 0x29, 0xc1, //0x000012e0 subq         %r8, %rcx
	0x48, 0xf7, 0xd0, //0x000012e3 notq         %rax
	0x48, 0x01, 0xc8, //0x000012e6 addq         %rcx, %rax
	0xe9, 0x78, 0xf9, 0xff, 0xff, //0x000012e9 jmp          LBB0_156
	//0x000012ee LBB0_260
	0x49, 0x29, 0xf9, //0x000012ee subq         %rdi, %r9
	0x49, 0x01, 0xc9, //0x000012f1 addq         %rcx, %r9
	0x4d, 0x39, 0xf1, //0x000012f4 cmpq         %r14, %r9
	0x0f, 0x82, 0xee, 0xf0, 0xff, 0xff, //0x000012f7 jb           LBB0_31
	//0x000012fd LBB0_261
	0x49, 0xc7, 0x45, 0x00, 0x01, 0x00, 0x00, 0x00, //0x000012fd movq         $1, (%r13)
	//0x00001305 LBB0_262
	0x49, 0x89, 0xd3, //0x00001305 movq         %rdx, %r11
	//0x00001308 LBB0_263
	0x4c, 0x89, 0xd8, //0x00001308 movq         %r11, %rax
	0x48, 0x83, 0xc4, 0x50, //0x0000130b addq         $80, %rsp
	0x5b, //0x0000130f popq         %rbx
	0x41, 0x5c, //0x00001310 popq         %r12
	0x41, 0x5d, //0x00001312 popq         %r13
	0x41, 0x5e, //0x00001314 popq         %r14
	0x41, 0x5f, //0x00001316 popq         %r15
	0x5d, //0x00001318 popq         %rbp
	0xc5, 0xf8, 0x77, //0x00001319 vzeroupper   
	0xc3, //0x0000131c retq         
	//0x0000131d LBB0_265
	0x49, 0x83, 0xc1, 0x03, //0x0000131d addq         $3, %r9
	0xe9, 0x78, 0xfc, 0xff, 0xff, //0x00001321 jmp          LBB0_267
	//0x00001326 LBB0_269
	0x48, 0x89, 0x55, 0x90, //0x00001326 movq         %rdx, $-112(%rbp)
	0x41, 0x8d, 0x87, 0x5c, 0x01, 0x00, 0x00, //0x0000132a leal         $348(%r15), %eax
	0x4d, 0x85, 0xd2, //0x00001331 testq        %r10, %r10
	0x0f, 0x85, 0x49, 0xff, 0xff, 0xff, //0x00001334 jne          LBB0_255
	//0x0000133a LBB0_270
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x0000133a movl         $64, %ecx
	//0x0000133f LBB0_271
	0x4c, 0x89, 0xd3, //0x0000133f movq         %r10, %rbx
	0x49, 0x89, 0xce, //0x00001342 movq         %rcx, %r14
	0x48, 0xd3, 0xe3, //0x00001345 shlq         %cl, %rbx
	0x89, 0xc0, //0x00001348 movl         %eax, %eax
	0x48, 0xc1, 0xe0, 0x04, //0x0000134a shlq         $4, %rax
	0x48, 0x8d, 0x0d, 0xcb, 0x22, 0x00, 0x00, //0x0000134e leaq         $8907(%rip), %rcx  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0x89, 0x45, 0xb0, //0x00001355 movq         %rax, $-80(%rbp)
	0x48, 0x8b, 0x44, 0x08, 0x08, //0x00001359 movq         $8(%rax,%rcx), %rax
	0x48, 0x89, 0x45, 0x88, //0x0000135e movq         %rax, $-120(%rbp)
	0x48, 0xf7, 0xe3, //0x00001362 mulq         %rbx
	0x48, 0x89, 0xc6, //0x00001365 movq         %rax, %rsi
	0x49, 0x89, 0xd4, //0x00001368 movq         %rdx, %r12
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x0000136b andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x00001371 cmpq         $511, %rdx
	0x0f, 0x85, 0x57, 0x00, 0x00, 0x00, //0x00001378 jne          LBB0_276
	0x48, 0x89, 0xd9, //0x0000137e movq         %rbx, %rcx
	0x48, 0xf7, 0xd1, //0x00001381 notq         %rcx
	0x48, 0x39, 0xce, //0x00001384 cmpq         %rcx, %rsi
	0x0f, 0x86, 0x48, 0x00, 0x00, 0x00, //0x00001387 jbe          LBB0_276
	0x48, 0x89, 0xd8, //0x0000138d movq         %rbx, %rax
	0x48, 0x8b, 0x55, 0xb0, //0x00001390 movq         $-80(%rbp), %rdx
	0x48, 0x89, 0xf3, //0x00001394 movq         %rsi, %rbx
	0x48, 0x8d, 0x35, 0x82, 0x22, 0x00, 0x00, //0x00001397 leaq         $8834(%rip), %rsi  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x32, //0x0000139e mulq         (%rdx,%rsi)
	0x48, 0x89, 0xde, //0x000013a2 movq         %rbx, %rsi
	0x48, 0x01, 0xd6, //0x000013a5 addq         %rdx, %rsi
	0x49, 0x83, 0xd4, 0x00, //0x000013a8 adcq         $0, %r12
	0x44, 0x89, 0xe2, //0x000013ac movl         %r12d, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x000013af andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x000013b5 cmpq         $511, %rdx
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x000013bc jne          LBB0_276
	0x48, 0x83, 0xfe, 0xff, //0x000013c2 cmpq         $-1, %rsi
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000013c6 jne          LBB0_276
	0x48, 0x39, 0xc8, //0x000013cc cmpq         %rcx, %rax
	0x0f, 0x87, 0x72, 0x00, 0x00, 0x00, //0x000013cf ja           LBB0_280
	//0x000013d5 LBB0_276
	0x4c, 0x89, 0xe0, //0x000013d5 movq         %r12, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x000013d8 shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x000013dc leal         $9(%rax), %ecx
	0x49, 0xd3, 0xec, //0x000013df shrq         %cl, %r12
	0x48, 0x85, 0xf6, //0x000013e2 testq        %rsi, %rsi
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x000013e5 jne          LBB0_279
	0x48, 0x85, 0xd2, //0x000013eb testq        %rdx, %rdx
	0x0f, 0x85, 0x0f, 0x00, 0x00, 0x00, //0x000013ee jne          LBB0_279
	0x44, 0x89, 0xe1, //0x000013f4 movl         %r12d, %ecx
	0x83, 0xe1, 0x03, //0x000013f7 andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x000013fa cmpl         $1, %ecx
	0x0f, 0x84, 0x44, 0x00, 0x00, 0x00, //0x000013fd je           LBB0_280
	//0x00001403 LBB0_279
	0x41, 0x69, 0xcf, 0x6a, 0x52, 0x03, 0x00, //0x00001403 imull        $217706, %r15d, %ecx
	0xc1, 0xf9, 0x10, //0x0000140a sarl         $16, %ecx
	0x81, 0xc1, 0x3f, 0x04, 0x00, 0x00, //0x0000140d addl         $1087, %ecx
	0x4c, 0x63, 0xf9, //0x00001413 movslq       %ecx, %r15
	0x4c, 0x89, 0xfe, //0x00001416 movq         %r15, %rsi
	0x4c, 0x29, 0xf6, //0x00001419 subq         %r14, %rsi
	0x44, 0x89, 0xe2, //0x0000141c movl         %r12d, %edx
	0x83, 0xe2, 0x01, //0x0000141f andl         $1, %edx
	0x4c, 0x01, 0xe2, //0x00001422 addq         %r12, %rdx
	0x48, 0x89, 0xd1, //0x00001425 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x36, //0x00001428 shrq         $54, %rcx
	0x48, 0x01, 0xc6, //0x0000142c addq         %rax, %rsi
	0x48, 0x83, 0xf9, 0x01, //0x0000142f cmpq         $1, %rcx
	0x48, 0x83, 0xde, 0x00, //0x00001433 sbbq         $0, %rsi
	0x48, 0x8d, 0x46, 0xff, //0x00001437 leaq         $-1(%rsi), %rax
	0x48, 0x3d, 0xfd, 0x07, 0x00, 0x00, //0x0000143b cmpq         $2045, %rax
	0x0f, 0x86, 0x50, 0x00, 0x00, 0x00, //0x00001441 jbe          LBB0_285
	//0x00001447 LBB0_280
	0x4c, 0x89, 0xd8, //0x00001447 movq         %r11, %rax
	0x4c, 0x29, 0xc8, //0x0000144a subq         %r9, %rax
	0x48, 0x89, 0x45, 0x98, //0x0000144d movq         %rax, $-104(%rbp)
	0x48, 0x8b, 0x55, 0xc0, //0x00001451 movq         $-64(%rbp), %rdx
	0x48, 0x85, 0xd2, //0x00001455 testq        %rdx, %rdx
	0x4c, 0x8b, 0x55, 0xb8, //0x00001458 movq         $-72(%rbp), %r10
	0x0f, 0x84, 0xd9, 0x04, 0x00, 0x00, //0x0000145c je           LBB0_336
	0x41, 0xc6, 0x00, 0x00, //0x00001462 movb         $0, (%r8)
	0x48, 0x83, 0xfa, 0x01, //0x00001466 cmpq         $1, %rdx
	0x0f, 0x84, 0xcb, 0x04, 0x00, 0x00, //0x0000146a je           LBB0_336
	0x48, 0x8d, 0x4a, 0xff, //0x00001470 leaq         $-1(%rdx), %rcx
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001474 movl         $1, %eax
	0x48, 0x83, 0xf9, 0x10, //0x00001479 cmpq         $16, %rcx
	0x0f, 0x82, 0xa6, 0x04, 0x00, 0x00, //0x0000147d jb           LBB0_335
	0x48, 0x81, 0xf9, 0x80, 0x00, 0x00, 0x00, //0x00001483 cmpq         $128, %rcx
	0x0f, 0x83, 0xf1, 0x01, 0x00, 0x00, //0x0000148a jae          LBB0_305
	0x31, 0xd2, //0x00001490 xorl         %edx, %edx
	0xe9, 0x48, 0x03, 0x00, 0x00, //0x00001492 jmp          LBB0_317
	//0x00001497 LBB0_285
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x00001497 movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x000014a1 cmpq         %rax, %rdx
	0xb1, 0x02, //0x000014a4 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000014a6 sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x000014a9 shrq         %cl, %rdx
	0x48, 0xc1, 0xe6, 0x34, //0x000014ac shlq         $52, %rsi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000014b0 movabsq      $4503599627370495, %rax
	0x48, 0x21, 0xc2, //0x000014ba andq         %rax, %rdx
	0x48, 0x09, 0xf2, //0x000014bd orq          %rsi, %rdx
	0x48, 0x89, 0xd0, //0x000014c0 movq         %rdx, %rax
	0x48, 0x09, 0xf8, //0x000014c3 orq          %rdi, %rax
	0x80, 0x7d, 0xa4, 0x2d, //0x000014c6 cmpb         $45, $-92(%rbp)
	0x48, 0x0f, 0x45, 0xc2, //0x000014ca cmovneq      %rdx, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc0, //0x000014ce vmovq        %rax, %xmm0
	0x83, 0x7d, 0x98, 0x00, //0x000014d3 cmpl         $0, $-104(%rbp)
	0x0f, 0x84, 0x49, 0x1a, 0x00, 0x00, //0x000014d7 je           LBB0_640
	0x41, 0xbe, 0x40, 0x00, 0x00, 0x00, //0x000014dd movl         $64, %r14d
	0x49, 0xff, 0xc2, //0x000014e3 incq         %r10
	0x0f, 0x84, 0x08, 0x00, 0x00, 0x00, //0x000014e6 je           LBB0_288
	0x4d, 0x0f, 0xbd, 0xf2, //0x000014ec bsrq         %r10, %r14
	0x49, 0x83, 0xf6, 0x3f, //0x000014f0 xorq         $63, %r14
	//0x000014f4 LBB0_288
	0x44, 0x89, 0xf1, //0x000014f4 movl         %r14d, %ecx
	0x49, 0xd3, 0xe2, //0x000014f7 shlq         %cl, %r10
	0x48, 0x8b, 0x45, 0x88, //0x000014fa movq         $-120(%rbp), %rax
	0x49, 0xf7, 0xe2, //0x000014fe mulq         %r10
	0x49, 0x89, 0xc4, //0x00001501 movq         %rax, %r12
	0x48, 0x89, 0xd3, //0x00001504 movq         %rdx, %rbx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001507 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000150d cmpq         $511, %rdx
	0x0f, 0x85, 0x50, 0x00, 0x00, 0x00, //0x00001514 jne          LBB0_293
	0x4c, 0x89, 0xd1, //0x0000151a movq         %r10, %rcx
	0x48, 0xf7, 0xd1, //0x0000151d notq         %rcx
	0x49, 0x39, 0xcc, //0x00001520 cmpq         %rcx, %r12
	0x0f, 0x86, 0x41, 0x00, 0x00, 0x00, //0x00001523 jbe          LBB0_293
	0x4c, 0x89, 0xd0, //0x00001529 movq         %r10, %rax
	0x48, 0x8b, 0x55, 0xb0, //0x0000152c movq         $-80(%rbp), %rdx
	0x48, 0x8d, 0x35, 0xe9, 0x20, 0x00, 0x00, //0x00001530 leaq         $8425(%rip), %rsi  /* _POW10_M128_TAB+0(%rip) */
	0x48, 0xf7, 0x24, 0x32, //0x00001537 mulq         (%rdx,%rsi)
	0x49, 0x01, 0xd4, //0x0000153b addq         %rdx, %r12
	0x48, 0x83, 0xd3, 0x00, //0x0000153e adcq         $0, %rbx
	0x89, 0xda, //0x00001542 movl         %ebx, %edx
	0x81, 0xe2, 0xff, 0x01, 0x00, 0x00, //0x00001544 andl         $511, %edx
	0x48, 0x81, 0xfa, 0xff, 0x01, 0x00, 0x00, //0x0000154a cmpq         $511, %rdx
	0x0f, 0x85, 0x13, 0x00, 0x00, 0x00, //0x00001551 jne          LBB0_293
	0x49, 0x83, 0xfc, 0xff, //0x00001557 cmpq         $-1, %r12
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x0000155b jne          LBB0_293
	0x48, 0x39, 0xc8, //0x00001561 cmpq         %rcx, %rax
	0x0f, 0x87, 0xdd, 0xfe, 0xff, 0xff, //0x00001564 ja           LBB0_280
	//0x0000156a LBB0_293
	0x48, 0x89, 0xd8, //0x0000156a movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x3f, //0x0000156d shrq         $63, %rax
	0x8d, 0x48, 0x09, //0x00001571 leal         $9(%rax), %ecx
	0x48, 0xd3, 0xeb, //0x00001574 shrq         %cl, %rbx
	0x4d, 0x85, 0xe4, //0x00001577 testq        %r12, %r12
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x0000157a jne          LBB0_296
	0x48, 0x85, 0xd2, //0x00001580 testq        %rdx, %rdx
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00001583 jne          LBB0_296
	0x89, 0xd9, //0x00001589 movl         %ebx, %ecx
	0x83, 0xe1, 0x03, //0x0000158b andl         $3, %ecx
	0x83, 0xf9, 0x01, //0x0000158e cmpl         $1, %ecx
	0x0f, 0x84, 0xb0, 0xfe, 0xff, 0xff, //0x00001591 je           LBB0_280
	//0x00001597 LBB0_296
	0x4d, 0x29, 0xf7, //0x00001597 subq         %r14, %r15
	0x89, 0xda, //0x0000159a movl         %ebx, %edx
	0x83, 0xe2, 0x01, //0x0000159c andl         $1, %edx
	0x48, 0x01, 0xda, //0x0000159f addq         %rbx, %rdx
	0x49, 0x01, 0xc7, //0x000015a2 addq         %rax, %r15
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x000015a5 movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x000015af cmpq         %rax, %rdx
	0x49, 0x83, 0xdf, 0x00, //0x000015b2 sbbq         $0, %r15
	0x49, 0x8d, 0x47, 0xff, //0x000015b6 leaq         $-1(%r15), %rax
	0x48, 0x3d, 0xfd, 0x07, 0x00, 0x00, //0x000015ba cmpq         $2045, %rax
	0x0f, 0x87, 0x81, 0xfe, 0xff, 0xff, //0x000015c0 ja           LBB0_280
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, //0x000015c6 movabsq      $18014398509481984, %rax
	0x48, 0x39, 0xc2, //0x000015d0 cmpq         %rax, %rdx
	0xb1, 0x02, //0x000015d3 movb         $2, %cl
	0x80, 0xd9, 0x00, //0x000015d5 sbbb         $0, %cl
	0x48, 0xd3, 0xea, //0x000015d8 shrq         %cl, %rdx
	0x49, 0xc1, 0xe7, 0x34, //0x000015db shlq         $52, %r15
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x000015df movabsq      $4503599627370495, %rax
	0x48, 0x21, 0xc2, //0x000015e9 andq         %rax, %rdx
	0x4c, 0x09, 0xfa, //0x000015ec orq          %r15, %rdx
	0x48, 0x89, 0xd0, //0x000015ef movq         %rdx, %rax
	0x48, 0x09, 0xf8, //0x000015f2 orq          %rdi, %rax
	0x80, 0x7d, 0xa4, 0x2d, //0x000015f5 cmpb         $45, $-92(%rbp)
	0x48, 0x0f, 0x45, 0xc2, //0x000015f9 cmovneq      %rdx, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc8, //0x000015fd vmovq        %rax, %xmm1
	0xc5, 0xf9, 0x2e, 0xc1, //0x00001602 vucomisd     %xmm1, %xmm0
	0x0f, 0x85, 0x3b, 0xfe, 0xff, 0xff, //0x00001606 jne          LBB0_280
	0x0f, 0x8b, 0x14, 0x19, 0x00, 0x00, //0x0000160c jnp          LBB0_640
	0xe9, 0x30, 0xfe, 0xff, 0xff, //0x00001612 jmp          LBB0_280
	//0x00001617 LBB0_298
	0x0f, 0xbc, 0xca, //0x00001617 bsfl         %edx, %ecx
	0xe9, 0x9c, 0x00, 0x00, 0x00, //0x0000161a jmp          LBB0_309
	//0x0000161f LBB0_299
	0x48, 0x89, 0xf1, //0x0000161f movq         %rsi, %rcx
	//0x00001622 LBB0_300
	0x8d, 0x73, 0xd0, //0x00001622 leal         $-48(%rbx), %esi
	0x40, 0x80, 0xfe, 0x09, //0x00001625 cmpb         $9, %sil
	0x0f, 0x87, 0x55, 0xee, 0xff, 0xff, //0x00001629 ja           LBB0_40
	0x45, 0x31, 0xff, //0x0000162f xorl         %r15d, %r15d
	0x4c, 0x39, 0xf1, //0x00001632 cmpq         %r14, %rcx
	0x0f, 0x83, 0xd4, 0x02, 0x00, 0x00, //0x00001635 jae          LBB0_333
	0x49, 0x8d, 0x76, 0xff, //0x0000163b leaq         $-1(%r14), %rsi
	0x45, 0x31, 0xff, //0x0000163f xorl         %r15d, %r15d
	//0x00001642 LBB0_303
	0x44, 0x89, 0xff, //0x00001642 movl         %r15d, %edi
	0x0f, 0xb6, 0xdb, //0x00001645 movzbl       %bl, %ebx
	0x41, 0x81, 0xff, 0x10, 0x27, 0x00, 0x00, //0x00001648 cmpl         $10000, %r15d
	0x8d, 0x04, 0xbf, //0x0000164f leal         (%rdi,%rdi,4), %eax
	0x44, 0x8d, 0x7c, 0x43, 0xd0, //0x00001652 leal         $-48(%rbx,%rax,2), %r15d
	0x44, 0x0f, 0x4d, 0xff, //0x00001657 cmovgel      %edi, %r15d
	0x48, 0x39, 0xce, //0x0000165b cmpq         %rcx, %rsi
	0x0f, 0x84, 0xa8, 0x02, 0x00, 0x00, //0x0000165e je           LBB0_332
	0x48, 0x8b, 0x45, 0xc8, //0x00001664 movq         $-56(%rbp), %rax
	0x0f, 0xb6, 0x5c, 0x08, 0x01, //0x00001668 movzbl       $1(%rax,%rcx), %ebx
	0x8d, 0x43, 0xd0, //0x0000166d leal         $-48(%rbx), %eax
	0x48, 0x83, 0xc1, 0x01, //0x00001670 addq         $1, %rcx
	0x3c, 0x0a, //0x00001674 cmpb         $10, %al
	0x0f, 0x82, 0xc6, 0xff, 0xff, 0xff, //0x00001676 jb           LBB0_303
	0xe9, 0x8e, 0x02, 0x00, 0x00, //0x0000167c jmp          LBB0_333
	//0x00001681 LBB0_305
	0x48, 0x89, 0xca, //0x00001681 movq         %rcx, %rdx
	0x48, 0x83, 0xe2, 0x80, //0x00001684 andq         $-128, %rdx
	0x48, 0x8d, 0x5a, 0x80, //0x00001688 leaq         $-128(%rdx), %rbx
	0x48, 0x89, 0xde, //0x0000168c movq         %rbx, %rsi
	0x48, 0xc1, 0xee, 0x07, //0x0000168f shrq         $7, %rsi
	0x48, 0x83, 0xc6, 0x01, //0x00001693 addq         $1, %rsi
	0x89, 0xf0, //0x00001697 movl         %esi, %eax
	0x83, 0xe0, 0x03, //0x00001699 andl         $3, %eax
	0x48, 0x81, 0xfb, 0x80, 0x01, 0x00, 0x00, //0x0000169c cmpq         $384, %rbx
	0x0f, 0x83, 0x33, 0x00, 0x00, 0x00, //0x000016a3 jae          LBB0_310
	0x31, 0xdb, //0x000016a9 xorl         %ebx, %ebx
	0xe9, 0xde, 0x00, 0x00, 0x00, //0x000016ab jmp          LBB0_312
	//0x000016b0 LBB0_307
	0x89, 0xf1, //0x000016b0 movl         %esi, %ecx
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000016b2 jmp          LBB0_309
	//0x000016b7 LBB0_308
	0x41, 0x0f, 0xbc, 0xca, //0x000016b7 bsfl         %r10d, %ecx
	//0x000016bb LBB0_309
	0x48, 0x8b, 0x55, 0xb8, //0x000016bb movq         $-72(%rbp), %rdx
	0x48, 0x03, 0x55, 0xc0, //0x000016bf addq         $-64(%rbp), %rdx
	0x4c, 0x29, 0xc2, //0x000016c3 subq         %r8, %rdx
	0x48, 0x29, 0xca, //0x000016c6 subq         %rcx, %rdx
	0x48, 0xf7, 0xd0, //0x000016c9 notq         %rax
	0x48, 0x01, 0xd0, //0x000016cc addq         %rdx, %rax
	0x4c, 0x8b, 0x6d, 0xa8, //0x000016cf movq         $-88(%rbp), %r13
	0x4c, 0x8b, 0x5d, 0xb0, //0x000016d3 movq         $-80(%rbp), %r11
	0xe9, 0x8a, 0xf5, 0xff, 0xff, //0x000016d7 jmp          LBB0_156
	//0x000016dc LBB0_310
	0x48, 0x83, 0xe6, 0xfc, //0x000016dc andq         $-4, %rsi
	0x48, 0xf7, 0xde, //0x000016e0 negq         %rsi
	0x31, 0xdb, //0x000016e3 xorl         %ebx, %ebx
	0xc5, 0xf9, 0xef, 0xc0, //0x000016e5 vpxor        %xmm0, %xmm0, %xmm0
	//0x000016e9 LBB0_311
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x01, //0x000016e9 vmovdqu      %ymm0, $1(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x21, //0x000016f0 vmovdqu      %ymm0, $33(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x41, //0x000016f7 vmovdqu      %ymm0, $65(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x18, 0x61, //0x000016fe vmovdqu      %ymm0, $97(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x81, 0x00, 0x00, 0x00, //0x00001705 vmovdqu      %ymm0, $129(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xa1, 0x00, 0x00, 0x00, //0x0000170f vmovdqu      %ymm0, $161(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xc1, 0x00, 0x00, 0x00, //0x00001719 vmovdqu      %ymm0, $193(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xe1, 0x00, 0x00, 0x00, //0x00001723 vmovdqu      %ymm0, $225(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x01, 0x01, 0x00, 0x00, //0x0000172d vmovdqu      %ymm0, $257(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x21, 0x01, 0x00, 0x00, //0x00001737 vmovdqu      %ymm0, $289(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x41, 0x01, 0x00, 0x00, //0x00001741 vmovdqu      %ymm0, $321(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x61, 0x01, 0x00, 0x00, //0x0000174b vmovdqu      %ymm0, $353(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0x81, 0x01, 0x00, 0x00, //0x00001755 vmovdqu      %ymm0, $385(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xa1, 0x01, 0x00, 0x00, //0x0000175f vmovdqu      %ymm0, $417(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xc1, 0x01, 0x00, 0x00, //0x00001769 vmovdqu      %ymm0, $449(%r8,%rbx)
	0xc4, 0xc1, 0x7e, 0x7f, 0x84, 0x18, 0xe1, 0x01, 0x00, 0x00, //0x00001773 vmovdqu      %ymm0, $481(%r8,%rbx)
	0x48, 0x81, 0xc3, 0x00, 0x02, 0x00, 0x00, //0x0000177d addq         $512, %rbx
	0x48, 0x83, 0xc6, 0x04, //0x00001784 addq         $4, %rsi
	0x0f, 0x85, 0x5b, 0xff, 0xff, 0xff, //0x00001788 jne          LBB0_311
	//0x0000178e LBB0_312
	0x48, 0x85, 0xc0, //0x0000178e testq        %rax, %rax
	0x0f, 0x84, 0x36, 0x00, 0x00, 0x00, //0x00001791 je           LBB0_315
	0x48, 0xf7, 0xd8, //0x00001797 negq         %rax
	0xc5, 0xf9, 0xef, 0xc0, //0x0000179a vpxor        %xmm0, %xmm0, %xmm0
	//0x0000179e LBB0_314
	0x48, 0x89, 0xde, //0x0000179e movq         %rbx, %rsi
	0x48, 0x83, 0xce, 0x01, //0x000017a1 orq          $1, %rsi
	0xc4, 0xc1, 0x7e, 0x7f, 0x04, 0x30, //0x000017a5 vmovdqu      %ymm0, (%r8,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x30, 0x20, //0x000017ab vmovdqu      %ymm0, $32(%r8,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x30, 0x40, //0x000017b2 vmovdqu      %ymm0, $64(%r8,%rsi)
	0xc4, 0xc1, 0x7e, 0x7f, 0x44, 0x30, 0x60, //0x000017b9 vmovdqu      %ymm0, $96(%r8,%rsi)
	0x48, 0x83, 0xeb, 0x80, //0x000017c0 subq         $-128, %rbx
	0x48, 0xff, 0xc0, //0x000017c4 incq         %rax
	0x0f, 0x85, 0xd1, 0xff, 0xff, 0xff, //0x000017c7 jne          LBB0_314
	//0x000017cd LBB0_315
	0x48, 0x39, 0xd1, //0x000017cd cmpq         %rdx, %rcx
	0x0f, 0x84, 0x65, 0x01, 0x00, 0x00, //0x000017d0 je           LBB0_336
	0xf6, 0xc1, 0x70, //0x000017d6 testb        $112, %cl
	0x0f, 0x84, 0x3f, 0x01, 0x00, 0x00, //0x000017d9 je           LBB0_334
	//0x000017df LBB0_317
	0x48, 0x89, 0xce, //0x000017df movq         %rcx, %rsi
	0x48, 0x83, 0xe6, 0xf0, //0x000017e2 andq         $-16, %rsi
	0x48, 0x8d, 0x46, 0x01, //0x000017e6 leaq         $1(%rsi), %rax
	0xc5, 0xf9, 0xef, 0xc0, //0x000017ea vpxor        %xmm0, %xmm0, %xmm0
	//0x000017ee LBB0_318
	0xc4, 0xc1, 0x7a, 0x7f, 0x44, 0x10, 0x01, //0x000017ee vmovdqu      %xmm0, $1(%r8,%rdx)
	0x48, 0x83, 0xc2, 0x10, //0x000017f5 addq         $16, %rdx
	0x48, 0x39, 0xd6, //0x000017f9 cmpq         %rdx, %rsi
	0x0f, 0x85, 0xec, 0xff, 0xff, 0xff, //0x000017fc jne          LBB0_318
	0x48, 0x39, 0xf1, //0x00001802 cmpq         %rsi, %rcx
	0x48, 0x8b, 0x55, 0xc0, //0x00001805 movq         $-64(%rbp), %rdx
	0x0f, 0x85, 0x1a, 0x01, 0x00, 0x00, //0x00001809 jne          LBB0_335
	0xe9, 0x27, 0x01, 0x00, 0x00, //0x0000180f jmp          LBB0_336
	//0x00001814 LBB0_320
	0x89, 0xf1, //0x00001814 movl         %esi, %ecx
	0x48, 0x8b, 0x55, 0xb8, //0x00001816 movq         $-72(%rbp), %rdx
	0x48, 0x03, 0x55, 0xc0, //0x0000181a addq         $-64(%rbp), %rdx
	0x4c, 0x29, 0xc2, //0x0000181e subq         %r8, %rdx
	0x48, 0x29, 0xca, //0x00001821 subq         %rcx, %rdx
	0x48, 0xf7, 0xd0, //0x00001824 notq         %rax
	0x48, 0x01, 0xd0, //0x00001827 addq         %rdx, %rax
	0x4c, 0x8b, 0x6d, 0xa8, //0x0000182a movq         $-88(%rbp), %r13
	0xe9, 0x33, 0xf4, 0xff, 0xff, //0x0000182e jmp          LBB0_156
	//0x00001833 LBB0_321
	0x49, 0x01, 0xf9, //0x00001833 addq         %rdi, %r9
	0x48, 0x85, 0xc0, //0x00001836 testq        %rax, %rax
	0x0f, 0x85, 0x49, 0xeb, 0xff, 0xff, //0x00001839 jne          LBB0_23
	0xe9, 0x7c, 0xeb, 0xff, 0xff, //0x0000183f jmp          LBB0_28
	//0x00001844 LBB0_322
	0x48, 0x8b, 0x75, 0xc8, //0x00001844 movq         $-56(%rbp), %rsi
	0x49, 0x01, 0xf1, //0x00001848 addq         %rsi, %r9
	0x49, 0x83, 0xff, 0x20, //0x0000184b cmpq         $32, %r15
	0x0f, 0x82, 0x5a, 0x17, 0x00, 0x00, //0x0000184f jb           LBB0_647
	//0x00001855 LBB0_323
	0xc4, 0xc1, 0x7e, 0x6f, 0x01, //0x00001855 vmovdqu      (%r9), %ymm0
	0xc5, 0xfd, 0x74, 0x0d, 0xbe, 0xe7, 0xff, 0xff, //0x0000185a vpcmpeqb     $-6210(%rip), %ymm0, %ymm1  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc1, //0x00001862 vpmovmskb    %ymm1, %eax
	0xc5, 0xfd, 0x74, 0x05, 0xd2, 0xe7, 0xff, 0xff, //0x00001866 vpcmpeqb     $-6190(%rip), %ymm0, %ymm0  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc8, //0x0000186e vpmovmskb    %ymm0, %ecx
	0x85, 0xc9, //0x00001872 testl        %ecx, %ecx
	0x0f, 0x85, 0x5e, 0x17, 0x00, 0x00, //0x00001874 jne          LBB0_650
	0x4d, 0x85, 0xe4, //0x0000187a testq        %r12, %r12
	0x0f, 0x85, 0x6d, 0x17, 0x00, 0x00, //0x0000187d jne          LBB0_652
	0x45, 0x31, 0xe4, //0x00001883 xorl         %r12d, %r12d
	0x48, 0x85, 0xc0, //0x00001886 testq        %rax, %rax
	0x0f, 0x84, 0x9d, 0x17, 0x00, 0x00, //0x00001889 je           LBB0_653
	//0x0000188f LBB0_326
	0x48, 0x0f, 0xbc, 0xc0, //0x0000188f bsfq         %rax, %rax
	0x49, 0x29, 0xf1, //0x00001893 subq         %rsi, %r9
	0x4d, 0x8d, 0x1c, 0x01, //0x00001896 leaq         (%r9,%rax), %r11
	0x49, 0x83, 0xc3, 0x01, //0x0000189a addq         $1, %r11
	0xe9, 0xb2, 0xf6, 0xff, 0xff, //0x0000189e jmp          LBB0_197
	//0x000018a3 LBB0_327
	0x4c, 0x03, 0x4d, 0xc8, //0x000018a3 addq         $-56(%rbp), %r9
	0x49, 0x83, 0xff, 0x20, //0x000018a7 cmpq         $32, %r15
	0x0f, 0x82, 0xfc, 0x18, 0x00, 0x00, //0x000018ab jb           LBB0_676
	//0x000018b1 LBB0_328
	0xc4, 0xc1, 0x7e, 0x6f, 0x09, //0x000018b1 vmovdqu      (%r9), %ymm1
	0xc5, 0xf5, 0x74, 0x05, 0x62, 0xe7, 0xff, 0xff, //0x000018b6 vpcmpeqb     $-6302(%rip), %ymm1, %ymm0  /* LCPI0_1+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc0, //0x000018be vpmovmskb    %ymm0, %eax
	0xc5, 0xf5, 0x74, 0x05, 0x76, 0xe7, 0xff, 0xff, //0x000018c2 vpcmpeqb     $-6282(%rip), %ymm1, %ymm0  /* LCPI0_2+0(%rip) */
	0xc5, 0xfd, 0xd7, 0xc8, //0x000018ca vpmovmskb    %ymm0, %ecx
	0xc5, 0xfe, 0x6f, 0x05, 0x8a, 0xe7, 0xff, 0xff, //0x000018ce vmovdqu      $-6262(%rip), %ymm0  /* LCPI0_3+0(%rip) */
	0xc5, 0xfd, 0x64, 0xc1, //0x000018d6 vpcmpgtb     %ymm1, %ymm0, %ymm0
	0xc5, 0xed, 0x76, 0xd2, //0x000018da vpcmpeqd     %ymm2, %ymm2, %ymm2
	0xc5, 0xf5, 0x64, 0xca, //0x000018de vpcmpgtb     %ymm2, %ymm1, %ymm1
	0x85, 0xc9, //0x000018e2 testl        %ecx, %ecx
	0x0f, 0x85, 0x1f, 0x18, 0x00, 0x00, //0x000018e4 jne          LBB0_667
	0x4d, 0x85, 0xed, //0x000018ea testq        %r13, %r13
	0x0f, 0x85, 0x2e, 0x18, 0x00, 0x00, //0x000018ed jne          LBB0_669
	0x45, 0x31, 0xed, //0x000018f3 xorl         %r13d, %r13d
	0xc5, 0xfd, 0xdb, 0xc1, //0x000018f6 vpand        %ymm1, %ymm0, %ymm0
	0x48, 0x85, 0xc0, //0x000018fa testq        %rax, %rax
	0x0f, 0x84, 0x5b, 0x18, 0x00, 0x00, //0x000018fd je           LBB0_670
	//0x00001903 LBB0_331
	0x48, 0x0f, 0xbc, 0xc8, //0x00001903 bsfq         %rax, %rcx
	0xe9, 0x57, 0x18, 0x00, 0x00, //0x00001907 jmp          LBB0_671
	//0x0000190c LBB0_332
	0x4c, 0x89, 0xf1, //0x0000190c movq         %r14, %rcx
	//0x0000190f LBB0_333
	0x45, 0x0f, 0xaf, 0xfb, //0x0000190f imull        %r11d, %r15d
	0x41, 0x01, 0xd7, //0x00001913 addl         %edx, %r15d
	0x49, 0x89, 0xcb, //0x00001916 movq         %rcx, %r11
	0xe9, 0x62, 0xf8, 0xff, 0xff, //0x00001919 jmp          LBB0_241
	//0x0000191e LBB0_334
	0x48, 0x83, 0xca, 0x01, //0x0000191e orq          $1, %rdx
	0x48, 0x89, 0xd0, //0x00001922 movq         %rdx, %rax
	0x48, 0x8b, 0x55, 0xc0, //0x00001925 movq         $-64(%rbp), %rdx
	//0x00001929 LBB0_335
	0x41, 0xc6, 0x04, 0x00, 0x00, //0x00001929 movb         $0, (%r8,%rax)
	0x48, 0x83, 0xc0, 0x01, //0x0000192e addq         $1, %rax
	0x48, 0x39, 0xc2, //0x00001932 cmpq         %rax, %rdx
	0x0f, 0x85, 0xee, 0xff, 0xff, 0xff, //0x00001935 jne          LBB0_335
	//0x0000193b LBB0_336
	0x41, 0x8a, 0x12, //0x0000193b movb         (%r10), %dl
	0x31, 0xc9, //0x0000193e xorl         %ecx, %ecx
	0x80, 0xfa, 0x2d, //0x00001940 cmpb         $45, %dl
	0x0f, 0x94, 0xc1, //0x00001943 sete         %cl
	0x48, 0x39, 0x4d, 0x98, //0x00001946 cmpq         %rcx, $-104(%rbp)
	0x0f, 0x8e, 0xc2, 0x00, 0x00, 0x00, //0x0000194a jle          LBB0_349
	0x88, 0x55, 0xd7, //0x00001950 movb         %dl, $-41(%rbp)
	0x4c, 0x89, 0x5d, 0xb0, //0x00001953 movq         %r11, $-80(%rbp)
	0x4d, 0x29, 0xcb, //0x00001957 subq         %r9, %r11
	0xb2, 0x01, //0x0000195a movb         $1, %dl
	0x45, 0x31, 0xff, //0x0000195c xorl         %r15d, %r15d
	0x45, 0x31, 0xe4, //0x0000195f xorl         %r12d, %r12d
	0x45, 0x31, 0xf6, //0x00001962 xorl         %r14d, %r14d
	0x31, 0xdb, //0x00001965 xorl         %ebx, %ebx
	0x31, 0xff, //0x00001967 xorl         %edi, %edi
	0xe9, 0x24, 0x00, 0x00, 0x00, //0x00001969 jmp          LBB0_340
	//0x0000196e LBB0_338
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x0000196e movl         $1, %ebx
	0x44, 0x89, 0xf7, //0x00001973 movl         %r14d, %edi
	0x3c, 0x2e, //0x00001976 cmpb         $46, %al
	0x0f, 0x85, 0xb0, 0x00, 0x00, 0x00, //0x00001978 jne          LBB0_351
	//0x0000197e LBB0_339
	0x48, 0x83, 0xc1, 0x01, //0x0000197e addq         $1, %rcx
	0x48, 0x3b, 0x4d, 0x98, //0x00001982 cmpq         $-104(%rbp), %rcx
	0x0f, 0x9c, 0xc2, //0x00001986 setl         %dl
	0x49, 0x39, 0xcb, //0x00001989 cmpq         %rcx, %r11
	0x0f, 0x84, 0x93, 0x00, 0x00, 0x00, //0x0000198c je           LBB0_350
	//0x00001992 LBB0_340
	0x89, 0xde, //0x00001992 movl         %ebx, %esi
	0x41, 0x89, 0xfa, //0x00001994 movl         %edi, %r10d
	0x48, 0x8b, 0x45, 0xb8, //0x00001997 movq         $-72(%rbp), %rax
	0x0f, 0xb6, 0x04, 0x08, //0x0000199b movzbl       (%rax,%rcx), %eax
	0x8d, 0x78, 0xd0, //0x0000199f leal         $-48(%rax), %edi
	0x40, 0x80, 0xff, 0x09, //0x000019a2 cmpb         $9, %dil
	0x0f, 0x87, 0xc2, 0xff, 0xff, 0xff, //0x000019a6 ja           LBB0_338
	0x3c, 0x30, //0x000019ac cmpb         $48, %al
	0x0f, 0x85, 0x1b, 0x00, 0x00, 0x00, //0x000019ae jne          LBB0_344
	0x45, 0x85, 0xf6, //0x000019b4 testl        %r14d, %r14d
	0x0f, 0x84, 0x44, 0x00, 0x00, 0x00, //0x000019b7 je           LBB0_348
	0x49, 0x63, 0xd7, //0x000019bd movslq       %r15d, %rdx
	0x48, 0x39, 0x55, 0xc0, //0x000019c0 cmpq         %rdx, $-64(%rbp)
	0x0f, 0x87, 0x12, 0x00, 0x00, 0x00, //0x000019c4 ja           LBB0_345
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x000019ca jmp          LBB0_346
	//0x000019cf LBB0_344
	0x49, 0x63, 0xd6, //0x000019cf movslq       %r14d, %rdx
	0x48, 0x39, 0x55, 0xc0, //0x000019d2 cmpq         %rdx, $-64(%rbp)
	0x0f, 0x86, 0x15, 0x00, 0x00, 0x00, //0x000019d6 jbe          LBB0_347
	//0x000019dc LBB0_345
	0x41, 0x88, 0x04, 0x10, //0x000019dc movb         %al, (%r8,%rdx)
	0x41, 0x83, 0xc7, 0x01, //0x000019e0 addl         $1, %r15d
	//0x000019e4 LBB0_346
	0x44, 0x89, 0xd7, //0x000019e4 movl         %r10d, %edi
	0x45, 0x89, 0xfe, //0x000019e7 movl         %r15d, %r14d
	0x89, 0xf3, //0x000019ea movl         %esi, %ebx
	0xe9, 0x8d, 0xff, 0xff, 0xff, //0x000019ec jmp          LBB0_339
	//0x000019f1 LBB0_347
	0x44, 0x89, 0xd7, //0x000019f1 movl         %r10d, %edi
	0x41, 0xbc, 0x01, 0x00, 0x00, 0x00, //0x000019f4 movl         $1, %r12d
	0x89, 0xf3, //0x000019fa movl         %esi, %ebx
	0xe9, 0x7d, 0xff, 0xff, 0xff, //0x000019fc jmp          LBB0_339
	//0x00001a01 LBB0_348
	0x41, 0x83, 0xc2, 0xff, //0x00001a01 addl         $-1, %r10d
	0x45, 0x31, 0xf6, //0x00001a05 xorl         %r14d, %r14d
	0x44, 0x89, 0xd7, //0x00001a08 movl         %r10d, %edi
	0x89, 0xf3, //0x00001a0b movl         %esi, %ebx
	0xe9, 0x6c, 0xff, 0xff, 0xff, //0x00001a0d jmp          LBB0_339
	//0x00001a12 LBB0_349
	0x31, 0xc9, //0x00001a12 xorl         %ecx, %ecx
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001a14 movabsq      $4503599627370495, %rax
	0x31, 0xdb, //0x00001a1e xorl         %ebx, %ebx
	0xe9, 0xe9, 0x14, 0x00, 0x00, //0x00001a20 jmp          LBB0_639
	//0x00001a25 LBB0_350
	0x41, 0x89, 0xfa, //0x00001a25 movl         %edi, %r10d
	0x48, 0x8b, 0x4d, 0x98, //0x00001a28 movq         $-104(%rbp), %rcx
	0x89, 0xde, //0x00001a2c movl         %ebx, %esi
	//0x00001a2e LBB0_351
	0x85, 0xf6, //0x00001a2e testl        %esi, %esi
	0x45, 0x0f, 0x44, 0xd7, //0x00001a30 cmovel       %r15d, %r10d
	0xf6, 0xc2, 0x01, //0x00001a34 testb        $1, %dl
	0x4c, 0x8b, 0x5d, 0xb0, //0x00001a37 movq         $-80(%rbp), %r11
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00001a3b movabsq      $-9223372036854775808, %rdi
	0x49, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00001a45 movabsq      $9218868437227405312, %r14
	0x0f, 0x84, 0xa5, 0x00, 0x00, 0x00, //0x00001a4f je           LBB0_366
	0x89, 0xc8, //0x00001a55 movl         %ecx, %eax
	0x48, 0x8b, 0x5d, 0xb8, //0x00001a57 movq         $-72(%rbp), %rbx
	0x8a, 0x04, 0x03, //0x00001a5b movb         (%rbx,%rax), %al
	0x0c, 0x20, //0x00001a5e orb          $32, %al
	0x3c, 0x65, //0x00001a60 cmpb         $101, %al
	0x0f, 0x85, 0x92, 0x00, 0x00, 0x00, //0x00001a62 jne          LBB0_366
	0x89, 0xca, //0x00001a68 movl         %ecx, %edx
	0x8a, 0x5c, 0x13, 0x01, //0x00001a6a movb         $1(%rbx,%rdx), %bl
	0x80, 0xfb, 0x2d, //0x00001a6e cmpb         $45, %bl
	0x0f, 0x84, 0x16, 0x00, 0x00, 0x00, //0x00001a71 je           LBB0_356
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x00001a77 movl         $1, %eax
	0x80, 0xfb, 0x2b, //0x00001a7c cmpb         $43, %bl
	0x0f, 0x85, 0x17, 0x00, 0x00, 0x00, //0x00001a7f jne          LBB0_358
	0x83, 0xc1, 0x02, //0x00001a85 addl         $2, %ecx
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00001a88 jmp          LBB0_357
	//0x00001a8d LBB0_356
	0x83, 0xc1, 0x02, //0x00001a8d addl         $2, %ecx
	0xb8, 0xff, 0xff, 0xff, 0xff, //0x00001a90 movl         $-1, %eax
	//0x00001a95 LBB0_357
	0x89, 0xca, //0x00001a95 movl         %ecx, %edx
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x00001a97 jmp          LBB0_359
	//0x00001a9c LBB0_358
	0x48, 0x83, 0xc2, 0x01, //0x00001a9c addq         $1, %rdx
	//0x00001aa0 LBB0_359
	0x48, 0x63, 0xd2, //0x00001aa0 movslq       %edx, %rdx
	0x31, 0xc9, //0x00001aa3 xorl         %ecx, %ecx
	0x48, 0x39, 0x55, 0x98, //0x00001aa5 cmpq         %rdx, $-104(%rbp)
	0x0f, 0x8e, 0x42, 0x00, 0x00, 0x00, //0x00001aa9 jle          LBB0_365
	0x49, 0x01, 0xd1, //0x00001aaf addq         %rdx, %r9
	0x31, 0xc9, //0x00001ab2 xorl         %ecx, %ecx
	//0x00001ab4 LBB0_361
	0x48, 0x8b, 0x55, 0xc8, //0x00001ab4 movq         $-56(%rbp), %rdx
	0x42, 0x0f, 0xbe, 0x14, 0x0a, //0x00001ab8 movsbl       (%rdx,%r9), %edx
	0x83, 0xfa, 0x30, //0x00001abd cmpl         $48, %edx
	0x0f, 0x8c, 0x2b, 0x00, 0x00, 0x00, //0x00001ac0 jl           LBB0_365
	0x80, 0xfa, 0x39, //0x00001ac6 cmpb         $57, %dl
	0x0f, 0x8f, 0x22, 0x00, 0x00, 0x00, //0x00001ac9 jg           LBB0_365
	0x81, 0xf9, 0x0f, 0x27, 0x00, 0x00, //0x00001acf cmpl         $9999, %ecx
	0x0f, 0x8f, 0x16, 0x00, 0x00, 0x00, //0x00001ad5 jg           LBB0_365
	0x8d, 0x0c, 0x89, //0x00001adb leal         (%rcx,%rcx,4), %ecx
	0x8d, 0x0c, 0x4a, //0x00001ade leal         (%rdx,%rcx,2), %ecx
	0x83, 0xc1, 0xd0, //0x00001ae1 addl         $-48, %ecx
	0x49, 0x83, 0xc1, 0x01, //0x00001ae4 addq         $1, %r9
	0x4d, 0x39, 0xcb, //0x00001ae8 cmpq         %r9, %r11
	0x0f, 0x85, 0xc3, 0xff, 0xff, 0xff, //0x00001aeb jne          LBB0_361
	//0x00001af1 LBB0_365
	0x0f, 0xaf, 0xc8, //0x00001af1 imull        %eax, %ecx
	0x44, 0x01, 0xd1, //0x00001af4 addl         %r10d, %ecx
	0x41, 0x89, 0xca, //0x00001af7 movl         %ecx, %r10d
	//0x00001afa LBB0_366
	0x45, 0x85, 0xff, //0x00001afa testl        %r15d, %r15d
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00001afd movabsq      $4503599627370495, %rax
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00001b07 je           LBB0_369
	0x4c, 0x89, 0xd2, //0x00001b0d movq         %r10, %rdx
	0x31, 0xc9, //0x00001b10 xorl         %ecx, %ecx
	0x81, 0xfa, 0x36, 0x01, 0x00, 0x00, //0x00001b12 cmpl         $310, %edx
	0x0f, 0x8e, 0x17, 0x00, 0x00, 0x00, //0x00001b18 jle          LBB0_370
	0x4c, 0x89, 0xf3, //0x00001b1e movq         %r14, %rbx
	0x8a, 0x55, 0xd7, //0x00001b21 movb         $-41(%rbp), %dl
	0xe9, 0xe5, 0x13, 0x00, 0x00, //0x00001b24 jmp          LBB0_639
	//0x00001b29 LBB0_369
	0x31, 0xdb, //0x00001b29 xorl         %ebx, %ebx
	0x31, 0xc9, //0x00001b2b xorl         %ecx, %ecx
	0x8a, 0x55, 0xd7, //0x00001b2d movb         $-41(%rbp), %dl
	0xe9, 0xd9, 0x13, 0x00, 0x00, //0x00001b30 jmp          LBB0_639
	//0x00001b35 LBB0_370
	0x81, 0xfa, 0xb6, 0xfe, 0xff, 0xff, //0x00001b35 cmpl         $-330, %edx
	0x0f, 0x8d, 0x0a, 0x00, 0x00, 0x00, //0x00001b3b jge          LBB0_372
	0x31, 0xdb, //0x00001b41 xorl         %ebx, %ebx
	0x8a, 0x55, 0xd7, //0x00001b43 movb         $-41(%rbp), %dl
	0xe9, 0xc3, 0x13, 0x00, 0x00, //0x00001b46 jmp          LBB0_639
	//0x00001b4b LBB0_372
	0x85, 0xd2, //0x00001b4b testl        %edx, %edx
	0x4c, 0x89, 0x6d, 0xa8, //0x00001b4d movq         %r13, $-88(%rbp)
	0x0f, 0x8e, 0x0e, 0x02, 0x00, 0x00, //0x00001b51 jle          LBB0_404
	0x45, 0x31, 0xdb, //0x00001b57 xorl         %r11d, %r11d
	0x44, 0x89, 0xff, //0x00001b5a movl         %r15d, %edi
	0x44, 0x89, 0xfa, //0x00001b5d movl         %r15d, %edx
	0x4c, 0x8b, 0x75, 0xc0, //0x00001b60 movq         $-64(%rbp), %r14
	0x4c, 0x89, 0xd6, //0x00001b64 movq         %r10, %rsi
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00001b67 jmp          LBB0_377
	//0x00001b6c LBB0_374
	0x45, 0x31, 0xff, //0x00001b6c xorl         %r15d, %r15d
	0x31, 0xff, //0x00001b6f xorl         %edi, %edi
	//0x00001b71 LBB0_375
	0x31, 0xd2, //0x00001b71 xorl         %edx, %edx
	//0x00001b73 LBB0_376
	0x48, 0x8b, 0x45, 0x98, //0x00001b73 movq         $-104(%rbp), %rax
	0x44, 0x01, 0xd8, //0x00001b77 addl         %r11d, %eax
	0x41, 0x89, 0xc3, //0x00001b7a movl         %eax, %r11d
	0x4c, 0x89, 0xce, //0x00001b7d movq         %r9, %rsi
	0x85, 0xf6, //0x00001b80 testl        %esi, %esi
	0x0f, 0x8e, 0xe5, 0x01, 0x00, 0x00, //0x00001b82 jle          LBB0_405
	//0x00001b88 LBB0_377
	0x83, 0xfe, 0x08, //0x00001b88 cmpl         $8, %esi
	0x0f, 0x8e, 0x0d, 0x00, 0x00, 0x00, //0x00001b8b jle          LBB0_379
	0x49, 0x89, 0xf1, //0x00001b91 movq         %rsi, %r9
	0xb8, 0x1b, 0x00, 0x00, 0x00, //0x00001b94 movl         $27, %eax
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x00001b99 jmp          LBB0_380
	//0x00001b9e LBB0_379
	0x49, 0x89, 0xf1, //0x00001b9e movq         %rsi, %r9
	0x89, 0xf0, //0x00001ba1 movl         %esi, %eax
	0x48, 0x8d, 0x0d, 0x06, 0x46, 0x00, 0x00, //0x00001ba3 leaq         $17926(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x04, 0x81, //0x00001baa movl         (%rcx,%rax,4), %eax
	//0x00001bad LBB0_380
	0x48, 0x89, 0x45, 0x98, //0x00001bad movq         %rax, $-104(%rbp)
	0x85, 0xd2, //0x00001bb1 testl        %edx, %edx
	0x0f, 0x84, 0xb8, 0xff, 0xff, 0xff, //0x00001bb3 je           LBB0_375
	0x8b, 0x4d, 0x98, //0x00001bb9 movl         $-104(%rbp), %ecx
	0x85, 0xd2, //0x00001bbc testl        %edx, %edx
	0xb8, 0x00, 0x00, 0x00, 0x00, //0x00001bbe movl         $0, %eax
	0x0f, 0x4f, 0xc2, //0x00001bc3 cmovgl       %edx, %eax
	0x31, 0xf6, //0x00001bc6 xorl         %esi, %esi
	0x31, 0xdb, //0x00001bc8 xorl         %ebx, %ebx
	//0x00001bca LBB0_382
	0x48, 0x39, 0xf0, //0x00001bca cmpq         %rsi, %rax
	0x0f, 0x84, 0xa1, 0x00, 0x00, 0x00, //0x00001bcd je           LBB0_390
	0x48, 0x8d, 0x3c, 0x9b, //0x00001bd3 leaq         (%rbx,%rbx,4), %rdi
	0x49, 0x0f, 0xbe, 0x1c, 0x30, //0x00001bd7 movsbq       (%r8,%rsi), %rbx
	0x48, 0x8d, 0x1c, 0x7b, //0x00001bdc leaq         (%rbx,%rdi,2), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x00001be0 addq         $-48, %rbx
	0x48, 0x83, 0xc6, 0x01, //0x00001be4 addq         $1, %rsi
	0x48, 0x89, 0xdf, //0x00001be8 movq         %rbx, %rdi
	0x48, 0xd3, 0xef, //0x00001beb shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00001bee testq        %rdi, %rdi
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x00001bf1 je           LBB0_382
	0x89, 0xf0, //0x00001bf7 movl         %esi, %eax
	//0x00001bf9 LBB0_385
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001bf9 movq         $-1, %r10
	0x49, 0xd3, 0xe2, //0x00001c00 shlq         %cl, %r10
	0x49, 0xf7, 0xd2, //0x00001c03 notq         %r10
	0x31, 0xff, //0x00001c06 xorl         %edi, %edi
	0x39, 0xd0, //0x00001c08 cmpl         %edx, %eax
	0x0f, 0x8d, 0x4a, 0x00, 0x00, 0x00, //0x00001c0a jge          LBB0_389
	0x4c, 0x63, 0xe8, //0x00001c10 movslq       %eax, %r13
	0x4d, 0x63, 0xf7, //0x00001c13 movslq       %r15d, %r14
	0x4f, 0x8d, 0x3c, 0x28, //0x00001c16 leaq         (%r8,%r13), %r15
	0x31, 0xff, //0x00001c1a xorl         %edi, %edi
	//0x00001c1c LBB0_387
	0x48, 0x89, 0xda, //0x00001c1c movq         %rbx, %rdx
	0x48, 0xd3, 0xea, //0x00001c1f shrq         %cl, %rdx
	0x4c, 0x21, 0xd3, //0x00001c22 andq         %r10, %rbx
	0x80, 0xc2, 0x30, //0x00001c25 addb         $48, %dl
	0x41, 0x88, 0x14, 0x38, //0x00001c28 movb         %dl, (%r8,%rdi)
	0x49, 0x0f, 0xbe, 0x14, 0x3f, //0x00001c2c movsbq       (%r15,%rdi), %rdx
	0x4a, 0x8d, 0x34, 0x2f, //0x00001c31 leaq         (%rdi,%r13), %rsi
	0x48, 0x83, 0xc6, 0x01, //0x00001c35 addq         $1, %rsi
	0x48, 0x83, 0xc7, 0x01, //0x00001c39 addq         $1, %rdi
	0x48, 0x8d, 0x1c, 0x9b, //0x00001c3d leaq         (%rbx,%rbx,4), %rbx
	0x48, 0x8d, 0x1c, 0x5a, //0x00001c41 leaq         (%rdx,%rbx,2), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x00001c45 addq         $-48, %rbx
	0x4c, 0x39, 0xf6, //0x00001c49 cmpq         %r14, %rsi
	0x0f, 0x8c, 0xca, 0xff, 0xff, 0xff, //0x00001c4c jl           LBB0_387
	0x4c, 0x8b, 0x6d, 0xa8, //0x00001c52 movq         $-88(%rbp), %r13
	0x4c, 0x8b, 0x75, 0xc0, //0x00001c56 movq         $-64(%rbp), %r14
	//0x00001c5a LBB0_389
	0x41, 0x29, 0xc1, //0x00001c5a subl         %eax, %r9d
	0x41, 0x83, 0xc1, 0x01, //0x00001c5d addl         $1, %r9d
	0x48, 0x85, 0xdb, //0x00001c61 testq        %rbx, %rbx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00001c64 movl         $1, %esi
	0x0f, 0x85, 0x54, 0x00, 0x00, 0x00, //0x00001c69 jne          LBB0_394
	0xe9, 0x7f, 0x00, 0x00, 0x00, //0x00001c6f jmp          LBB0_396
	//0x00001c74 LBB0_390
	0x48, 0x85, 0xdb, //0x00001c74 testq        %rbx, %rbx
	0x0f, 0x84, 0xef, 0xfe, 0xff, 0xff, //0x00001c77 je           LBB0_374
	0x48, 0x89, 0xde, //0x00001c7d movq         %rbx, %rsi
	0x48, 0xd3, 0xee, //0x00001c80 shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00001c83 testq        %rsi, %rsi
	0x0f, 0x84, 0x98, 0x00, 0x00, 0x00, //0x00001c86 je           LBB0_400
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x00001c8c movq         $-1, %r10
	0x49, 0xd3, 0xe2, //0x00001c93 shlq         %cl, %r10
	0x49, 0xf7, 0xd2, //0x00001c96 notq         %r10
	0x41, 0x29, 0xc1, //0x00001c99 subl         %eax, %r9d
	0x41, 0x83, 0xc1, 0x01, //0x00001c9c addl         $1, %r9d
	0x31, 0xff, //0x00001ca0 xorl         %edi, %edi
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x00001ca2 movl         $1, %esi
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x00001ca7 jmp          LBB0_394
	//0x00001cac LBB0_393
	0x48, 0x85, 0xc0, //0x00001cac testq        %rax, %rax
	0x44, 0x0f, 0x45, 0xe6, //0x00001caf cmovnel      %esi, %r12d
	0x48, 0x01, 0xdb, //0x00001cb3 addq         %rbx, %rbx
	0x48, 0x8d, 0x1c, 0x9b, //0x00001cb6 leaq         (%rbx,%rbx,4), %rbx
	0x48, 0x85, 0xdb, //0x00001cba testq        %rbx, %rbx
	0x0f, 0x84, 0x30, 0x00, 0x00, 0x00, //0x00001cbd je           LBB0_396
	//0x00001cc3 LBB0_394
	0x48, 0x89, 0xd8, //0x00001cc3 movq         %rbx, %rax
	0x48, 0xd3, 0xe8, //0x00001cc6 shrq         %cl, %rax
	0x4c, 0x21, 0xd3, //0x00001cc9 andq         %r10, %rbx
	0x48, 0x63, 0xd7, //0x00001ccc movslq       %edi, %rdx
	0x49, 0x39, 0xd6, //0x00001ccf cmpq         %rdx, %r14
	0x0f, 0x86, 0xd4, 0xff, 0xff, 0xff, //0x00001cd2 jbe          LBB0_393
	0x04, 0x30, //0x00001cd8 addb         $48, %al
	0x41, 0x88, 0x04, 0x10, //0x00001cda movb         %al, (%r8,%rdx)
	0x83, 0xc2, 0x01, //0x00001cde addl         $1, %edx
	0x89, 0xd7, //0x00001ce1 movl         %edx, %edi
	0x48, 0x01, 0xdb, //0x00001ce3 addq         %rbx, %rbx
	0x48, 0x8d, 0x1c, 0x9b, //0x00001ce6 leaq         (%rbx,%rbx,4), %rbx
	0x48, 0x85, 0xdb, //0x00001cea testq        %rbx, %rbx
	0x0f, 0x85, 0xd0, 0xff, 0xff, 0xff, //0x00001ced jne          LBB0_394
	//0x00001cf3 LBB0_396
	0x85, 0xff, //0x00001cf3 testl        %edi, %edi
	0x0f, 0x8e, 0x47, 0x00, 0x00, 0x00, //0x00001cf5 jle          LBB0_401
	0x89, 0xf8, //0x00001cfb movl         %edi, %eax
	0x48, 0x83, 0xc0, 0x01, //0x00001cfd addq         $1, %rax
	//0x00001d01 LBB0_398
	0x8d, 0x4f, 0xff, //0x00001d01 leal         $-1(%rdi), %ecx
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x00001d04 cmpb         $48, (%r8,%rcx)
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x00001d09 jne          LBB0_402
	0x48, 0x83, 0xc0, 0xff, //0x00001d0f addq         $-1, %rax
	0x89, 0xcf, //0x00001d13 movl         %ecx, %edi
	0x48, 0x83, 0xf8, 0x01, //0x00001d15 cmpq         $1, %rax
	0x0f, 0x8f, 0xe2, 0xff, 0xff, 0xff, //0x00001d19 jg           LBB0_398
	0xe9, 0x2e, 0x00, 0x00, 0x00, //0x00001d1f jmp          LBB0_403
	//0x00001d24 LBB0_400
	0x48, 0x01, 0xdb, //0x00001d24 addq         %rbx, %rbx
	0x48, 0x8d, 0x1c, 0x9b, //0x00001d27 leaq         (%rbx,%rbx,4), %rbx
	0x83, 0xc0, 0x01, //0x00001d2b addl         $1, %eax
	0x48, 0x89, 0xde, //0x00001d2e movq         %rbx, %rsi
	0x48, 0xd3, 0xee, //0x00001d31 shrq         %cl, %rsi
	0x48, 0x85, 0xf6, //0x00001d34 testq        %rsi, %rsi
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x00001d37 je           LBB0_400
	0xe9, 0xb7, 0xfe, 0xff, 0xff, //0x00001d3d jmp          LBB0_385
	//0x00001d42 LBB0_401
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00001d42 je           LBB0_403
	//0x00001d48 LBB0_402
	0x41, 0x89, 0xff, //0x00001d48 movl         %edi, %r15d
	0x89, 0xfa, //0x00001d4b movl         %edi, %edx
	0xe9, 0x21, 0xfe, 0xff, 0xff, //0x00001d4d jmp          LBB0_376
	//0x00001d52 LBB0_403
	0x48, 0x8b, 0x45, 0x98, //0x00001d52 movq         $-104(%rbp), %rax
	0x44, 0x01, 0xd8, //0x00001d56 addl         %r11d, %eax
	0x31, 0xf6, //0x00001d59 xorl         %esi, %esi
	0x45, 0x31, 0xff, //0x00001d5b xorl         %r15d, %r15d
	0x31, 0xff, //0x00001d5e xorl         %edi, %edi
	0xe9, 0x08, 0x00, 0x00, 0x00, //0x00001d60 jmp          LBB0_405
	//0x00001d65 LBB0_404
	0x44, 0x89, 0xff, //0x00001d65 movl         %r15d, %edi
	0x4c, 0x89, 0xd6, //0x00001d68 movq         %r10, %rsi
	0x31, 0xc0, //0x00001d6b xorl         %eax, %eax
	//0x00001d6d LBB0_405
	0x49, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, //0x00001d6d movabsq      $1152921504606846975, %r10
	0x49, 0x8d, 0x48, 0x01, //0x00001d77 leaq         $1(%r8), %rcx
	0x48, 0x89, 0x4d, 0x88, //0x00001d7b movq         %rcx, $-120(%rbp)
	0x41, 0x89, 0xfe, //0x00001d7f movl         %edi, %r14d
	0x49, 0x89, 0xf3, //0x00001d82 movq         %rsi, %r11
	0x49, 0x89, 0xc1, //0x00001d85 movq         %rax, %r9
	0xe9, 0x06, 0x00, 0x00, 0x00, //0x00001d88 jmp          LBB0_407
	//0x00001d8d LBB0_406
	0x45, 0x31, 0xf6, //0x00001d8d xorl         %r14d, %r14d
	0x41, 0x29, 0xc9, //0x00001d90 subl         %ecx, %r9d
	//0x00001d93 LBB0_407
	0x45, 0x85, 0xdb, //0x00001d93 testl        %r11d, %r11d
	0x0f, 0x88, 0x15, 0x00, 0x00, 0x00, //0x00001d96 js           LBB0_410
	0x0f, 0x85, 0x15, 0x07, 0x00, 0x00, //0x00001d9c jne          LBB0_508
	0x41, 0x80, 0x38, 0x35, //0x00001da2 cmpb         $53, (%r8)
	0x0f, 0x8c, 0x24, 0x00, 0x00, 0x00, //0x00001da6 jl           LBB0_413
	0xe9, 0x06, 0x07, 0x00, 0x00, //0x00001dac jmp          LBB0_508
	//0x00001db1 LBB0_410
	0x41, 0x83, 0xfb, 0xf8, //0x00001db1 cmpl         $-8, %r11d
	0x0f, 0x8d, 0x15, 0x00, 0x00, 0x00, //0x00001db5 jge          LBB0_413
	0xb9, 0x1b, 0x00, 0x00, 0x00, //0x00001dbb movl         $27, %ecx
	0x85, 0xff, //0x00001dc0 testl        %edi, %edi
	0x0f, 0x84, 0x88, 0x05, 0x00, 0x00, //0x00001dc2 je           LBB0_485
	0x41, 0x89, 0xfe, //0x00001dc8 movl         %edi, %r14d
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00001dcb jmp          LBB0_414
	//0x00001dd0 LBB0_413
	0x44, 0x89, 0xd8, //0x00001dd0 movl         %r11d, %eax
	0xf7, 0xd8, //0x00001dd3 negl         %eax
	0x48, 0x8d, 0x0d, 0xd4, 0x43, 0x00, 0x00, //0x00001dd5 leaq         $17364(%rip), %rcx  /* _POW_TAB+0(%rip) */
	0x8b, 0x0c, 0x81, //0x00001ddc movl         (%rcx,%rax,4), %ecx
	0x45, 0x85, 0xf6, //0x00001ddf testl        %r14d, %r14d
	0x0f, 0x84, 0xa5, 0xff, 0xff, 0xff, //0x00001de2 je           LBB0_406
	//0x00001de8 LBB0_414
	0x89, 0x4d, 0xa4, //0x00001de8 movl         %ecx, $-92(%rbp)
	0x89, 0xc9, //0x00001deb movl         %ecx, %ecx
	0x48, 0x6b, 0xc1, 0x68, //0x00001ded imulq        $104, %rcx, %rax
	0x48, 0x8d, 0x35, 0xe8, 0x43, 0x00, 0x00, //0x00001df1 leaq         $17384(%rip), %rsi  /* _LSHIFT_TAB+0(%rip) */
	0x8b, 0x14, 0x30, //0x00001df8 movl         (%rax,%rsi), %edx
	0x48, 0x89, 0x55, 0xb8, //0x00001dfb movq         %rdx, $-72(%rbp)
	0x49, 0x63, 0xd6, //0x00001dff movslq       %r14d, %rdx
	0x48, 0x01, 0xc6, //0x00001e02 addq         %rax, %rsi
	0x48, 0x83, 0xc6, 0x04, //0x00001e05 addq         $4, %rsi
	0x31, 0xff, //0x00001e09 xorl         %edi, %edi
	0x4c, 0x89, 0x5d, 0xc8, //0x00001e0b movq         %r11, $-56(%rbp)
	//0x00001e0f LBB0_415
	0x0f, 0xb6, 0x1c, 0x3e, //0x00001e0f movzbl       (%rsi,%rdi), %ebx
	0x84, 0xdb, //0x00001e13 testb        %bl, %bl
	0x0f, 0x84, 0x39, 0x00, 0x00, 0x00, //0x00001e15 je           LBB0_419
	0x41, 0x38, 0x1c, 0x38, //0x00001e1b cmpb         %bl, (%r8,%rdi)
	0x0f, 0x85, 0x39, 0x00, 0x00, 0x00, //0x00001e1f jne          LBB0_420
	0x48, 0x83, 0xc7, 0x01, //0x00001e25 addq         $1, %rdi
	0x48, 0x39, 0xfa, //0x00001e29 cmpq         %rdi, %rdx
	0x0f, 0x85, 0xdd, 0xff, 0xff, 0xff, //0x00001e2c jne          LBB0_415
	0x44, 0x89, 0xf2, //0x00001e32 movl         %r14d, %edx
	0x48, 0x8d, 0x35, 0xa4, 0x43, 0x00, 0x00, //0x00001e35 leaq         $17316(%rip), %rsi  /* _LSHIFT_TAB+0(%rip) */
	0x48, 0x01, 0xf0, //0x00001e3c addq         %rsi, %rax
	0x80, 0x7c, 0x02, 0x04, 0x00, //0x00001e3f cmpb         $0, $4(%rdx,%rax)
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e44 movl         $1, %ebx
	0x0f, 0x85, 0x1a, 0x00, 0x00, 0x00, //0x00001e49 jne          LBB0_421
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x00001e4f jmp          LBB0_422
	//0x00001e54 LBB0_419
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e54 movl         $1, %ebx
	0xe9, 0x16, 0x00, 0x00, 0x00, //0x00001e59 jmp          LBB0_422
	//0x00001e5e LBB0_420
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e5e movl         $1, %ebx
	0x0f, 0x8d, 0x0b, 0x00, 0x00, 0x00, //0x00001e63 jge          LBB0_422
	//0x00001e69 LBB0_421
	0x48, 0x8b, 0x45, 0xb8, //0x00001e69 movq         $-72(%rbp), %rax
	0x83, 0xc0, 0xff, //0x00001e6d addl         $-1, %eax
	0x48, 0x89, 0x45, 0xb8, //0x00001e70 movq         %rax, $-72(%rbp)
	//0x00001e74 LBB0_422
	0x45, 0x85, 0xf6, //0x00001e74 testl        %r14d, %r14d
	0x4c, 0x89, 0x4d, 0x98, //0x00001e77 movq         %r9, $-104(%rbp)
	0x0f, 0x8e, 0xc9, 0x00, 0x00, 0x00, //0x00001e7b jle          LBB0_430
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001e81 movl         $1, %r11d
	0x48, 0x8b, 0x45, 0xb8, //0x00001e87 movq         $-72(%rbp), %rax
	0x44, 0x01, 0xf0, //0x00001e8b addl         %r14d, %eax
	0x45, 0x89, 0xf1, //0x00001e8e movl         %r14d, %r9d
	0x48, 0x98, //0x00001e91 cltq         
	0x49, 0x89, 0xc5, //0x00001e93 movq         %rax, %r13
	0x49, 0xc1, 0xe5, 0x20, //0x00001e96 shlq         $32, %r13
	0x48, 0x83, 0xc0, 0xff, //0x00001e9a addq         $-1, %rax
	0x49, 0x83, 0xc1, 0x01, //0x00001e9e addq         $1, %r9
	0x41, 0x83, 0xc6, 0xff, //0x00001ea2 addl         $-1, %r14d
	0x31, 0xf6, //0x00001ea6 xorl         %esi, %esi
	0xe9, 0x2a, 0x00, 0x00, 0x00, //0x00001ea8 jmp          LBB0_426
	//0x00001ead LBB0_424
	0x48, 0x85, 0xc0, //0x00001ead testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xe3, //0x00001eb0 cmovnel      %r11d, %r12d
	//0x00001eb4 LBB0_425
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00001eb4 movabsq      $-4294967296, %rax
	0x49, 0x01, 0xc5, //0x00001ebe addq         %rax, %r13
	0x48, 0x8d, 0x43, 0xff, //0x00001ec1 leaq         $-1(%rbx), %rax
	0x49, 0x83, 0xc1, 0xff, //0x00001ec5 addq         $-1, %r9
	0x41, 0x83, 0xc6, 0xff, //0x00001ec9 addl         $-1, %r14d
	0x49, 0x83, 0xf9, 0x01, //0x00001ecd cmpq         $1, %r9
	0x0f, 0x8e, 0x4f, 0x00, 0x00, 0x00, //0x00001ed1 jle          LBB0_428
	//0x00001ed7 LBB0_426
	0x48, 0x89, 0xc3, //0x00001ed7 movq         %rax, %rbx
	0x44, 0x89, 0xf0, //0x00001eda movl         %r14d, %eax
	0x49, 0x0f, 0xbe, 0x3c, 0x00, //0x00001edd movsbq       (%r8,%rax), %rdi
	0x48, 0x83, 0xc7, 0xd0, //0x00001ee2 addq         $-48, %rdi
	0x48, 0xd3, 0xe7, //0x00001ee6 shlq         %cl, %rdi
	0x48, 0x01, 0xf7, //0x00001ee9 addq         %rsi, %rdi
	0x48, 0x89, 0xf8, //0x00001eec movq         %rdi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001eef movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001ef9 mulq         %rdx
	0x48, 0x89, 0xd6, //0x00001efc movq         %rdx, %rsi
	0x48, 0xc1, 0xee, 0x03, //0x00001eff shrq         $3, %rsi
	0x48, 0x8d, 0x04, 0x36, //0x00001f03 leaq         (%rsi,%rsi), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00001f07 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf8, //0x00001f0b movq         %rdi, %rax
	0x48, 0x29, 0xd0, //0x00001f0e subq         %rdx, %rax
	0x48, 0x3b, 0x5d, 0xc0, //0x00001f11 cmpq         $-64(%rbp), %rbx
	0x0f, 0x83, 0x92, 0xff, 0xff, 0xff, //0x00001f15 jae          LBB0_424
	0x04, 0x30, //0x00001f1b addb         $48, %al
	0x41, 0x88, 0x04, 0x18, //0x00001f1d movb         %al, (%r8,%rbx)
	0xe9, 0x8e, 0xff, 0xff, 0xff, //0x00001f21 jmp          LBB0_425
	//0x00001f26 LBB0_428
	0x48, 0x83, 0xff, 0x0a, //0x00001f26 cmpq         $10, %rdi
	0x4c, 0x8b, 0x6d, 0xa8, //0x00001f2a movq         $-88(%rbp), %r13
	0x4c, 0x8b, 0x4d, 0x98, //0x00001f2e movq         $-104(%rbp), %r9
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x00001f32 jae          LBB0_431
	0x4c, 0x8b, 0x75, 0xc0, //0x00001f38 movq         $-64(%rbp), %r14
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001f3c movq         $-56(%rbp), %r11
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001f40 movl         $1, %ebx
	0xe9, 0x80, 0x00, 0x00, 0x00, //0x00001f45 jmp          LBB0_435
	//0x00001f4a LBB0_430
	0x4c, 0x8b, 0x75, 0xc0, //0x00001f4a movq         $-64(%rbp), %r14
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001f4e movq         $-56(%rbp), %r11
	0xe9, 0x73, 0x00, 0x00, 0x00, //0x00001f52 jmp          LBB0_435
	//0x00001f57 LBB0_431
	0x48, 0x63, 0xcb, //0x00001f57 movslq       %ebx, %rcx
	0x48, 0x83, 0xc1, 0xff, //0x00001f5a addq         $-1, %rcx
	0x4c, 0x8b, 0x75, 0xc0, //0x00001f5e movq         $-64(%rbp), %r14
	0x4c, 0x8b, 0x5d, 0xc8, //0x00001f62 movq         $-56(%rbp), %r11
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x00001f66 movl         $1, %ebx
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00001f6b jmp          LBB0_433
	//0x00001f70 LBB0_432
	0x48, 0x85, 0xc0, //0x00001f70 testq        %rax, %rax
	0x44, 0x0f, 0x45, 0xe3, //0x00001f73 cmovnel      %ebx, %r12d
	0x48, 0x83, 0xc1, 0xff, //0x00001f77 addq         $-1, %rcx
	0x48, 0x83, 0xfe, 0x09, //0x00001f7b cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x00001f7f movq         %rdx, %rsi
	0x0f, 0x86, 0x42, 0x00, 0x00, 0x00, //0x00001f82 jbe          LBB0_435
	//0x00001f88 LBB0_433
	0x48, 0x89, 0xf0, //0x00001f88 movq         %rsi, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00001f8b movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00001f95 mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00001f98 shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00001f9c leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x3c, 0x80, //0x00001fa0 leaq         (%rax,%rax,4), %rdi
	0x48, 0x89, 0xf0, //0x00001fa4 movq         %rsi, %rax
	0x48, 0x29, 0xf8, //0x00001fa7 subq         %rdi, %rax
	0x4c, 0x39, 0xf1, //0x00001faa cmpq         %r14, %rcx
	0x0f, 0x83, 0xbd, 0xff, 0xff, 0xff, //0x00001fad jae          LBB0_432
	0x04, 0x30, //0x00001fb3 addb         $48, %al
	0x41, 0x88, 0x04, 0x08, //0x00001fb5 movb         %al, (%r8,%rcx)
	0x48, 0x83, 0xc1, 0xff, //0x00001fb9 addq         $-1, %rcx
	0x48, 0x83, 0xfe, 0x09, //0x00001fbd cmpq         $9, %rsi
	0x48, 0x89, 0xd6, //0x00001fc1 movq         %rdx, %rsi
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00001fc4 ja           LBB0_433
	//0x00001fca LBB0_435
	0x48, 0x8b, 0x45, 0xb8, //0x00001fca movq         $-72(%rbp), %rax
	0x41, 0x01, 0xc7, //0x00001fce addl         %eax, %r15d
	0x4d, 0x63, 0xff, //0x00001fd1 movslq       %r15d, %r15
	0x4d, 0x39, 0xfe, //0x00001fd4 cmpq         %r15, %r14
	0x45, 0x0f, 0x46, 0xfe, //0x00001fd7 cmovbel      %r14d, %r15d
	0x41, 0x01, 0xc3, //0x00001fdb addl         %eax, %r11d
	0x45, 0x85, 0xff, //0x00001fde testl        %r15d, %r15d
	0x0f, 0x8e, 0x39, 0x00, 0x00, 0x00, //0x00001fe1 jle          LBB0_440
	0x41, 0x8d, 0x47, 0xff, //0x00001fe7 leal         $-1(%r15), %eax
	0x41, 0x80, 0x3c, 0x00, 0x30, //0x00001feb cmpb         $48, (%r8,%rax)
	0x8b, 0x4d, 0xa4, //0x00001ff0 movl         $-92(%rbp), %ecx
	0x0f, 0x85, 0x36, 0x00, 0x00, 0x00, //0x00001ff3 jne          LBB0_442
	0x44, 0x89, 0xf8, //0x00001ff9 movl         %r15d, %eax
	//0x00001ffc LBB0_438
	0x48, 0x83, 0xf8, 0x01, //0x00001ffc cmpq         $1, %rax
	0x0f, 0x8e, 0x23, 0x00, 0x00, 0x00, //0x00002000 jle          LBB0_441
	0x4c, 0x8d, 0x78, 0xff, //0x00002006 leaq         $-1(%rax), %r15
	0x83, 0xc0, 0xfe, //0x0000200a addl         $-2, %eax
	0x41, 0x80, 0x3c, 0x00, 0x30, //0x0000200d cmpb         $48, (%r8,%rax)
	0x4c, 0x89, 0xf8, //0x00002012 movq         %r15, %rax
	0x0f, 0x84, 0xe1, 0xff, 0xff, 0xff, //0x00002015 je           LBB0_438
	0xe9, 0x0f, 0x00, 0x00, 0x00, //0x0000201b jmp          LBB0_442
	//0x00002020 LBB0_440
	0x8b, 0x4d, 0xa4, //0x00002020 movl         $-92(%rbp), %ecx
	0x0f, 0x85, 0x06, 0x00, 0x00, 0x00, //0x00002023 jne          LBB0_442
	//0x00002029 LBB0_441
	0x45, 0x31, 0xdb, //0x00002029 xorl         %r11d, %r11d
	0x45, 0x31, 0xff, //0x0000202c xorl         %r15d, %r15d
	//0x0000202f LBB0_442
	0x85, 0xc9, //0x0000202f testl        %ecx, %ecx
	0x0f, 0x88, 0x0e, 0x00, 0x00, 0x00, //0x00002031 js           LBB0_444
	0x44, 0x89, 0xff, //0x00002037 movl         %r15d, %edi
	0x45, 0x89, 0xfe, //0x0000203a movl         %r15d, %r14d
	0x41, 0x29, 0xc9, //0x0000203d subl         %ecx, %r9d
	0xe9, 0x4e, 0xfd, 0xff, 0xff, //0x00002040 jmp          LBB0_407
	//0x00002045 LBB0_444
	0x83, 0xf9, 0xc3, //0x00002045 cmpl         $-61, %ecx
	0x0f, 0x8f, 0x30, 0x02, 0x00, 0x00, //0x00002048 jg           LBB0_473
	0x41, 0x89, 0xc9, //0x0000204e movl         %ecx, %r9d
	0xe9, 0x19, 0x00, 0x00, 0x00, //0x00002051 jmp          LBB0_449
	//0x00002056 LBB0_446
	0x45, 0x31, 0xdb, //0x00002056 xorl         %r11d, %r11d
	//0x00002059 LBB0_447
	0x31, 0xc0, //0x00002059 xorl         %eax, %eax
	//0x0000205b LBB0_448
	0x41, 0x8d, 0x49, 0x3c, //0x0000205b leal         $60(%r9), %ecx
	0x41, 0x89, 0xc7, //0x0000205f movl         %eax, %r15d
	0x41, 0x83, 0xf9, 0x88, //0x00002062 cmpl         $-120, %r9d
	0x41, 0x89, 0xc9, //0x00002066 movl         %ecx, %r9d
	0x0f, 0x8d, 0x12, 0x02, 0x00, 0x00, //0x00002069 jge          LBB0_474
	//0x0000206f LBB0_449
	0x45, 0x85, 0xff, //0x0000206f testl        %r15d, %r15d
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00002072 movl         $0, %esi
	0x41, 0x0f, 0x4f, 0xf7, //0x00002077 cmovgl       %r15d, %esi
	0x31, 0xc0, //0x0000207b xorl         %eax, %eax
	0x31, 0xc9, //0x0000207d xorl         %ecx, %ecx
	0x90, //0x0000207f .p2align 4, 0x90
	//0x00002080 LBB0_450
	0x48, 0x39, 0xc6, //0x00002080 cmpq         %rax, %rsi
	0x0f, 0x84, 0x29, 0x00, 0x00, 0x00, //0x00002083 je           LBB0_453
	0x48, 0x8d, 0x0c, 0x89, //0x00002089 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x00, //0x0000208d movsbq       (%r8,%rax), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x00002092 leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002096 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x0000209a addq         $1, %rax
	0x49, 0x8d, 0x52, 0x01, //0x0000209e leaq         $1(%r10), %rdx
	0x48, 0x39, 0xd1, //0x000020a2 cmpq         %rdx, %rcx
	0x0f, 0x82, 0xd5, 0xff, 0xff, 0xff, //0x000020a5 jb           LBB0_450
	0x89, 0xc6, //0x000020ab movl         %eax, %esi
	0xe9, 0x25, 0x00, 0x00, 0x00, //0x000020ad jmp          LBB0_455
	//0x000020b2 LBB0_453
	0x48, 0x85, 0xc9, //0x000020b2 testq        %rcx, %rcx
	0x0f, 0x84, 0x9e, 0xff, 0xff, 0xff, //0x000020b5 je           LBB0_447
	0x90, 0x90, 0x90, 0x90, 0x90, //0x000020bb .p2align 4, 0x90
	//0x000020c0 LBB0_454
	0x48, 0x01, 0xc9, //0x000020c0 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000020c3 leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc6, 0x01, //0x000020c7 addl         $1, %esi
	0x49, 0x8d, 0x42, 0x01, //0x000020ca leaq         $1(%r10), %rax
	0x48, 0x39, 0xc1, //0x000020ce cmpq         %rax, %rcx
	0x0f, 0x82, 0xe9, 0xff, 0xff, 0xff, //0x000020d1 jb           LBB0_454
	//0x000020d7 LBB0_455
	0x41, 0x29, 0xf3, //0x000020d7 subl         %esi, %r11d
	0x44, 0x89, 0xf8, //0x000020da movl         %r15d, %eax
	0x29, 0xf0, //0x000020dd subl         %esi, %eax
	0x0f, 0x8e, 0x26, 0x00, 0x00, 0x00, //0x000020df jle          LBB0_458
	0x4c, 0x89, 0x5d, 0xc8, //0x000020e5 movq         %r11, $-56(%rbp)
	0x48, 0x63, 0xf6, //0x000020e9 movslq       %esi, %rsi
	0x49, 0x63, 0xd7, //0x000020ec movslq       %r15d, %rdx
	0x49, 0x89, 0xd3, //0x000020ef movq         %rdx, %r11
	0x49, 0x29, 0xf3, //0x000020f2 subq         %rsi, %r11
	0x48, 0x89, 0xf7, //0x000020f5 movq         %rsi, %rdi
	0x48, 0xf7, 0xd7, //0x000020f8 notq         %rdi
	0x48, 0x01, 0xd7, //0x000020fb addq         %rdx, %rdi
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x000020fe jne          LBB0_459
	0x31, 0xdb, //0x00002104 xorl         %ebx, %ebx
	0xe9, 0x86, 0x00, 0x00, 0x00, //0x00002106 jmp          LBB0_462
	//0x0000210b LBB0_458
	0x31, 0xc0, //0x0000210b xorl         %eax, %eax
	0xe9, 0xea, 0x00, 0x00, 0x00, //0x0000210d jmp          LBB0_466
	//0x00002112 LBB0_459
	0x4d, 0x89, 0xde, //0x00002112 movq         %r11, %r14
	0x49, 0x83, 0xe6, 0xfe, //0x00002115 andq         $-2, %r14
	0x49, 0xf7, 0xde, //0x00002119 negq         %r14
	0x31, 0xdb, //0x0000211c xorl         %ebx, %ebx
	0x48, 0x8b, 0x55, 0x88, //0x0000211e movq         $-120(%rbp), %rdx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002122 .p2align 4, 0x90
	//0x00002130 LBB0_460
	0x48, 0x89, 0xcf, //0x00002130 movq         %rcx, %rdi
	0x48, 0xc1, 0xef, 0x3c, //0x00002133 shrq         $60, %rdi
	0x4c, 0x21, 0xd1, //0x00002137 andq         %r10, %rcx
	0x40, 0x80, 0xcf, 0x30, //0x0000213a orb          $48, %dil
	0x40, 0x88, 0x7a, 0xff, //0x0000213e movb         %dil, $-1(%rdx)
	0x48, 0x8d, 0x0c, 0x89, //0x00002142 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x7c, 0x32, 0xff, //0x00002146 movsbq       $-1(%rdx,%rsi), %rdi
	0x48, 0x8d, 0x0c, 0x4f, //0x0000214c leaq         (%rdi,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002150 addq         $-48, %rcx
	0x48, 0x89, 0xcf, //0x00002154 movq         %rcx, %rdi
	0x48, 0xc1, 0xef, 0x3c, //0x00002157 shrq         $60, %rdi
	0x4c, 0x21, 0xd1, //0x0000215b andq         %r10, %rcx
	0x40, 0x80, 0xcf, 0x30, //0x0000215e orb          $48, %dil
	0x40, 0x88, 0x3a, //0x00002162 movb         %dil, (%rdx)
	0x48, 0x8d, 0x0c, 0x89, //0x00002165 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x0f, 0xbe, 0x3c, 0x32, //0x00002169 movsbq       (%rdx,%rsi), %rdi
	0x48, 0x8d, 0x0c, 0x4f, //0x0000216e leaq         (%rdi,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002172 addq         $-48, %rcx
	0x48, 0x83, 0xc2, 0x02, //0x00002176 addq         $2, %rdx
	0x48, 0x83, 0xc3, 0xfe, //0x0000217a addq         $-2, %rbx
	0x49, 0x39, 0xde, //0x0000217e cmpq         %rbx, %r14
	0x0f, 0x85, 0xa9, 0xff, 0xff, 0xff, //0x00002181 jne          LBB0_460
	0x48, 0x29, 0xde, //0x00002187 subq         %rbx, %rsi
	0x48, 0xf7, 0xdb, //0x0000218a negq         %rbx
	0x4c, 0x8b, 0x75, 0xc0, //0x0000218d movq         $-64(%rbp), %r14
	//0x00002191 LBB0_462
	0x41, 0xf6, 0xc3, 0x01, //0x00002191 testb        $1, %r11b
	0x0f, 0x84, 0x22, 0x00, 0x00, 0x00, //0x00002195 je           LBB0_464
	0x48, 0x89, 0xca, //0x0000219b movq         %rcx, %rdx
	0x48, 0xc1, 0xea, 0x3c, //0x0000219e shrq         $60, %rdx
	0x80, 0xca, 0x30, //0x000021a2 orb          $48, %dl
	0x41, 0x88, 0x14, 0x18, //0x000021a5 movb         %dl, (%r8,%rbx)
	0x4c, 0x21, 0xd1, //0x000021a9 andq         %r10, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000021ac leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x14, 0x30, //0x000021b0 movsbq       (%r8,%rsi), %rdx
	0x48, 0x8d, 0x0c, 0x4a, //0x000021b5 leaq         (%rdx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000021b9 addq         $-48, %rcx
	//0x000021bd LBB0_464
	0x48, 0x85, 0xc9, //0x000021bd testq        %rcx, %rcx
	0x4c, 0x8b, 0x5d, 0xc8, //0x000021c0 movq         $-56(%rbp), %r11
	0xbb, 0x01, 0x00, 0x00, 0x00, //0x000021c4 movl         $1, %ebx
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x000021c9 jne          LBB0_466
	0xe9, 0x5b, 0x00, 0x00, 0x00, //0x000021cf jmp          LBB0_468
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000021d4 .p2align 4, 0x90
	//0x000021e0 LBB0_465
	0x49, 0x8d, 0x72, 0x01, //0x000021e0 leaq         $1(%r10), %rsi
	0x48, 0x39, 0xf1, //0x000021e4 cmpq         %rsi, %rcx
	0x44, 0x0f, 0x43, 0xe3, //0x000021e7 cmovael      %ebx, %r12d
	0x48, 0x8d, 0x0c, 0x12, //0x000021eb leaq         (%rdx,%rdx), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x000021ef leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xd2, //0x000021f3 testq        %rdx, %rdx
	0x0f, 0x84, 0x33, 0x00, 0x00, 0x00, //0x000021f6 je           LBB0_468
	//0x000021fc LBB0_466
	0x48, 0x89, 0xca, //0x000021fc movq         %rcx, %rdx
	0x4c, 0x21, 0xd2, //0x000021ff andq         %r10, %rdx
	0x48, 0x63, 0xf0, //0x00002202 movslq       %eax, %rsi
	0x49, 0x39, 0xf6, //0x00002205 cmpq         %rsi, %r14
	0x0f, 0x86, 0xd2, 0xff, 0xff, 0xff, //0x00002208 jbe          LBB0_465
	0x48, 0xc1, 0xe9, 0x3c, //0x0000220e shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x00002212 orb          $48, %cl
	0x41, 0x88, 0x0c, 0x30, //0x00002215 movb         %cl, (%r8,%rsi)
	0x83, 0xc6, 0x01, //0x00002219 addl         $1, %esi
	0x89, 0xf0, //0x0000221c movl         %esi, %eax
	0x48, 0x8d, 0x0c, 0x12, //0x0000221e leaq         (%rdx,%rdx), %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002222 leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x85, 0xd2, //0x00002226 testq        %rdx, %rdx
	0x0f, 0x85, 0xcd, 0xff, 0xff, 0xff, //0x00002229 jne          LBB0_466
	//0x0000222f LBB0_468
	0x41, 0x83, 0xc3, 0x01, //0x0000222f addl         $1, %r11d
	0x85, 0xc0, //0x00002233 testl        %eax, %eax
	0x0f, 0x8e, 0x38, 0x00, 0x00, 0x00, //0x00002235 jle          LBB0_472
	0x89, 0xc1, //0x0000223b movl         %eax, %ecx
	0x48, 0x83, 0xc1, 0x01, //0x0000223d addq         $1, %rcx
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00002241 .p2align 4, 0x90
	//0x00002250 LBB0_470
	0x8d, 0x50, 0xff, //0x00002250 leal         $-1(%rax), %edx
	0x41, 0x80, 0x3c, 0x10, 0x30, //0x00002253 cmpb         $48, (%r8,%rdx)
	0x0f, 0x85, 0xfd, 0xfd, 0xff, 0xff, //0x00002258 jne          LBB0_448
	0x48, 0x83, 0xc1, 0xff, //0x0000225e addq         $-1, %rcx
	0x89, 0xd0, //0x00002262 movl         %edx, %eax
	0x48, 0x83, 0xf9, 0x01, //0x00002264 cmpq         $1, %rcx
	0x0f, 0x8f, 0xe2, 0xff, 0xff, 0xff, //0x00002268 jg           LBB0_470
	0xe9, 0xe3, 0xfd, 0xff, 0xff, //0x0000226e jmp          LBB0_446
	//0x00002273 LBB0_472
	0x0f, 0x85, 0xe2, 0xfd, 0xff, 0xff, //0x00002273 jne          LBB0_448
	0xe9, 0xd8, 0xfd, 0xff, 0xff, //0x00002279 jmp          LBB0_446
	//0x0000227e LBB0_473
	0x44, 0x89, 0xf8, //0x0000227e movl         %r15d, %eax
	//0x00002281 LBB0_474
	0xf7, 0xd9, //0x00002281 negl         %ecx
	0x85, 0xc0, //0x00002283 testl        %eax, %eax
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x00002285 movl         $0, %esi
	0x0f, 0x4f, 0xf0, //0x0000228a cmovgl       %eax, %esi
	0x31, 0xff, //0x0000228d xorl         %edi, %edi
	0x31, 0xdb, //0x0000228f xorl         %ebx, %ebx
	0x4c, 0x8b, 0x4d, 0x98, //0x00002291 movq         $-104(%rbp), %r9
	//0x00002295 LBB0_475
	0x48, 0x39, 0xfe, //0x00002295 cmpq         %rdi, %rsi
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x00002298 je           LBB0_481
	0x48, 0x8d, 0x14, 0x9b, //0x0000229e leaq         (%rbx,%rbx,4), %rdx
	0x49, 0x0f, 0xbe, 0x1c, 0x38, //0x000022a2 movsbq       (%r8,%rdi), %rbx
	0x48, 0x8d, 0x1c, 0x53, //0x000022a7 leaq         (%rbx,%rdx,2), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x000022ab addq         $-48, %rbx
	0x48, 0x83, 0xc7, 0x01, //0x000022af addq         $1, %rdi
	0x48, 0x89, 0xda, //0x000022b3 movq         %rbx, %rdx
	0x48, 0xd3, 0xea, //0x000022b6 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x000022b9 testq        %rdx, %rdx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x000022bc je           LBB0_475
	0x89, 0xfe, //0x000022c2 movl         %edi, %esi
	//0x000022c4 LBB0_478
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x000022c4 movq         $-1, %r13
	0x49, 0xd3, 0xe5, //0x000022cb shlq         %cl, %r13
	0x49, 0xf7, 0xd5, //0x000022ce notq         %r13
	0x41, 0x89, 0xc7, //0x000022d1 movl         %eax, %r15d
	0x41, 0x29, 0xf7, //0x000022d4 subl         %esi, %r15d
	0x0f, 0x8e, 0x80, 0x00, 0x00, 0x00, //0x000022d7 jle          LBB0_486
	0x4c, 0x63, 0xf6, //0x000022dd movslq       %esi, %r14
	0x48, 0x98, //0x000022e0 cltq         
	0x49, 0x89, 0xc1, //0x000022e2 movq         %rax, %r9
	0x4d, 0x29, 0xf1, //0x000022e5 subq         %r14, %r9
	0x4c, 0x89, 0xf2, //0x000022e8 movq         %r14, %rdx
	0x48, 0xf7, 0xd2, //0x000022eb notq         %rdx
	0x48, 0x01, 0xc2, //0x000022ee addq         %rax, %rdx
	0x0f, 0x85, 0x6e, 0x00, 0x00, 0x00, //0x000022f1 jne          LBB0_487
	0x31, 0xc0, //0x000022f7 xorl         %eax, %eax
	0xe9, 0xd8, 0x00, 0x00, 0x00, //0x000022f9 jmp          LBB0_490
	//0x000022fe LBB0_481
	0x48, 0x85, 0xdb, //0x000022fe testq        %rbx, %rbx
	0x0f, 0x84, 0x9d, 0x01, 0x00, 0x00, //0x00002301 je           LBB0_506
	0x48, 0x89, 0xdf, //0x00002307 movq         %rbx, %rdi
	0x48, 0xd3, 0xef, //0x0000230a shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x0000230d testq        %rdi, %rdi
	0x0f, 0x84, 0x1c, 0x00, 0x00, 0x00, //0x00002310 je           LBB0_484
	0x49, 0xc7, 0xc5, 0xff, 0xff, 0xff, 0xff, //0x00002316 movq         $-1, %r13
	0x49, 0xd3, 0xe5, //0x0000231d shlq         %cl, %r13
	0x49, 0xf7, 0xd5, //0x00002320 notq         %r13
	0x41, 0x29, 0xf3, //0x00002323 subl         %esi, %r11d
	0x41, 0x83, 0xc3, 0x01, //0x00002326 addl         $1, %r11d
	0x45, 0x31, 0xff, //0x0000232a xorl         %r15d, %r15d
	0xe9, 0xe3, 0x00, 0x00, 0x00, //0x0000232d jmp          LBB0_494
	//0x00002332 LBB0_484
	0x48, 0x01, 0xdb, //0x00002332 addq         %rbx, %rbx
	0x48, 0x8d, 0x1c, 0x9b, //0x00002335 leaq         (%rbx,%rbx,4), %rbx
	0x83, 0xc6, 0x01, //0x00002339 addl         $1, %esi
	0x48, 0x89, 0xdf, //0x0000233c movq         %rbx, %rdi
	0x48, 0xd3, 0xef, //0x0000233f shrq         %cl, %rdi
	0x48, 0x85, 0xff, //0x00002342 testq        %rdi, %rdi
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x00002345 je           LBB0_484
	0xe9, 0x74, 0xff, 0xff, 0xff, //0x0000234b jmp          LBB0_478
	//0x00002350 LBB0_485
	0x31, 0xff, //0x00002350 xorl         %edi, %edi
	0x45, 0x31, 0xf6, //0x00002352 xorl         %r14d, %r14d
	0x41, 0x29, 0xc9, //0x00002355 subl         %ecx, %r9d
	0xe9, 0x36, 0xfa, 0xff, 0xff, //0x00002358 jmp          LBB0_407
	//0x0000235d LBB0_486
	0x45, 0x31, 0xff, //0x0000235d xorl         %r15d, %r15d
	0xe9, 0xa4, 0x00, 0x00, 0x00, //0x00002360 jmp          LBB0_493
	//0x00002365 LBB0_487
	0x4c, 0x89, 0x5d, 0xc8, //0x00002365 movq         %r11, $-56(%rbp)
	0x4d, 0x89, 0xcb, //0x00002369 movq         %r9, %r11
	0x49, 0x83, 0xe3, 0xfe, //0x0000236c andq         $-2, %r11
	0x49, 0xf7, 0xdb, //0x00002370 negq         %r11
	0x31, 0xc0, //0x00002373 xorl         %eax, %eax
	0x48, 0x8b, 0x55, 0x88, //0x00002375 movq         $-120(%rbp), %rdx
	//0x00002379 LBB0_488
	0x48, 0x89, 0xdf, //0x00002379 movq         %rbx, %rdi
	0x48, 0xd3, 0xef, //0x0000237c shrq         %cl, %rdi
	0x4c, 0x21, 0xeb, //0x0000237f andq         %r13, %rbx
	0x40, 0x80, 0xc7, 0x30, //0x00002382 addb         $48, %dil
	0x40, 0x88, 0x7a, 0xff, //0x00002386 movb         %dil, $-1(%rdx)
	0x48, 0x8d, 0x3c, 0x9b, //0x0000238a leaq         (%rbx,%rbx,4), %rdi
	0x4a, 0x0f, 0xbe, 0x5c, 0x32, 0xff, //0x0000238e movsbq       $-1(%rdx,%r14), %rbx
	0x48, 0x8d, 0x3c, 0x7b, //0x00002394 leaq         (%rbx,%rdi,2), %rdi
	0x48, 0x83, 0xc7, 0xd0, //0x00002398 addq         $-48, %rdi
	0x48, 0x89, 0xfb, //0x0000239c movq         %rdi, %rbx
	0x48, 0xd3, 0xeb, //0x0000239f shrq         %cl, %rbx
	0x4c, 0x21, 0xef, //0x000023a2 andq         %r13, %rdi
	0x80, 0xc3, 0x30, //0x000023a5 addb         $48, %bl
	0x88, 0x1a, //0x000023a8 movb         %bl, (%rdx)
	0x48, 0x8d, 0x3c, 0xbf, //0x000023aa leaq         (%rdi,%rdi,4), %rdi
	0x4a, 0x0f, 0xbe, 0x1c, 0x32, //0x000023ae movsbq       (%rdx,%r14), %rbx
	0x48, 0x8d, 0x1c, 0x7b, //0x000023b3 leaq         (%rbx,%rdi,2), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x000023b7 addq         $-48, %rbx
	0x48, 0x83, 0xc2, 0x02, //0x000023bb addq         $2, %rdx
	0x48, 0x83, 0xc0, 0xfe, //0x000023bf addq         $-2, %rax
	0x49, 0x39, 0xc3, //0x000023c3 cmpq         %rax, %r11
	0x0f, 0x85, 0xad, 0xff, 0xff, 0xff, //0x000023c6 jne          LBB0_488
	0x49, 0x29, 0xc6, //0x000023cc subq         %rax, %r14
	0x48, 0xf7, 0xd8, //0x000023cf negq         %rax
	0x4c, 0x8b, 0x5d, 0xc8, //0x000023d2 movq         $-56(%rbp), %r11
	//0x000023d6 LBB0_490
	0x41, 0xf6, 0xc1, 0x01, //0x000023d6 testb        $1, %r9b
	0x0f, 0x84, 0x21, 0x00, 0x00, 0x00, //0x000023da je           LBB0_492
	0x48, 0x89, 0xda, //0x000023e0 movq         %rbx, %rdx
	0x48, 0xd3, 0xea, //0x000023e3 shrq         %cl, %rdx
	0x80, 0xc2, 0x30, //0x000023e6 addb         $48, %dl
	0x41, 0x88, 0x14, 0x00, //0x000023e9 movb         %dl, (%r8,%rax)
	0x4c, 0x21, 0xeb, //0x000023ed andq         %r13, %rbx
	0x48, 0x8d, 0x04, 0x9b, //0x000023f0 leaq         (%rbx,%rbx,4), %rax
	0x4b, 0x0f, 0xbe, 0x14, 0x30, //0x000023f4 movsbq       (%r8,%r14), %rdx
	0x48, 0x8d, 0x1c, 0x42, //0x000023f9 leaq         (%rdx,%rax,2), %rbx
	0x48, 0x83, 0xc3, 0xd0, //0x000023fd addq         $-48, %rbx
	//0x00002401 LBB0_492
	0x4c, 0x8b, 0x75, 0xc0, //0x00002401 movq         $-64(%rbp), %r14
	0x4c, 0x8b, 0x4d, 0x98, //0x00002405 movq         $-104(%rbp), %r9
	//0x00002409 LBB0_493
	0x41, 0x29, 0xf3, //0x00002409 subl         %esi, %r11d
	0x41, 0x83, 0xc3, 0x01, //0x0000240c addl         $1, %r11d
	0xe9, 0x39, 0x00, 0x00, 0x00, //0x00002410 jmp          LBB0_498
	//0x00002415 LBB0_494
	0x48, 0x89, 0xd8, //0x00002415 movq         %rbx, %rax
	0x48, 0xd3, 0xe8, //0x00002418 shrq         %cl, %rax
	0x4c, 0x21, 0xeb, //0x0000241b andq         %r13, %rbx
	0x49, 0x63, 0xf7, //0x0000241e movslq       %r15d, %rsi
	0x49, 0x39, 0xf6, //0x00002421 cmpq         %rsi, %r14
	0x0f, 0x86, 0x11, 0x00, 0x00, 0x00, //0x00002424 jbe          LBB0_496
	0x04, 0x30, //0x0000242a addb         $48, %al
	0x41, 0x88, 0x04, 0x30, //0x0000242c movb         %al, (%r8,%rsi)
	0x83, 0xc6, 0x01, //0x00002430 addl         $1, %esi
	0x41, 0x89, 0xf7, //0x00002433 movl         %esi, %r15d
	0xe9, 0x0c, 0x00, 0x00, 0x00, //0x00002436 jmp          LBB0_497
	//0x0000243b LBB0_496
	0x48, 0x85, 0xc0, //0x0000243b testq        %rax, %rax
	0xb8, 0x01, 0x00, 0x00, 0x00, //0x0000243e movl         $1, %eax
	0x44, 0x0f, 0x45, 0xe0, //0x00002443 cmovnel      %eax, %r12d
	//0x00002447 LBB0_497
	0x48, 0x01, 0xdb, //0x00002447 addq         %rbx, %rbx
	0x48, 0x8d, 0x1c, 0x9b, //0x0000244a leaq         (%rbx,%rbx,4), %rbx
	//0x0000244e LBB0_498
	0x48, 0x85, 0xdb, //0x0000244e testq        %rbx, %rbx
	0x0f, 0x85, 0xbe, 0xff, 0xff, 0xff, //0x00002451 jne          LBB0_494
	0x45, 0x85, 0xff, //0x00002457 testl        %r15d, %r15d
	0x4c, 0x8b, 0x6d, 0xa8, //0x0000245a movq         $-88(%rbp), %r13
	0x0f, 0x8e, 0x2c, 0x00, 0x00, 0x00, //0x0000245e jle          LBB0_503
	0x44, 0x89, 0xf8, //0x00002464 movl         %r15d, %eax
	0x48, 0x83, 0xc0, 0x01, //0x00002467 addq         $1, %rax
	//0x0000246b LBB0_501
	0x41, 0x8d, 0x4f, 0xff, //0x0000246b leal         $-1(%r15), %ecx
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x0000246f cmpb         $48, (%r8,%rcx)
	0x0f, 0x85, 0x1c, 0x00, 0x00, 0x00, //0x00002474 jne          LBB0_504
	0x48, 0x83, 0xc0, 0xff, //0x0000247a addq         $-1, %rax
	0x41, 0x89, 0xcf, //0x0000247e movl         %ecx, %r15d
	0x48, 0x83, 0xf8, 0x01, //0x00002481 cmpq         $1, %rax
	0x0f, 0x8f, 0xe0, 0xff, 0xff, 0xff, //0x00002485 jg           LBB0_501
	0xe9, 0x11, 0x00, 0x00, 0x00, //0x0000248b jmp          LBB0_505
	//0x00002490 LBB0_503
	0x0f, 0x84, 0x0b, 0x00, 0x00, 0x00, //0x00002490 je           LBB0_505
	//0x00002496 LBB0_504
	0x44, 0x89, 0xff, //0x00002496 movl         %r15d, %edi
	0x45, 0x89, 0xfe, //0x00002499 movl         %r15d, %r14d
	0xe9, 0x0b, 0x00, 0x00, 0x00, //0x0000249c jmp          LBB0_507
	//0x000024a1 LBB0_505
	0x45, 0x31, 0xdb, //0x000024a1 xorl         %r11d, %r11d
	//0x000024a4 LBB0_506
	0x45, 0x31, 0xff, //0x000024a4 xorl         %r15d, %r15d
	0x31, 0xff, //0x000024a7 xorl         %edi, %edi
	0x45, 0x31, 0xf6, //0x000024a9 xorl         %r14d, %r14d
	//0x000024ac LBB0_507
	0x8b, 0x4d, 0xa4, //0x000024ac movl         $-92(%rbp), %ecx
	0x41, 0x29, 0xc9, //0x000024af subl         %ecx, %r9d
	0xe9, 0xdc, 0xf8, 0xff, 0xff, //0x000024b2 jmp          LBB0_407
	//0x000024b7 LBB0_508
	0x41, 0x81, 0xf9, 0x02, 0xfc, 0xff, 0xff, //0x000024b7 cmpl         $-1022, %r9d
	0x0f, 0x8f, 0xc2, 0x01, 0x00, 0x00, //0x000024be jg           LBB0_536
	0xb9, 0x02, 0xfc, 0xff, 0xff, //0x000024c4 movl         $-1022, %ecx
	0x45, 0x85, 0xf6, //0x000024c9 testl        %r14d, %r14d
	0x0f, 0x84, 0xe2, 0x01, 0x00, 0x00, //0x000024cc je           LBB0_539
	0x4c, 0x89, 0x5d, 0xc8, //0x000024d2 movq         %r11, $-56(%rbp)
	0x4c, 0x89, 0xc8, //0x000024d6 movq         %r9, %rax
	0x41, 0x81, 0xc1, 0xfd, 0x03, 0x00, 0x00, //0x000024d9 addl         $1021, %r9d
	0x3d, 0xc6, 0xfb, 0xff, 0xff, //0x000024e0 cmpl         $-1082, %eax
	0x0f, 0x8f, 0xd0, 0x01, 0x00, 0x00, //0x000024e5 jg           LBB0_540
	0x4d, 0x8d, 0x6a, 0x01, //0x000024eb leaq         $1(%r10), %r13
	0x41, 0xbb, 0x01, 0x00, 0x00, 0x00, //0x000024ef movl         $1, %r11d
	0xe9, 0x20, 0x00, 0x00, 0x00, //0x000024f5 jmp          LBB0_515
	//0x000024fa LBB0_512
	0x31, 0xc0, //0x000024fa xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xc8, //0x000024fc movq         %rax, $-56(%rbp)
	//0x00002500 LBB0_513
	0x45, 0x31, 0xff, //0x00002500 xorl         %r15d, %r15d
	//0x00002503 LBB0_514
	0x41, 0x8d, 0x49, 0x3c, //0x00002503 leal         $60(%r9), %ecx
	0x45, 0x89, 0xfe, //0x00002507 movl         %r15d, %r14d
	0x44, 0x89, 0xff, //0x0000250a movl         %r15d, %edi
	0x41, 0x83, 0xf9, 0x88, //0x0000250d cmpl         $-120, %r9d
	0x41, 0x89, 0xc9, //0x00002511 movl         %ecx, %r9d
	0x0f, 0x8d, 0xa7, 0x01, 0x00, 0x00, //0x00002514 jge          LBB0_541
	//0x0000251a LBB0_515
	0x45, 0x85, 0xf6, //0x0000251a testl        %r14d, %r14d
	0xbf, 0x00, 0x00, 0x00, 0x00, //0x0000251d movl         $0, %edi
	0x41, 0x0f, 0x4f, 0xfe, //0x00002522 cmovgl       %r14d, %edi
	0x31, 0xc0, //0x00002526 xorl         %eax, %eax
	0x31, 0xc9, //0x00002528 xorl         %ecx, %ecx
	//0x0000252a LBB0_516
	0x48, 0x39, 0xc7, //0x0000252a cmpq         %rax, %rdi
	0x0f, 0x84, 0x25, 0x00, 0x00, 0x00, //0x0000252d je           LBB0_519
	0x48, 0x8d, 0x0c, 0x89, //0x00002533 leaq         (%rcx,%rcx,4), %rcx
	0x49, 0x0f, 0xbe, 0x1c, 0x00, //0x00002537 movsbq       (%r8,%rax), %rbx
	0x48, 0x8d, 0x0c, 0x4b, //0x0000253c leaq         (%rbx,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x00002540 addq         $-48, %rcx
	0x48, 0x83, 0xc0, 0x01, //0x00002544 addq         $1, %rax
	0x4c, 0x39, 0xe9, //0x00002548 cmpq         %r13, %rcx
	0x0f, 0x82, 0xd9, 0xff, 0xff, 0xff, //0x0000254b jb           LBB0_516
	0x89, 0xc7, //0x00002551 movl         %eax, %edi
	0xe9, 0x1c, 0x00, 0x00, 0x00, //0x00002553 jmp          LBB0_521
	//0x00002558 LBB0_519
	0x48, 0x85, 0xc9, //0x00002558 testq        %rcx, %rcx
	0x0f, 0x84, 0x9f, 0xff, 0xff, 0xff, //0x0000255b je           LBB0_513
	//0x00002561 LBB0_520
	0x48, 0x01, 0xc9, //0x00002561 addq         %rcx, %rcx
	0x48, 0x8d, 0x0c, 0x89, //0x00002564 leaq         (%rcx,%rcx,4), %rcx
	0x83, 0xc7, 0x01, //0x00002568 addl         $1, %edi
	0x4c, 0x39, 0xe9, //0x0000256b cmpq         %r13, %rcx
	0x0f, 0x82, 0xed, 0xff, 0xff, 0xff, //0x0000256e jb           LBB0_520
	//0x00002574 LBB0_521
	0x48, 0x8b, 0x45, 0xc8, //0x00002574 movq         $-56(%rbp), %rax
	0x29, 0xf8, //0x00002578 subl         %edi, %eax
	0x48, 0x89, 0x45, 0xc8, //0x0000257a movq         %rax, $-56(%rbp)
	0x31, 0xc0, //0x0000257e xorl         %eax, %eax
	0x44, 0x39, 0xf7, //0x00002580 cmpl         %r14d, %edi
	0x0f, 0x8d, 0x5a, 0x00, 0x00, 0x00, //0x00002583 jge          LBB0_526
	0x48, 0x63, 0xff, //0x00002589 movslq       %edi, %rdi
	0x49, 0x63, 0xdf, //0x0000258c movslq       %r15d, %rbx
	0x49, 0x8d, 0x04, 0x38, //0x0000258f leaq         (%r8,%rdi), %rax
	0x45, 0x31, 0xff, //0x00002593 xorl         %r15d, %r15d
	0x4c, 0x8b, 0x75, 0xc0, //0x00002596 movq         $-64(%rbp), %r14
	//0x0000259a LBB0_523
	0x48, 0x89, 0xce, //0x0000259a movq         %rcx, %rsi
	0x48, 0xc1, 0xee, 0x3c, //0x0000259d shrq         $60, %rsi
	0x4c, 0x21, 0xd1, //0x000025a1 andq         %r10, %rcx
	0x40, 0x80, 0xce, 0x30, //0x000025a4 orb          $48, %sil
	0x43, 0x88, 0x34, 0x38, //0x000025a8 movb         %sil, (%r8,%r15)
	0x4a, 0x0f, 0xbe, 0x34, 0x38, //0x000025ac movsbq       (%rax,%r15), %rsi
	0x4a, 0x8d, 0x14, 0x3f, //0x000025b1 leaq         (%rdi,%r15), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x000025b5 addq         $1, %rdx
	0x49, 0x83, 0xc7, 0x01, //0x000025b9 addq         $1, %r15
	0x48, 0x8d, 0x0c, 0x89, //0x000025bd leaq         (%rcx,%rcx,4), %rcx
	0x48, 0x8d, 0x0c, 0x4e, //0x000025c1 leaq         (%rsi,%rcx,2), %rcx
	0x48, 0x83, 0xc1, 0xd0, //0x000025c5 addq         $-48, %rcx
	0x48, 0x39, 0xda, //0x000025c9 cmpq         %rbx, %rdx
	0x0f, 0x8c, 0xc8, 0xff, 0xff, 0xff, //0x000025cc jl           LBB0_523
	0x48, 0x85, 0xc9, //0x000025d2 testq        %rcx, %rcx
	0x0f, 0x84, 0x60, 0x00, 0x00, 0x00, //0x000025d5 je           LBB0_531
	0x44, 0x89, 0xf8, //0x000025db movl         %r15d, %eax
	0xe9, 0x04, 0x00, 0x00, 0x00, //0x000025de jmp          LBB0_527
	//0x000025e3 LBB0_526
	0x4c, 0x8b, 0x75, 0xc0, //0x000025e3 movq         $-64(%rbp), %r14
	//0x000025e7 LBB0_527
	0x41, 0x89, 0xc7, //0x000025e7 movl         %eax, %r15d
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x000025ea jmp          LBB0_529
	//0x000025ef LBB0_528
	0x4c, 0x39, 0xe9, //0x000025ef cmpq         %r13, %rcx
	0x45, 0x0f, 0x43, 0xe3, //0x000025f2 cmovael      %r11d, %r12d
	0x48, 0x8d, 0x04, 0x3f, //0x000025f6 leaq         (%rdi,%rdi), %rax
	0x48, 0x8d, 0x0c, 0x80, //0x000025fa leaq         (%rax,%rax,4), %rcx
	0x48, 0x85, 0xff, //0x000025fe testq        %rdi, %rdi
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x00002601 je           LBB0_531
	//0x00002607 LBB0_529
	0x48, 0x89, 0xcf, //0x00002607 movq         %rcx, %rdi
	0x4c, 0x21, 0xd7, //0x0000260a andq         %r10, %rdi
	0x49, 0x63, 0xc7, //0x0000260d movslq       %r15d, %rax
	0x49, 0x39, 0xc6, //0x00002610 cmpq         %rax, %r14
	0x0f, 0x86, 0xd6, 0xff, 0xff, 0xff, //0x00002613 jbe          LBB0_528
	0x48, 0xc1, 0xe9, 0x3c, //0x00002619 shrq         $60, %rcx
	0x80, 0xc9, 0x30, //0x0000261d orb          $48, %cl
	0x41, 0x88, 0x0c, 0x00, //0x00002620 movb         %cl, (%r8,%rax)
	0x83, 0xc0, 0x01, //0x00002624 addl         $1, %eax
	0x41, 0x89, 0xc7, //0x00002627 movl         %eax, %r15d
	0x48, 0x8d, 0x04, 0x3f, //0x0000262a leaq         (%rdi,%rdi), %rax
	0x48, 0x8d, 0x0c, 0x80, //0x0000262e leaq         (%rax,%rax,4), %rcx
	0x48, 0x85, 0xff, //0x00002632 testq        %rdi, %rdi
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x00002635 jne          LBB0_529
	//0x0000263b LBB0_531
	0x48, 0x8b, 0x45, 0xc8, //0x0000263b movq         $-56(%rbp), %rax
	0x83, 0xc0, 0x01, //0x0000263f addl         $1, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00002642 movq         %rax, $-56(%rbp)
	0x45, 0x85, 0xff, //0x00002646 testl        %r15d, %r15d
	0x0f, 0x8e, 0x2c, 0x00, 0x00, 0x00, //0x00002649 jle          LBB0_535
	0x44, 0x89, 0xf8, //0x0000264f movl         %r15d, %eax
	0x48, 0x83, 0xc0, 0x01, //0x00002652 addq         $1, %rax
	//0x00002656 LBB0_533
	0x41, 0x8d, 0x4f, 0xff, //0x00002656 leal         $-1(%r15), %ecx
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x0000265a cmpb         $48, (%r8,%rcx)
	0x0f, 0x85, 0x9e, 0xfe, 0xff, 0xff, //0x0000265f jne          LBB0_514
	0x48, 0x83, 0xc0, 0xff, //0x00002665 addq         $-1, %rax
	0x41, 0x89, 0xcf, //0x00002669 movl         %ecx, %r15d
	0x48, 0x83, 0xf8, 0x01, //0x0000266c cmpq         $1, %rax
	0x0f, 0x8f, 0xe0, 0xff, 0xff, 0xff, //0x00002670 jg           LBB0_533
	0xe9, 0x7f, 0xfe, 0xff, 0xff, //0x00002676 jmp          LBB0_512
	//0x0000267b LBB0_535
	0x0f, 0x85, 0x82, 0xfe, 0xff, 0xff, //0x0000267b jne          LBB0_514
	0xe9, 0x74, 0xfe, 0xff, 0xff, //0x00002681 jmp          LBB0_512
	//0x00002686 LBB0_536
	0x41, 0x81, 0xf9, 0x00, 0x04, 0x00, 0x00, //0x00002686 cmpl         $1024, %r9d
	0x0f, 0x8e, 0x11, 0x00, 0x00, 0x00, //0x0000268d jle          LBB0_538
	0x31, 0xc9, //0x00002693 xorl         %ecx, %ecx
	0x48, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002695 movabsq      $9218868437227405312, %rbx
	0xe9, 0x09, 0x08, 0x00, 0x00, //0x0000269f jmp          LBB0_635
	//0x000026a4 LBB0_538
	0x4c, 0x89, 0x5d, 0xc8, //0x000026a4 movq         %r11, $-56(%rbp)
	0x41, 0x83, 0xc1, 0xff, //0x000026a8 addl         $-1, %r9d
	0x44, 0x89, 0xc9, //0x000026ac movl         %r9d, %ecx
	0xe9, 0x2b, 0x02, 0x00, 0x00, //0x000026af jmp          LBB0_568
	//0x000026b4 LBB0_539
	0x31, 0xc0, //0x000026b4 xorl         %eax, %eax
	0xe9, 0xa7, 0x04, 0x00, 0x00, //0x000026b6 jmp          LBB0_604
	//0x000026bb LBB0_540
	0x44, 0x89, 0xf7, //0x000026bb movl         %r14d, %edi
	0x44, 0x89, 0xc9, //0x000026be movl         %r9d, %ecx
	//0x000026c1 LBB0_541
	0xf7, 0xd9, //0x000026c1 negl         %ecx
	0x31, 0xd2, //0x000026c3 xorl         %edx, %edx
	0x85, 0xff, //0x000026c5 testl        %edi, %edi
	0xbe, 0x00, 0x00, 0x00, 0x00, //0x000026c7 movl         $0, %esi
	0x0f, 0x4f, 0xf7, //0x000026cc cmovgl       %edi, %esi
	0x31, 0xc0, //0x000026cf xorl         %eax, %eax
	//0x000026d1 LBB0_542
	0x48, 0x39, 0xd6, //0x000026d1 cmpq         %rdx, %rsi
	0x0f, 0x84, 0x9e, 0x00, 0x00, 0x00, //0x000026d4 je           LBB0_550
	0x48, 0x8d, 0x04, 0x80, //0x000026da leaq         (%rax,%rax,4), %rax
	0x49, 0x0f, 0xbe, 0x1c, 0x10, //0x000026de movsbq       (%r8,%rdx), %rbx
	0x48, 0x8d, 0x04, 0x43, //0x000026e3 leaq         (%rbx,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x000026e7 addq         $-48, %rax
	0x48, 0x83, 0xc2, 0x01, //0x000026eb addq         $1, %rdx
	0x48, 0x89, 0xc3, //0x000026ef movq         %rax, %rbx
	0x48, 0xd3, 0xeb, //0x000026f2 shrq         %cl, %rbx
	0x48, 0x85, 0xdb, //0x000026f5 testq        %rbx, %rbx
	0x0f, 0x84, 0xd3, 0xff, 0xff, 0xff, //0x000026f8 je           LBB0_542
	0x89, 0xd6, //0x000026fe movl         %edx, %esi
	//0x00002700 LBB0_545
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002700 movq         $-1, %r14
	0x49, 0xd3, 0xe6, //0x00002707 shlq         %cl, %r14
	0x49, 0xf7, 0xd6, //0x0000270a notq         %r14
	0x31, 0xdb, //0x0000270d xorl         %ebx, %ebx
	0x39, 0xfe, //0x0000270f cmpl         %edi, %esi
	0x0f, 0x8d, 0x43, 0x00, 0x00, 0x00, //0x00002711 jge          LBB0_548
	0x4c, 0x63, 0xd6, //0x00002717 movslq       %esi, %r10
	0x4d, 0x63, 0xcf, //0x0000271a movslq       %r15d, %r9
	0x4f, 0x8d, 0x1c, 0x10, //0x0000271d leaq         (%r8,%r10), %r11
	0x31, 0xdb, //0x00002721 xorl         %ebx, %ebx
	//0x00002723 LBB0_547
	0x48, 0x89, 0xc7, //0x00002723 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x00002726 shrq         %cl, %rdi
	0x4c, 0x21, 0xf0, //0x00002729 andq         %r14, %rax
	0x40, 0x80, 0xc7, 0x30, //0x0000272c addb         $48, %dil
	0x41, 0x88, 0x3c, 0x18, //0x00002730 movb         %dil, (%r8,%rbx)
	0x49, 0x0f, 0xbe, 0x3c, 0x1b, //0x00002734 movsbq       (%r11,%rbx), %rdi
	0x49, 0x8d, 0x14, 0x1a, //0x00002739 leaq         (%r10,%rbx), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x0000273d addq         $1, %rdx
	0x48, 0x83, 0xc3, 0x01, //0x00002741 addq         $1, %rbx
	0x48, 0x8d, 0x04, 0x80, //0x00002745 leaq         (%rax,%rax,4), %rax
	0x48, 0x8d, 0x04, 0x47, //0x00002749 leaq         (%rdi,%rax,2), %rax
	0x48, 0x83, 0xc0, 0xd0, //0x0000274d addq         $-48, %rax
	0x4c, 0x39, 0xca, //0x00002751 cmpq         %r9, %rdx
	0x0f, 0x8c, 0xc9, 0xff, 0xff, 0xff, //0x00002754 jl           LBB0_547
	//0x0000275a LBB0_548
	0x48, 0x8b, 0x55, 0xc8, //0x0000275a movq         $-56(%rbp), %rdx
	0x29, 0xf2, //0x0000275e subl         %esi, %edx
	0x83, 0xc2, 0x01, //0x00002760 addl         $1, %edx
	0x48, 0x89, 0x55, 0xc8, //0x00002763 movq         %rdx, $-56(%rbp)
	0x48, 0x85, 0xc0, //0x00002767 testq        %rax, %rax
	0x0f, 0x85, 0x3c, 0x00, 0x00, 0x00, //0x0000276a jne          LBB0_553
	0x41, 0x89, 0xdf, //0x00002770 movl         %ebx, %r15d
	0xe9, 0x8c, 0x00, 0x00, 0x00, //0x00002773 jmp          LBB0_557
	//0x00002778 LBB0_550
	0x48, 0x85, 0xc0, //0x00002778 testq        %rax, %rax
	0x0f, 0x84, 0xd6, 0x03, 0x00, 0x00, //0x0000277b je           LBB0_603
	0x48, 0x89, 0xc2, //0x00002781 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002784 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00002787 testq        %rdx, %rdx
	0x0f, 0x84, 0xd7, 0x00, 0x00, 0x00, //0x0000278a je           LBB0_562
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00002790 movq         $-1, %r14
	0x49, 0xd3, 0xe6, //0x00002797 shlq         %cl, %r14
	0x49, 0xf7, 0xd6, //0x0000279a notq         %r14
	0x48, 0x8b, 0x55, 0xc8, //0x0000279d movq         $-56(%rbp), %rdx
	0x29, 0xf2, //0x000027a1 subl         %esi, %edx
	0x83, 0xc2, 0x01, //0x000027a3 addl         $1, %edx
	0x48, 0x89, 0x55, 0xc8, //0x000027a6 movq         %rdx, $-56(%rbp)
	0x31, 0xdb, //0x000027aa xorl         %ebx, %ebx
	//0x000027ac LBB0_553
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000027ac movl         $1, %esi
	0x41, 0x89, 0xdf, //0x000027b1 movl         %ebx, %r15d
	0xe9, 0x17, 0x00, 0x00, 0x00, //0x000027b4 jmp          LBB0_555
	//0x000027b9 LBB0_554
	0x48, 0x85, 0xff, //0x000027b9 testq        %rdi, %rdi
	0x44, 0x0f, 0x45, 0xe6, //0x000027bc cmovnel      %esi, %r12d
	0x48, 0x01, 0xc0, //0x000027c0 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000027c3 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x000027c7 testq        %rax, %rax
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x000027ca je           LBB0_557
	//0x000027d0 LBB0_555
	0x48, 0x89, 0xc7, //0x000027d0 movq         %rax, %rdi
	0x48, 0xd3, 0xef, //0x000027d3 shrq         %cl, %rdi
	0x4c, 0x21, 0xf0, //0x000027d6 andq         %r14, %rax
	0x49, 0x63, 0xdf, //0x000027d9 movslq       %r15d, %rbx
	0x48, 0x39, 0x5d, 0xc0, //0x000027dc cmpq         %rbx, $-64(%rbp)
	0x0f, 0x86, 0xd3, 0xff, 0xff, 0xff, //0x000027e0 jbe          LBB0_554
	0x40, 0x80, 0xc7, 0x30, //0x000027e6 addb         $48, %dil
	0x41, 0x88, 0x3c, 0x18, //0x000027ea movb         %dil, (%r8,%rbx)
	0x83, 0xc3, 0x01, //0x000027ee addl         $1, %ebx
	0x41, 0x89, 0xdf, //0x000027f1 movl         %ebx, %r15d
	0x48, 0x01, 0xc0, //0x000027f4 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x000027f7 leaq         (%rax,%rax,4), %rax
	0x48, 0x85, 0xc0, //0x000027fb testq        %rax, %rax
	0x0f, 0x85, 0xcc, 0xff, 0xff, 0xff, //0x000027fe jne          LBB0_555
	//0x00002804 LBB0_557
	0x45, 0x85, 0xff, //0x00002804 testl        %r15d, %r15d
	0x0f, 0x8e, 0x8c, 0x00, 0x00, 0x00, //0x00002807 jle          LBB0_564
	0x44, 0x89, 0xf8, //0x0000280d movl         %r15d, %eax
	0x48, 0x83, 0xc0, 0x01, //0x00002810 addq         $1, %rax
	//0x00002814 LBB0_559
	0x41, 0x8d, 0x4f, 0xff, //0x00002814 leal         $-1(%r15), %ecx
	0x41, 0x80, 0x3c, 0x08, 0x30, //0x00002818 cmpb         $48, (%r8,%rcx)
	0x0f, 0x85, 0xb4, 0x00, 0x00, 0x00, //0x0000281d jne          LBB0_567
	0x48, 0x83, 0xc0, 0xff, //0x00002823 addq         $-1, %rax
	0x41, 0x89, 0xcf, //0x00002827 movl         %ecx, %r15d
	0x48, 0x83, 0xf8, 0x01, //0x0000282a cmpq         $1, %rax
	0x0f, 0x8f, 0xe0, 0xff, 0xff, 0xff, //0x0000282e jg           LBB0_559
	0xb8, 0x02, 0xfc, 0xff, 0xff, //0x00002834 movl         $-1022, %eax
	0x48, 0x89, 0x45, 0xb8, //0x00002839 movq         %rax, $-72(%rbp)
	0x4c, 0x8b, 0x6d, 0xa8, //0x0000283d movq         $-88(%rbp), %r13
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002841 movq         $-80(%rbp), %r11
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002845 movabsq      $-9223372036854775808, %rdi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x0000284f movabsq      $4503599627370495, %rax
	0x48, 0x8b, 0x75, 0x90, //0x00002859 movq         $-112(%rbp), %rsi
	0x8a, 0x55, 0xd7, //0x0000285d movb         $-41(%rbp), %dl
	0x31, 0xc9, //0x00002860 xorl         %ecx, %ecx
	0xe9, 0x89, 0x06, 0x00, 0x00, //0x00002862 jmp          LBB0_638
	//0x00002867 LBB0_562
	0x48, 0x01, 0xc0, //0x00002867 addq         %rax, %rax
	0x48, 0x8d, 0x04, 0x80, //0x0000286a leaq         (%rax,%rax,4), %rax
	0x83, 0xc6, 0x01, //0x0000286e addl         $1, %esi
	0x48, 0x89, 0xc2, //0x00002871 movq         %rax, %rdx
	0x48, 0xd3, 0xea, //0x00002874 shrq         %cl, %rdx
	0x48, 0x85, 0xd2, //0x00002877 testq        %rdx, %rdx
	0x0f, 0x84, 0xe7, 0xff, 0xff, 0xff, //0x0000287a je           LBB0_562
	0xe9, 0x7b, 0xfe, 0xff, 0xff, //0x00002880 jmp          LBB0_545
	//0x00002885 LBB0_563
	0x49, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002885 movq         $-1, %r9
	0x4c, 0x2b, 0x5d, 0xc8, //0x0000288c subq         $-56(%rbp), %r11
	0x4d, 0x89, 0x4d, 0x00, //0x00002890 movq         %r9, (%r13)
	0xe9, 0x6f, 0xea, 0xff, 0xff, //0x00002894 jmp          LBB0_263
	//0x00002899 LBB0_564
	0xb8, 0x02, 0xfc, 0xff, 0xff, //0x00002899 movl         $-1022, %eax
	0x48, 0x89, 0x45, 0xb8, //0x0000289e movq         %rax, $-72(%rbp)
	0x45, 0x85, 0xff, //0x000028a2 testl        %r15d, %r15d
	0x0f, 0x84, 0x98, 0x03, 0x00, 0x00, //0x000028a5 je           LBB0_615
	0x45, 0x89, 0xfe, //0x000028ab movl         %r15d, %r14d
	0xe9, 0x39, 0x00, 0x00, 0x00, //0x000028ae jmp          LBB0_570
	//0x000028b3 LBB0_566
	0x49, 0xc7, 0xc7, 0xff, 0xff, 0xff, 0xff, //0x000028b3 movq         $-1, %r15
	0x49, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x000028ba movq         $-1, %r10
	0x4d, 0x89, 0xd8, //0x000028c1 movq         %r11, %r8
	0x4c, 0x89, 0x6d, 0xa8, //0x000028c4 movq         %r13, $-88(%rbp)
	0x49, 0x83, 0xfe, 0x10, //0x000028c8 cmpq         $16, %r14
	0x0f, 0x83, 0xa3, 0xdd, 0xff, 0xff, //0x000028cc jae          LBB0_66
	0xe9, 0x19, 0xdf, 0xff, 0xff, //0x000028d2 jmp          LBB0_84
	//0x000028d7 LBB0_567
	0xb9, 0x02, 0xfc, 0xff, 0xff, //0x000028d7 movl         $-1022, %ecx
	0x45, 0x89, 0xfe, //0x000028dc movl         %r15d, %r14d
	//0x000028df LBB0_568
	0x45, 0x85, 0xf6, //0x000028df testl        %r14d, %r14d
	0x0f, 0x84, 0xb3, 0x00, 0x00, 0x00, //0x000028e2 je           LBB0_580
	0x48, 0x89, 0x4d, 0xb8, //0x000028e8 movq         %rcx, $-72(%rbp)
	//0x000028ec LBB0_570
	0x49, 0x63, 0xc6, //0x000028ec movslq       %r14d, %rax
	0x48, 0x8d, 0x78, 0xfe, //0x000028ef leaq         $-2(%rax), %rdi
	0x48, 0x8d, 0x50, 0xff, //0x000028f3 leaq         $-1(%rax), %rdx
	0x31, 0xf6, //0x000028f7 xorl         %esi, %esi
	//0x000028f9 LBB0_571
	0x48, 0x8d, 0x0d, 0xe0, 0x38, 0x00, 0x00, //0x000028f9 leaq         $14560(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x0e, 0x8c, 0x15, 0x00, 0x00, //0x00002900 movzbl       $5516(%rsi,%rcx), %ebx
	0x41, 0x0f, 0xb6, 0x0c, 0x30, //0x00002908 movzbl       (%r8,%rsi), %ecx
	0x38, 0xd9, //0x0000290d cmpb         %bl, %cl
	0x0f, 0x85, 0x91, 0x00, 0x00, 0x00, //0x0000290f jne          LBB0_581
	0x48, 0x39, 0xf2, //0x00002915 cmpq         %rsi, %rdx
	0x0f, 0x84, 0x5a, 0x00, 0x00, 0x00, //0x00002918 je           LBB0_578
	0x48, 0x8d, 0x0d, 0xbb, 0x38, 0x00, 0x00, //0x0000291e leaq         $14523(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x0e, 0x8d, 0x15, 0x00, 0x00, //0x00002925 movzbl       $5517(%rsi,%rcx), %ebx
	0x41, 0x0f, 0xb6, 0x4c, 0x30, 0x01, //0x0000292d movzbl       $1(%r8,%rsi), %ecx
	0x38, 0xd9, //0x00002933 cmpb         %bl, %cl
	0x0f, 0x85, 0x6b, 0x00, 0x00, 0x00, //0x00002935 jne          LBB0_581
	0x48, 0x39, 0xf7, //0x0000293b cmpq         %rsi, %rdi
	0x0f, 0x84, 0x34, 0x00, 0x00, 0x00, //0x0000293e je           LBB0_578
	0x48, 0x83, 0xfe, 0x24, //0x00002944 cmpq         $36, %rsi
	0x0f, 0x84, 0x42, 0x00, 0x00, 0x00, //0x00002948 je           LBB0_579
	0x48, 0x8d, 0x0d, 0x8b, 0x38, 0x00, 0x00, //0x0000294e leaq         $14475(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x0f, 0xb6, 0x9c, 0x0e, 0x8e, 0x15, 0x00, 0x00, //0x00002955 movzbl       $5518(%rsi,%rcx), %ebx
	0x41, 0x0f, 0xb6, 0x4c, 0x30, 0x02, //0x0000295d movzbl       $2(%r8,%rsi), %ecx
	0x38, 0xd9, //0x00002963 cmpb         %bl, %cl
	0x0f, 0x85, 0x3b, 0x00, 0x00, 0x00, //0x00002965 jne          LBB0_581
	0x48, 0x83, 0xc6, 0x03, //0x0000296b addq         $3, %rsi
	0x48, 0x39, 0xf0, //0x0000296f cmpq         %rsi, %rax
	0x0f, 0x85, 0x81, 0xff, 0xff, 0xff, //0x00002972 jne          LBB0_571
	//0x00002978 LBB0_578
	0x44, 0x89, 0xf0, //0x00002978 movl         %r14d, %eax
	0x48, 0x8d, 0x0d, 0x5e, 0x38, 0x00, 0x00, //0x0000297b leaq         $14430(%rip), %rcx  /* _LSHIFT_TAB+0(%rip) */
	0x80, 0xbc, 0x08, 0x8c, 0x15, 0x00, 0x00, 0x00, //0x00002982 cmpb         $0, $5516(%rax,%rcx)
	0x0f, 0x85, 0x24, 0x00, 0x00, 0x00, //0x0000298a jne          LBB0_582
	//0x00002990 LBB0_579
	0x41, 0xba, 0x10, 0x00, 0x00, 0x00, //0x00002990 movl         $16, %r10d
	0xe9, 0x1f, 0x00, 0x00, 0x00, //0x00002996 jmp          LBB0_583
	//0x0000299b LBB0_580
	0x31, 0xc0, //0x0000299b xorl         %eax, %eax
	0x4c, 0x8b, 0x5d, 0xc8, //0x0000299d movq         $-56(%rbp), %r11
	0xe9, 0xbc, 0x01, 0x00, 0x00, //0x000029a1 jmp          LBB0_604
	//0x000029a6 LBB0_581
	0x41, 0xba, 0x10, 0x00, 0x00, 0x00, //0x000029a6 movl         $16, %r10d
	0x38, 0xd9, //0x000029ac cmpb         %bl, %cl
	0x0f, 0x8d, 0x06, 0x00, 0x00, 0x00, //0x000029ae jge          LBB0_583
	//0x000029b4 LBB0_582
	0x41, 0xba, 0x0f, 0x00, 0x00, 0x00, //0x000029b4 movl         $15, %r10d
	//0x000029ba LBB0_583
	0x45, 0x85, 0xf6, //0x000029ba testl        %r14d, %r14d
	0x0f, 0x8e, 0xb2, 0x00, 0x00, 0x00, //0x000029bd jle          LBB0_591
	0x4c, 0x89, 0xd3, //0x000029c3 movq         %r10, %rbx
	0x45, 0x01, 0xf2, //0x000029c6 addl         %r14d, %r10d
	0x44, 0x89, 0xf7, //0x000029c9 movl         %r14d, %edi
	0x4d, 0x63, 0xca, //0x000029cc movslq       %r10d, %r9
	0x49, 0x83, 0xc1, 0xff, //0x000029cf addq         $-1, %r9
	0x48, 0x83, 0xc7, 0x01, //0x000029d3 addq         $1, %rdi
	0x41, 0x83, 0xc6, 0xff, //0x000029d7 addl         $-1, %r14d
	0x31, 0xc9, //0x000029db xorl         %ecx, %ecx
	0x49, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x000029dd movabsq      $-432345564227567616, %r11
	0x41, 0xbd, 0x01, 0x00, 0x00, 0x00, //0x000029e7 movl         $1, %r13d
	0xe9, 0x21, 0x00, 0x00, 0x00, //0x000029ed jmp          LBB0_587
	//0x000029f2 LBB0_585
	0x48, 0x85, 0xc0, //0x000029f2 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xe5, //0x000029f5 cmovnel      %r13d, %r12d
	//0x000029f9 LBB0_586
	0x41, 0x83, 0xc2, 0xff, //0x000029f9 addl         $-1, %r10d
	0x49, 0x83, 0xc1, 0xff, //0x000029fd addq         $-1, %r9
	0x48, 0x83, 0xc7, 0xff, //0x00002a01 addq         $-1, %rdi
	0x41, 0x83, 0xc6, 0xff, //0x00002a05 addl         $-1, %r14d
	0x48, 0x83, 0xff, 0x01, //0x00002a09 cmpq         $1, %rdi
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x00002a0d jle          LBB0_589
	//0x00002a13 LBB0_587
	0x44, 0x89, 0xf0, //0x00002a13 movl         %r14d, %eax
	0x49, 0x0f, 0xbe, 0x34, 0x00, //0x00002a16 movsbq       (%r8,%rax), %rsi
	0x48, 0xc1, 0xe6, 0x35, //0x00002a1b shlq         $53, %rsi
	0x48, 0x01, 0xce, //0x00002a1f addq         %rcx, %rsi
	0x4c, 0x01, 0xde, //0x00002a22 addq         %r11, %rsi
	0x48, 0x89, 0xf0, //0x00002a25 movq         %rsi, %rax
	0x48, 0xb9, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002a28 movabsq      $-3689348814741910323, %rcx
	0x48, 0xf7, 0xe1, //0x00002a32 mulq         %rcx
	0x48, 0x89, 0xd1, //0x00002a35 movq         %rdx, %rcx
	0x48, 0xc1, 0xe9, 0x03, //0x00002a38 shrq         $3, %rcx
	0x48, 0x8d, 0x04, 0x09, //0x00002a3c leaq         (%rcx,%rcx), %rax
	0x48, 0x8d, 0x14, 0x80, //0x00002a40 leaq         (%rax,%rax,4), %rdx
	0x48, 0x89, 0xf0, //0x00002a44 movq         %rsi, %rax
	0x48, 0x29, 0xd0, //0x00002a47 subq         %rdx, %rax
	0x4c, 0x3b, 0x4d, 0xc0, //0x00002a4a cmpq         $-64(%rbp), %r9
	0x0f, 0x83, 0x9e, 0xff, 0xff, 0xff, //0x00002a4e jae          LBB0_585
	0x04, 0x30, //0x00002a54 addb         $48, %al
	0x43, 0x88, 0x04, 0x08, //0x00002a56 movb         %al, (%r8,%r9)
	0xe9, 0x9a, 0xff, 0xff, 0xff, //0x00002a5a jmp          LBB0_586
	//0x00002a5f LBB0_589
	0x48, 0x83, 0xfe, 0x0a, //0x00002a5f cmpq         $10, %rsi
	0x0f, 0x83, 0x15, 0x00, 0x00, 0x00, //0x00002a63 jae          LBB0_592
	0x48, 0x8b, 0x7d, 0xc0, //0x00002a69 movq         $-64(%rbp), %rdi
	0x49, 0x89, 0xda, //0x00002a6d movq         %rbx, %r10
	0xe9, 0x7c, 0x00, 0x00, 0x00, //0x00002a70 jmp          LBB0_596
	//0x00002a75 LBB0_591
	0x48, 0x8b, 0x7d, 0xc0, //0x00002a75 movq         $-64(%rbp), %rdi
	0xe9, 0x73, 0x00, 0x00, 0x00, //0x00002a79 jmp          LBB0_596
	//0x00002a7e LBB0_592
	0x49, 0x63, 0xf2, //0x00002a7e movslq       %r10d, %rsi
	0x48, 0x83, 0xc6, 0xff, //0x00002a81 addq         $-1, %rsi
	0x41, 0xb9, 0x01, 0x00, 0x00, 0x00, //0x00002a85 movl         $1, %r9d
	0x48, 0x8b, 0x7d, 0xc0, //0x00002a8b movq         $-64(%rbp), %rdi
	0x49, 0x89, 0xda, //0x00002a8f movq         %rbx, %r10
	0xe9, 0x18, 0x00, 0x00, 0x00, //0x00002a92 jmp          LBB0_594
	//0x00002a97 LBB0_593
	0x48, 0x85, 0xc0, //0x00002a97 testq        %rax, %rax
	0x45, 0x0f, 0x45, 0xe1, //0x00002a9a cmovnel      %r9d, %r12d
	0x48, 0x83, 0xc6, 0xff, //0x00002a9e addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002aa2 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002aa6 movq         %rdx, %rcx
	0x0f, 0x86, 0x42, 0x00, 0x00, 0x00, //0x00002aa9 jbe          LBB0_596
	//0x00002aaf LBB0_594
	0x48, 0x89, 0xc8, //0x00002aaf movq         %rcx, %rax
	0x48, 0xba, 0xcd, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00002ab2 movabsq      $-3689348814741910323, %rdx
	0x48, 0xf7, 0xe2, //0x00002abc mulq         %rdx
	0x48, 0xc1, 0xea, 0x03, //0x00002abf shrq         $3, %rdx
	0x48, 0x8d, 0x04, 0x12, //0x00002ac3 leaq         (%rdx,%rdx), %rax
	0x48, 0x8d, 0x1c, 0x80, //0x00002ac7 leaq         (%rax,%rax,4), %rbx
	0x48, 0x89, 0xc8, //0x00002acb movq         %rcx, %rax
	0x48, 0x29, 0xd8, //0x00002ace subq         %rbx, %rax
	0x48, 0x39, 0xfe, //0x00002ad1 cmpq         %rdi, %rsi
	0x0f, 0x83, 0xbd, 0xff, 0xff, 0xff, //0x00002ad4 jae          LBB0_593
	0x04, 0x30, //0x00002ada addb         $48, %al
	0x41, 0x88, 0x04, 0x30, //0x00002adc movb         %al, (%r8,%rsi)
	0x48, 0x83, 0xc6, 0xff, //0x00002ae0 addq         $-1, %rsi
	0x48, 0x83, 0xf9, 0x09, //0x00002ae4 cmpq         $9, %rcx
	0x48, 0x89, 0xd1, //0x00002ae8 movq         %rdx, %rcx
	0x0f, 0x87, 0xbe, 0xff, 0xff, 0xff, //0x00002aeb ja           LBB0_594
	//0x00002af1 LBB0_596
	0x45, 0x01, 0xd7, //0x00002af1 addl         %r10d, %r15d
	0x49, 0x63, 0xc7, //0x00002af4 movslq       %r15d, %rax
	0x48, 0x39, 0xc7, //0x00002af7 cmpq         %rax, %rdi
	0x0f, 0x46, 0xc7, //0x00002afa cmovbel      %edi, %eax
	0x48, 0x8b, 0x4d, 0xc8, //0x00002afd movq         $-56(%rbp), %rcx
	0x44, 0x01, 0xd1, //0x00002b01 addl         %r10d, %ecx
	0x48, 0x89, 0x4d, 0xc8, //0x00002b04 movq         %rcx, $-56(%rbp)
	0x85, 0xc0, //0x00002b08 testl        %eax, %eax
	0x0f, 0x8e, 0x29, 0x00, 0x00, 0x00, //0x00002b0a jle          LBB0_600
	0x89, 0xc1, //0x00002b10 movl         %eax, %ecx
	0x48, 0x83, 0xc1, 0x01, //0x00002b12 addq         $1, %rcx
	//0x00002b16 LBB0_598
	0x8d, 0x50, 0xff, //0x00002b16 leal         $-1(%rax), %edx
	0x41, 0x80, 0x3c, 0x10, 0x30, //0x00002b19 cmpb         $48, (%r8,%rdx)
	0x0f, 0x85, 0x1d, 0x00, 0x00, 0x00, //0x00002b1e jne          LBB0_601
	0x48, 0x83, 0xc1, 0xff, //0x00002b24 addq         $-1, %rcx
	0x89, 0xd0, //0x00002b28 movl         %edx, %eax
	0x48, 0x83, 0xf9, 0x01, //0x00002b2a cmpq         $1, %rcx
	0x0f, 0x8f, 0xe2, 0xff, 0xff, 0xff, //0x00002b2e jg           LBB0_598
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00002b34 jmp          LBB0_602
	//0x00002b39 LBB0_600
	0x85, 0xc0, //0x00002b39 testl        %eax, %eax
	0x0f, 0x84, 0x0d, 0x00, 0x00, 0x00, //0x00002b3b je           LBB0_602
	//0x00002b41 LBB0_601
	0x4c, 0x8b, 0x5d, 0xc8, //0x00002b41 movq         $-56(%rbp), %r11
	0x48, 0x8b, 0x4d, 0xb8, //0x00002b45 movq         $-72(%rbp), %rcx
	0xe9, 0x14, 0x00, 0x00, 0x00, //0x00002b49 jmp          LBB0_604
	//0x00002b4e LBB0_602
	0x31, 0xd2, //0x00002b4e xorl         %edx, %edx
	0x31, 0xf6, //0x00002b50 xorl         %esi, %esi
	0xe9, 0x1d, 0x03, 0x00, 0x00, //0x00002b52 jmp          LBB0_632
	//0x00002b57 LBB0_603
	0x31, 0xc0, //0x00002b57 xorl         %eax, %eax
	0x4c, 0x8b, 0x5d, 0xc8, //0x00002b59 movq         $-56(%rbp), %r11
	0xb9, 0x02, 0xfc, 0xff, 0xff, //0x00002b5d movl         $-1022, %ecx
	//0x00002b62 LBB0_604
	0x48, 0x89, 0x4d, 0xb8, //0x00002b62 movq         %rcx, $-72(%rbp)
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x00002b66 movq         $-1, %rcx
	0x41, 0x83, 0xfb, 0x14, //0x00002b6d cmpl         $20, %r11d
	0x0f, 0x8f, 0x56, 0x03, 0x00, 0x00, //0x00002b71 jg           LBB0_636
	0x44, 0x89, 0xd9, //0x00002b77 movl         %r11d, %ecx
	0x4d, 0x89, 0xde, //0x00002b7a movq         %r11, %r14
	0x45, 0x85, 0xdb, //0x00002b7d testl        %r11d, %r11d
	0x0f, 0x8e, 0x4c, 0x00, 0x00, 0x00, //0x00002b80 jle          LBB0_610
	0x31, 0xff, //0x00002b86 xorl         %edi, %edi
	0x85, 0xc0, //0x00002b88 testl        %eax, %eax
	0xbb, 0x00, 0x00, 0x00, 0x00, //0x00002b8a movl         $0, %ebx
	0x0f, 0x4f, 0xd8, //0x00002b8f cmovgl       %eax, %ebx
	0x4c, 0x8d, 0x59, 0xff, //0x00002b92 leaq         $-1(%rcx), %r11
	0x49, 0x39, 0xdb, //0x00002b96 cmpq         %rbx, %r11
	0x4c, 0x0f, 0x43, 0xdb, //0x00002b99 cmovaeq      %rbx, %r11
	0x45, 0x8d, 0x4b, 0x01, //0x00002b9d leal         $1(%r11), %r9d
	0x31, 0xd2, //0x00002ba1 xorl         %edx, %edx
	//0x00002ba3 LBB0_607
	0x48, 0x39, 0xfb, //0x00002ba3 cmpq         %rdi, %rbx
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x00002ba6 je           LBB0_611
	0x48, 0x8d, 0x14, 0x92, //0x00002bac leaq         (%rdx,%rdx,4), %rdx
	0x49, 0x0f, 0xbe, 0x34, 0x38, //0x00002bb0 movsbq       (%r8,%rdi), %rsi
	0x48, 0x8d, 0x14, 0x56, //0x00002bb5 leaq         (%rsi,%rdx,2), %rdx
	0x48, 0x83, 0xc2, 0xd0, //0x00002bb9 addq         $-48, %rdx
	0x48, 0x83, 0xc7, 0x01, //0x00002bbd addq         $1, %rdi
	0x48, 0x39, 0xf9, //0x00002bc1 cmpq         %rdi, %rcx
	0x0f, 0x85, 0xd9, 0xff, 0xff, 0xff, //0x00002bc4 jne          LBB0_607
	0x45, 0x89, 0xcb, //0x00002bca movl         %r9d, %r11d
	0xe9, 0x05, 0x00, 0x00, 0x00, //0x00002bcd jmp          LBB0_611
	//0x00002bd2 LBB0_610
	0x45, 0x31, 0xdb, //0x00002bd2 xorl         %r11d, %r11d
	0x31, 0xd2, //0x00002bd5 xorl         %edx, %edx
	//0x00002bd7 LBB0_611
	0x4c, 0x89, 0xf6, //0x00002bd7 movq         %r14, %rsi
	0x85, 0xf6, //0x00002bda testl        %esi, %esi
	0x41, 0x0f, 0x9e, 0xc2, //0x00002bdc setle        %r10b
	0x89, 0xf7, //0x00002be0 movl         %esi, %edi
	0x44, 0x29, 0xdf, //0x00002be2 subl         %r11d, %edi
	0x0f, 0x8e, 0x33, 0x02, 0x00, 0x00, //0x00002be5 jle          LBB0_625
	0x83, 0xff, 0x10, //0x00002beb cmpl         $16, %edi
	0x0f, 0x82, 0x14, 0x02, 0x00, 0x00, //0x00002bee jb           LBB0_623
	0x41, 0x89, 0xf9, //0x00002bf4 movl         %edi, %r9d
	0xc5, 0xfa, 0x6f, 0x05, 0x61, 0xd5, 0xff, 0xff, //0x00002bf7 vmovdqu      $-10911(%rip), %xmm0  /* LCPI0_22+0(%rip) */
	0xc4, 0xe3, 0xf9, 0x22, 0xc2, 0x00, //0x00002bff vpinsrq      $0, %rdx, %xmm0, %xmm0
	0x41, 0x83, 0xe1, 0xf0, //0x00002c05 andl         $-16, %r9d
	0xc4, 0xe3, 0x7d, 0x02, 0x05, 0x4d, 0xd5, 0xff, 0xff, 0xf0, //0x00002c09 vpblendd     $240, $-10931(%rip), %ymm0, %ymm0  /* LCPI0_22+0(%rip) */
	0x41, 0x8d, 0x71, 0xf0, //0x00002c13 leal         $-16(%r9), %esi
	0x89, 0xf2, //0x00002c17 movl         %esi, %edx
	0xc1, 0xea, 0x04, //0x00002c19 shrl         $4, %edx
	0x83, 0xc2, 0x01, //0x00002c1c addl         $1, %edx
	0x89, 0xd3, //0x00002c1f movl         %edx, %ebx
	0x83, 0xe3, 0x03, //0x00002c21 andl         $3, %ebx
	0x83, 0xfe, 0x30, //0x00002c24 cmpl         $48, %esi
	0x0f, 0x83, 0x1d, 0x00, 0x00, 0x00, //0x00002c27 jae          LBB0_616
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0xea, 0xd5, 0xff, 0xff, //0x00002c2d vpbroadcastq $-10774(%rip), %ymm2  /* LCPI0_23+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x00002c36 vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x00002c3a vmovdqa      %ymm2, %ymm1
	0xe9, 0x87, 0x00, 0x00, 0x00, //0x00002c3e jmp          LBB0_618
	//0x00002c43 LBB0_615
	0x31, 0xc9, //0x00002c43 xorl         %ecx, %ecx
	0xe9, 0x83, 0x02, 0x00, 0x00, //0x00002c45 jmp          LBB0_636
	//0x00002c4a LBB0_616
	0x83, 0xe2, 0xfc, //0x00002c4a andl         $-4, %edx
	0xf7, 0xda, //0x00002c4d negl         %edx
	0xc4, 0xe2, 0x7d, 0x59, 0x15, 0xc8, 0xd5, 0xff, 0xff, //0x00002c4f vpbroadcastq $-10808(%rip), %ymm2  /* LCPI0_23+0(%rip) */
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0xc7, 0xd5, 0xff, 0xff, //0x00002c58 vpbroadcastq $-10809(%rip), %ymm4  /* LCPI0_24+0(%rip) */
	0xc5, 0xfd, 0x6f, 0xda, //0x00002c61 vmovdqa      %ymm2, %ymm3
	0xc5, 0xfd, 0x6f, 0xca, //0x00002c65 vmovdqa      %ymm2, %ymm1
	//0x00002c69 LBB0_617
	0xc5, 0xfd, 0xf4, 0xec, //0x00002c69 vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00002c6d vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x00002c72 vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x00002c76 vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x00002c7b vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x00002c7f vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x00002c83 vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x00002c88 vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002c8c vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x00002c91 vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x00002c95 vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x00002c99 vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x00002c9e vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x00002ca2 vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x00002ca7 vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x00002cab vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x00002caf vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x00002cb4 vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x00002cb8 vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x00002cbd vpaddq       %ymm1, %ymm5, %ymm1
	0x83, 0xc2, 0x04, //0x00002cc1 addl         $4, %edx
	0x0f, 0x85, 0x9f, 0xff, 0xff, 0xff, //0x00002cc4 jne          LBB0_617
	//0x00002cca LBB0_618
	0x85, 0xdb, //0x00002cca testl        %ebx, %ebx
	0x0f, 0x84, 0x6b, 0x00, 0x00, 0x00, //0x00002ccc je           LBB0_621
	0xf7, 0xdb, //0x00002cd2 negl         %ebx
	0xc4, 0xe2, 0x7d, 0x59, 0x25, 0x53, 0xd5, 0xff, 0xff, //0x00002cd4 vpbroadcastq $-10925(%rip), %ymm4  /* LCPI0_25+0(%rip) */
	//0x00002cdd LBB0_620
	0xc5, 0xfd, 0xf4, 0xec, //0x00002cdd vpmuludq     %ymm4, %ymm0, %ymm5
	0xc5, 0xfd, 0x73, 0xd0, 0x20, //0x00002ce1 vpsrlq       $32, %ymm0, %ymm0
	0xc5, 0xfd, 0xf4, 0xc4, //0x00002ce6 vpmuludq     %ymm4, %ymm0, %ymm0
	0xc5, 0xfd, 0x73, 0xf0, 0x20, //0x00002cea vpsllq       $32, %ymm0, %ymm0
	0xc5, 0xd5, 0xd4, 0xc0, //0x00002cef vpaddq       %ymm0, %ymm5, %ymm0
	0xc5, 0xed, 0xf4, 0xec, //0x00002cf3 vpmuludq     %ymm4, %ymm2, %ymm5
	0xc5, 0xed, 0x73, 0xd2, 0x20, //0x00002cf7 vpsrlq       $32, %ymm2, %ymm2
	0xc5, 0xed, 0xf4, 0xd4, //0x00002cfc vpmuludq     %ymm4, %ymm2, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002d00 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xd5, 0xd4, 0xd2, //0x00002d05 vpaddq       %ymm2, %ymm5, %ymm2
	0xc5, 0xe5, 0xf4, 0xec, //0x00002d09 vpmuludq     %ymm4, %ymm3, %ymm5
	0xc5, 0xe5, 0x73, 0xd3, 0x20, //0x00002d0d vpsrlq       $32, %ymm3, %ymm3
	0xc5, 0xe5, 0xf4, 0xdc, //0x00002d12 vpmuludq     %ymm4, %ymm3, %ymm3
	0xc5, 0xe5, 0x73, 0xf3, 0x20, //0x00002d16 vpsllq       $32, %ymm3, %ymm3
	0xc5, 0xd5, 0xd4, 0xdb, //0x00002d1b vpaddq       %ymm3, %ymm5, %ymm3
	0xc5, 0xf5, 0xf4, 0xec, //0x00002d1f vpmuludq     %ymm4, %ymm1, %ymm5
	0xc5, 0xf5, 0x73, 0xd1, 0x20, //0x00002d23 vpsrlq       $32, %ymm1, %ymm1
	0xc5, 0xf5, 0xf4, 0xcc, //0x00002d28 vpmuludq     %ymm4, %ymm1, %ymm1
	0xc5, 0xf5, 0x73, 0xf1, 0x20, //0x00002d2c vpsllq       $32, %ymm1, %ymm1
	0xc5, 0xd5, 0xd4, 0xc9, //0x00002d31 vpaddq       %ymm1, %ymm5, %ymm1
	0xff, 0xc3, //0x00002d35 incl         %ebx
	0x0f, 0x85, 0xa0, 0xff, 0xff, 0xff, //0x00002d37 jne          LBB0_620
	//0x00002d3d LBB0_621
	0xc5, 0xdd, 0x73, 0xd2, 0x20, //0x00002d3d vpsrlq       $32, %ymm2, %ymm4
	0xc5, 0xdd, 0xf4, 0xe0, //0x00002d42 vpmuludq     %ymm0, %ymm4, %ymm4
	0xc5, 0xd5, 0x73, 0xd0, 0x20, //0x00002d46 vpsrlq       $32, %ymm0, %ymm5
	0xc5, 0xed, 0xf4, 0xed, //0x00002d4b vpmuludq     %ymm5, %ymm2, %ymm5
	0xc5, 0xd5, 0xd4, 0xe4, //0x00002d4f vpaddq       %ymm4, %ymm5, %ymm4
	0xc5, 0xdd, 0x73, 0xf4, 0x20, //0x00002d53 vpsllq       $32, %ymm4, %ymm4
	0xc5, 0xed, 0xf4, 0xc0, //0x00002d58 vpmuludq     %ymm0, %ymm2, %ymm0
	0xc5, 0xfd, 0xd4, 0xc4, //0x00002d5c vpaddq       %ymm4, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd3, 0x20, //0x00002d60 vpsrlq       $32, %ymm3, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x00002d65 vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xdd, 0x73, 0xd0, 0x20, //0x00002d69 vpsrlq       $32, %ymm0, %ymm4
	0xc5, 0xe5, 0xf4, 0xe4, //0x00002d6e vpmuludq     %ymm4, %ymm3, %ymm4
	0xc5, 0xdd, 0xd4, 0xd2, //0x00002d72 vpaddq       %ymm2, %ymm4, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002d76 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xe5, 0xf4, 0xc0, //0x00002d7b vpmuludq     %ymm0, %ymm3, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x00002d7f vpaddq       %ymm2, %ymm0, %ymm0
	0xc5, 0xed, 0x73, 0xd1, 0x20, //0x00002d83 vpsrlq       $32, %ymm1, %ymm2
	0xc5, 0xed, 0xf4, 0xd0, //0x00002d88 vpmuludq     %ymm0, %ymm2, %ymm2
	0xc5, 0xe5, 0x73, 0xd0, 0x20, //0x00002d8c vpsrlq       $32, %ymm0, %ymm3
	0xc5, 0xf5, 0xf4, 0xdb, //0x00002d91 vpmuludq     %ymm3, %ymm1, %ymm3
	0xc5, 0xe5, 0xd4, 0xd2, //0x00002d95 vpaddq       %ymm2, %ymm3, %ymm2
	0xc5, 0xed, 0x73, 0xf2, 0x20, //0x00002d99 vpsllq       $32, %ymm2, %ymm2
	0xc5, 0xf5, 0xf4, 0xc0, //0x00002d9e vpmuludq     %ymm0, %ymm1, %ymm0
	0xc5, 0xfd, 0xd4, 0xc2, //0x00002da2 vpaddq       %ymm2, %ymm0, %ymm0
	0xc4, 0xe3, 0x7d, 0x39, 0xc1, 0x01, //0x00002da6 vextracti128 $1, %ymm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x00002dac vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x00002db1 vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xe1, 0x73, 0xd1, 0x20, //0x00002db5 vpsrlq       $32, %xmm1, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x00002dba vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x00002dbe vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x00002dc2 vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x00002dc7 vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x00002dcb vpaddq       %xmm2, %xmm0, %xmm0
	0xc5, 0xf9, 0x70, 0xc8, 0xee, //0x00002dcf vpshufd      $238, %xmm0, %xmm1
	0xc5, 0xe9, 0x73, 0xd0, 0x20, //0x00002dd4 vpsrlq       $32, %xmm0, %xmm2
	0xc5, 0xe9, 0xf4, 0xd1, //0x00002dd9 vpmuludq     %xmm1, %xmm2, %xmm2
	0xc5, 0xf9, 0x70, 0xd8, 0xff, //0x00002ddd vpshufd      $255, %xmm0, %xmm3
	0xc5, 0xf9, 0xf4, 0xdb, //0x00002de2 vpmuludq     %xmm3, %xmm0, %xmm3
	0xc5, 0xe1, 0xd4, 0xd2, //0x00002de6 vpaddq       %xmm2, %xmm3, %xmm2
	0xc5, 0xe9, 0x73, 0xf2, 0x20, //0x00002dea vpsllq       $32, %xmm2, %xmm2
	0xc5, 0xf9, 0xf4, 0xc1, //0x00002def vpmuludq     %xmm1, %xmm0, %xmm0
	0xc5, 0xf9, 0xd4, 0xc2, //0x00002df3 vpaddq       %xmm2, %xmm0, %xmm0
	0xc4, 0xe1, 0xf9, 0x7e, 0xc2, //0x00002df7 vmovq        %xmm0, %rdx
	0x44, 0x39, 0xcf, //0x00002dfc cmpl         %r9d, %edi
	0x0f, 0x84, 0x19, 0x00, 0x00, 0x00, //0x00002dff je           LBB0_625
	0x45, 0x01, 0xcb, //0x00002e05 addl         %r9d, %r11d
	//0x00002e08 LBB0_623
	0x44, 0x89, 0xf6, //0x00002e08 movl         %r14d, %esi
	0x44, 0x29, 0xde, //0x00002e0b subl         %r11d, %esi
	//0x00002e0e LBB0_624
	0x48, 0x01, 0xd2, //0x00002e0e addq         %rdx, %rdx
	0x48, 0x8d, 0x14, 0x92, //0x00002e11 leaq         (%rdx,%rdx,4), %rdx
	0x83, 0xc6, 0xff, //0x00002e15 addl         $-1, %esi
	0x0f, 0x85, 0xf0, 0xff, 0xff, 0xff, //0x00002e18 jne          LBB0_624
	//0x00002e1e LBB0_625
	0x31, 0xf6, //0x00002e1e xorl         %esi, %esi
	0x4c, 0x89, 0xf7, //0x00002e20 movq         %r14, %rdi
	0x85, 0xff, //0x00002e23 testl        %edi, %edi
	0x0f, 0x88, 0x49, 0x00, 0x00, 0x00, //0x00002e25 js           LBB0_632
	0x39, 0xf8, //0x00002e2b cmpl         %edi, %eax
	0x0f, 0x8e, 0x41, 0x00, 0x00, 0x00, //0x00002e2d jle          LBB0_632
	0x41, 0x8a, 0x0c, 0x08, //0x00002e33 movb         (%r8,%rcx), %cl
	0x80, 0xf9, 0x35, //0x00002e37 cmpb         $53, %cl
	0x0f, 0x85, 0x2d, 0x00, 0x00, 0x00, //0x00002e3a jne          LBB0_631
	0x41, 0x8d, 0x76, 0x01, //0x00002e40 leal         $1(%r14), %esi
	0x39, 0xc6, //0x00002e44 cmpl         %eax, %esi
	0x0f, 0x85, 0x21, 0x00, 0x00, 0x00, //0x00002e46 jne          LBB0_631
	0x45, 0x85, 0xe4, //0x00002e4c testl        %r12d, %r12d
	0x40, 0x0f, 0x95, 0xc6, //0x00002e4f setne        %sil
	0x41, 0x08, 0xf2, //0x00002e53 orb          %sil, %r10b
	0x0f, 0x85, 0x18, 0x00, 0x00, 0x00, //0x00002e56 jne          LBB0_632
	0x41, 0x83, 0xc6, 0xff, //0x00002e5c addl         $-1, %r14d
	0x43, 0x8a, 0x34, 0x30, //0x00002e60 movb         (%r8,%r14), %sil
	0x40, 0x80, 0xe6, 0x01, //0x00002e64 andb         $1, %sil
	0xe9, 0x07, 0x00, 0x00, 0x00, //0x00002e68 jmp          LBB0_632
	//0x00002e6d LBB0_631
	0x80, 0xf9, 0x35, //0x00002e6d cmpb         $53, %cl
	0x40, 0x0f, 0x9d, 0xc6, //0x00002e70 setge        %sil
	//0x00002e74 LBB0_632
	0x40, 0x0f, 0xb6, 0xce, //0x00002e74 movzbl       %sil, %ecx
	0x48, 0x01, 0xd1, //0x00002e78 addq         %rdx, %rcx
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, //0x00002e7b movabsq      $9007199254740992, %rax
	0x48, 0x39, 0xc1, //0x00002e85 cmpq         %rax, %rcx
	0x0f, 0x85, 0x3f, 0x00, 0x00, 0x00, //0x00002e88 jne          LBB0_636
	0x48, 0x8b, 0x45, 0xb8, //0x00002e8e movq         $-72(%rbp), %rax
	0x3d, 0xfe, 0x03, 0x00, 0x00, //0x00002e92 cmpl         $1022, %eax
	0x0f, 0x8e, 0xbb, 0x00, 0x00, 0x00, //0x00002e97 jle          LBB0_643
	0x31, 0xc9, //0x00002e9d xorl         %ecx, %ecx
	0x48, 0xbb, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002e9f movabsq      $9218868437227405312, %rbx
	0x4c, 0x8b, 0x6d, 0xa8, //0x00002ea9 movq         $-88(%rbp), %r13
	//0x00002ead LBB0_635
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002ead movq         $-80(%rbp), %r11
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002eb1 movabsq      $-9223372036854775808, %rdi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002ebb movabsq      $4503599627370495, %rax
	0x8a, 0x55, 0xd7, //0x00002ec5 movb         $-41(%rbp), %dl
	0xe9, 0x41, 0x00, 0x00, 0x00, //0x00002ec8 jmp          LBB0_639
	//0x00002ecd LBB0_636
	0x4c, 0x8b, 0x6d, 0xa8, //0x00002ecd movq         $-88(%rbp), %r13
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002ed1 movq         $-80(%rbp), %r11
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002ed5 movabsq      $-9223372036854775808, %rdi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002edf movabsq      $4503599627370495, %rax
	0x48, 0x8b, 0x75, 0x90, //0x00002ee9 movq         $-112(%rbp), %rsi
	//0x00002eed LBB0_637
	0x8a, 0x55, 0xd7, //0x00002eed movb         $-41(%rbp), %dl
	//0x00002ef0 LBB0_638
	0x48, 0x21, 0xce, //0x00002ef0 andq         %rcx, %rsi
	0x48, 0x8b, 0x5d, 0xb8, //0x00002ef3 movq         $-72(%rbp), %rbx
	0x81, 0xc3, 0xff, 0x03, 0x00, 0x00, //0x00002ef7 addl         $1023, %ebx
	0x81, 0xe3, 0xff, 0x07, 0x00, 0x00, //0x00002efd andl         $2047, %ebx
	0x48, 0xc1, 0xe3, 0x34, //0x00002f03 shlq         $52, %rbx
	0x48, 0x85, 0xf6, //0x00002f07 testq        %rsi, %rsi
	0x48, 0x0f, 0x44, 0xde, //0x00002f0a cmoveq       %rsi, %rbx
	//0x00002f0e LBB0_639
	0x48, 0x21, 0xc1, //0x00002f0e andq         %rax, %rcx
	0x48, 0x09, 0xd9, //0x00002f11 orq          %rbx, %rcx
	0x48, 0x89, 0xc8, //0x00002f14 movq         %rcx, %rax
	0x48, 0x09, 0xf8, //0x00002f17 orq          %rdi, %rax
	0x80, 0xfa, 0x2d, //0x00002f1a cmpb         $45, %dl
	0x48, 0x0f, 0x45, 0xc1, //0x00002f1d cmovneq      %rcx, %rax
	0xc4, 0xe1, 0xf9, 0x6e, 0xc0, //0x00002f21 vmovq        %rax, %xmm0
	//0x00002f26 LBB0_640
	0xc4, 0xe1, 0xf9, 0x7e, 0xc0, //0x00002f26 vmovq        %xmm0, %rax
	0x48, 0x83, 0xc7, 0xff, //0x00002f2b addq         $-1, %rdi
	0x48, 0x21, 0xc7, //0x00002f2f andq         %rax, %rdi
	0x48, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x7f, //0x00002f32 movabsq      $9218868437227405312, %rax
	0x48, 0x39, 0xc7, //0x00002f3c cmpq         %rax, %rdi
	0x0f, 0x85, 0x08, 0x00, 0x00, 0x00, //0x00002f3f jne          LBB0_642
	0x49, 0xc7, 0x45, 0x00, 0xf8, 0xff, 0xff, 0xff, //0x00002f45 movq         $-8, (%r13)
	//0x00002f4d LBB0_642
	0xc4, 0xc1, 0x7b, 0x11, 0x45, 0x08, //0x00002f4d vmovsd       %xmm0, $8(%r13)
	0xe9, 0xb0, 0xe3, 0xff, 0xff, //0x00002f53 jmp          LBB0_263
	//0x00002f58 LBB0_643
	0x83, 0xc0, 0x01, //0x00002f58 addl         $1, %eax
	0x48, 0x89, 0x45, 0xb8, //0x00002f5b movq         %rax, $-72(%rbp)
	0x48, 0x8b, 0x75, 0x90, //0x00002f5f movq         $-112(%rbp), %rsi
	0x48, 0x89, 0xf1, //0x00002f63 movq         %rsi, %rcx
	0x4c, 0x8b, 0x6d, 0xa8, //0x00002f66 movq         $-88(%rbp), %r13
	0x4c, 0x8b, 0x5d, 0xb0, //0x00002f6a movq         $-80(%rbp), %r11
	0x48, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00002f6e movabsq      $-9223372036854775808, %rdi
	0x48, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x00, //0x00002f78 movabsq      $4503599627370495, %rax
	0xe9, 0x66, 0xff, 0xff, 0xff, //0x00002f82 jmp          LBB0_637
	//0x00002f87 LBB0_645
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x00002f87 movq         $-1, %r11
	0xe9, 0xa4, 0x02, 0x00, 0x00, //0x00002f8e jmp          LBB0_688
	//0x00002f93 LBB0_646
	0x48, 0x8b, 0x75, 0xc8, //0x00002f93 movq         $-56(%rbp), %rsi
	0x4c, 0x8d, 0x0c, 0x16, //0x00002f97 leaq         (%rsi,%rdx), %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002f9b movq         $-1, %r8
	0x45, 0x31, 0xe4, //0x00002fa2 xorl         %r12d, %r12d
	0x49, 0x83, 0xff, 0x20, //0x00002fa5 cmpq         $32, %r15
	0x0f, 0x83, 0xa6, 0xe8, 0xff, 0xff, //0x00002fa9 jae          LBB0_323
	//0x00002faf LBB0_647
	0x4c, 0x89, 0xef, //0x00002faf movq         %r13, %rdi
	0xe9, 0x80, 0x00, 0x00, 0x00, //0x00002fb2 jmp          LBB0_654
	//0x00002fb7 LBB0_648
	0x48, 0x8b, 0x45, 0xc8, //0x00002fb7 movq         $-56(%rbp), %rax
	0x4c, 0x8d, 0x0c, 0x10, //0x00002fbb leaq         (%rax,%rdx), %r9
	0x49, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00002fbf movq         $-1, %r8
	0x45, 0x31, 0xed, //0x00002fc6 xorl         %r13d, %r13d
	0x49, 0x83, 0xff, 0x20, //0x00002fc9 cmpq         $32, %r15
	0x0f, 0x83, 0xde, 0xe8, 0xff, 0xff, //0x00002fcd jae          LBB0_328
	0xe9, 0xd5, 0x01, 0x00, 0x00, //0x00002fd3 jmp          LBB0_676
	//0x00002fd8 LBB0_650
	0x49, 0x83, 0xf8, 0xff, //0x00002fd8 cmpq         $-1, %r8
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x00002fdc jne          LBB0_652
	0x4c, 0x89, 0xce, //0x00002fe2 movq         %r9, %rsi
	0x48, 0x2b, 0x75, 0xc8, //0x00002fe5 subq         $-56(%rbp), %rsi
	0x4c, 0x0f, 0xbc, 0xc1, //0x00002fe9 bsfq         %rcx, %r8
	0x49, 0x01, 0xf0, //0x00002fed addq         %rsi, %r8
	//0x00002ff0 LBB0_652
	0x44, 0x89, 0xe6, //0x00002ff0 movl         %r12d, %esi
	0xf7, 0xd6, //0x00002ff3 notl         %esi
	0x21, 0xce, //0x00002ff5 andl         %ecx, %esi
	0x8d, 0x3c, 0x36, //0x00002ff7 leal         (%rsi,%rsi), %edi
	0x41, 0x8d, 0x1c, 0x74, //0x00002ffa leal         (%r12,%rsi,2), %ebx
	0xf7, 0xd7, //0x00002ffe notl         %edi
	0x21, 0xcf, //0x00003000 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003002 andl         $-1431655766, %edi
	0x45, 0x31, 0xe4, //0x00003008 xorl         %r12d, %r12d
	0x01, 0xf7, //0x0000300b addl         %esi, %edi
	0x41, 0x0f, 0x92, 0xc4, //0x0000300d setb         %r12b
	0x01, 0xff, //0x00003011 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003013 xorl         $1431655765, %edi
	0x21, 0xdf, //0x00003019 andl         %ebx, %edi
	0xf7, 0xd7, //0x0000301b notl         %edi
	0x21, 0xf8, //0x0000301d andl         %edi, %eax
	0x48, 0x8b, 0x75, 0xc8, //0x0000301f movq         $-56(%rbp), %rsi
	0x48, 0x85, 0xc0, //0x00003023 testq        %rax, %rax
	0x0f, 0x85, 0x63, 0xe8, 0xff, 0xff, //0x00003026 jne          LBB0_326
	//0x0000302c LBB0_653
	0x4c, 0x89, 0xef, //0x0000302c movq         %r13, %rdi
	0x49, 0x83, 0xc1, 0x20, //0x0000302f addq         $32, %r9
	0x49, 0x83, 0xc7, 0xe0, //0x00003033 addq         $-32, %r15
	//0x00003037 LBB0_654
	0x4d, 0x85, 0xe4, //0x00003037 testq        %r12, %r12
	0x0f, 0x85, 0x16, 0x02, 0x00, 0x00, //0x0000303a jne          LBB0_690
	0x48, 0x8b, 0x45, 0xc8, //0x00003040 movq         $-56(%rbp), %rax
	0x48, 0xf7, 0xd0, //0x00003044 notq         %rax
	0x4c, 0x89, 0xc1, //0x00003047 movq         %r8, %rcx
	0x4d, 0x85, 0xff, //0x0000304a testq        %r15, %r15
	0x0f, 0x84, 0x8c, 0x00, 0x00, 0x00, //0x0000304d je           LBB0_664
	//0x00003053 LBB0_656
	0x48, 0x83, 0xc0, 0x01, //0x00003053 addq         $1, %rax
	0x49, 0x89, 0xfd, //0x00003057 movq         %rdi, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x0000305a movq         $-1, %r11
	//0x00003061 LBB0_657
	0x4c, 0x89, 0xef, //0x00003061 movq         %r13, %rdi
	0x31, 0xf6, //0x00003064 xorl         %esi, %esi
	//0x00003066 LBB0_658
	0x41, 0x0f, 0xb6, 0x1c, 0x31, //0x00003066 movzbl       (%r9,%rsi), %ebx
	0x80, 0xfb, 0x22, //0x0000306b cmpb         $34, %bl
	0x0f, 0x84, 0x64, 0x00, 0x00, 0x00, //0x0000306e je           LBB0_663
	0x80, 0xfb, 0x5c, //0x00003074 cmpb         $92, %bl
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00003077 je           LBB0_661
	0x48, 0x83, 0xc6, 0x01, //0x0000307d addq         $1, %rsi
	0x49, 0x39, 0xf7, //0x00003081 cmpq         %rsi, %r15
	0x0f, 0x85, 0xdc, 0xff, 0xff, 0xff, //0x00003084 jne          LBB0_658
	0xe9, 0x5f, 0x00, 0x00, 0x00, //0x0000308a jmp          LBB0_665
	//0x0000308f LBB0_661
	0x49, 0x8d, 0x5f, 0xff, //0x0000308f leaq         $-1(%r15), %rbx
	0x48, 0x39, 0xf3, //0x00003093 cmpq         %rsi, %rbx
	0x0f, 0x84, 0x47, 0x02, 0x00, 0x00, //0x00003096 je           LBB0_696
	0x4a, 0x8d, 0x1c, 0x08, //0x0000309c leaq         (%rax,%r9), %rbx
	0x48, 0x01, 0xf3, //0x000030a0 addq         %rsi, %rbx
	0x48, 0x83, 0xf9, 0xff, //0x000030a3 cmpq         $-1, %rcx
	0x4c, 0x0f, 0x44, 0xc3, //0x000030a7 cmoveq       %rbx, %r8
	0x48, 0x0f, 0x44, 0xcb, //0x000030ab cmoveq       %rbx, %rcx
	0x49, 0x01, 0xf1, //0x000030af addq         %rsi, %r9
	0x49, 0x83, 0xc1, 0x02, //0x000030b2 addq         $2, %r9
	0x4c, 0x89, 0xfb, //0x000030b6 movq         %r15, %rbx
	0x48, 0x29, 0xf3, //0x000030b9 subq         %rsi, %rbx
	0x48, 0x83, 0xc3, 0xfe, //0x000030bc addq         $-2, %rbx
	0x49, 0x83, 0xc7, 0xfe, //0x000030c0 addq         $-2, %r15
	0x49, 0x39, 0xf7, //0x000030c4 cmpq         %rsi, %r15
	0x49, 0x89, 0xdf, //0x000030c7 movq         %rbx, %r15
	0x49, 0x89, 0xfd, //0x000030ca movq         %rdi, %r13
	0x0f, 0x85, 0x8e, 0xff, 0xff, 0xff, //0x000030cd jne          LBB0_657
	0xe9, 0x5f, 0x01, 0x00, 0x00, //0x000030d3 jmp          LBB0_688
	//0x000030d8 LBB0_663
	0x49, 0x01, 0xf1, //0x000030d8 addq         %rsi, %r9
	0x49, 0x83, 0xc1, 0x01, //0x000030db addq         $1, %r9
	//0x000030df LBB0_664
	0x4c, 0x2b, 0x4d, 0xc8, //0x000030df subq         $-56(%rbp), %r9
	0x4d, 0x89, 0xcb, //0x000030e3 movq         %r9, %r11
	0x49, 0x89, 0xfd, //0x000030e6 movq         %rdi, %r13
	0xe9, 0x67, 0xde, 0xff, 0xff, //0x000030e9 jmp          LBB0_197
	//0x000030ee LBB0_665
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000030ee movq         $-1, %r11
	0x80, 0xfb, 0x22, //0x000030f5 cmpb         $34, %bl
	0x49, 0x89, 0xfd, //0x000030f8 movq         %rdi, %r13
	0x0f, 0x85, 0x36, 0x01, 0x00, 0x00, //0x000030fb jne          LBB0_688
	0x4d, 0x01, 0xf9, //0x00003101 addq         %r15, %r9
	0xe9, 0xd6, 0xff, 0xff, 0xff, //0x00003104 jmp          LBB0_664
	//0x00003109 LBB0_667
	0x49, 0x83, 0xf8, 0xff, //0x00003109 cmpq         $-1, %r8
	0x0f, 0x85, 0x0e, 0x00, 0x00, 0x00, //0x0000310d jne          LBB0_669
	0x4c, 0x89, 0xce, //0x00003113 movq         %r9, %rsi
	0x48, 0x2b, 0x75, 0xc8, //0x00003116 subq         $-56(%rbp), %rsi
	0x4c, 0x0f, 0xbc, 0xc1, //0x0000311a bsfq         %rcx, %r8
	0x49, 0x01, 0xf0, //0x0000311e addq         %rsi, %r8
	//0x00003121 LBB0_669
	0x44, 0x89, 0xee, //0x00003121 movl         %r13d, %esi
	0xf7, 0xd6, //0x00003124 notl         %esi
	0x21, 0xce, //0x00003126 andl         %ecx, %esi
	0x8d, 0x3c, 0x36, //0x00003128 leal         (%rsi,%rsi), %edi
	0x41, 0x8d, 0x5c, 0x75, 0x00, //0x0000312b leal         (%r13,%rsi,2), %ebx
	0xf7, 0xd7, //0x00003130 notl         %edi
	0x21, 0xcf, //0x00003132 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x00003134 andl         $-1431655766, %edi
	0x45, 0x31, 0xed, //0x0000313a xorl         %r13d, %r13d
	0x01, 0xf7, //0x0000313d addl         %esi, %edi
	0x41, 0x0f, 0x92, 0xc5, //0x0000313f setb         %r13b
	0x01, 0xff, //0x00003143 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x00003145 xorl         $1431655765, %edi
	0x21, 0xdf, //0x0000314b andl         %ebx, %edi
	0xf7, 0xd7, //0x0000314d notl         %edi
	0x21, 0xf8, //0x0000314f andl         %edi, %eax
	0xc5, 0xfd, 0xdb, 0xc1, //0x00003151 vpand        %ymm1, %ymm0, %ymm0
	0x48, 0x85, 0xc0, //0x00003155 testq        %rax, %rax
	0x0f, 0x85, 0xa5, 0xe7, 0xff, 0xff, //0x00003158 jne          LBB0_331
	//0x0000315e LBB0_670
	0xb9, 0x40, 0x00, 0x00, 0x00, //0x0000315e movl         $64, %ecx
	//0x00003163 LBB0_671
	0xc5, 0xfd, 0xd7, 0xf0, //0x00003163 vpmovmskb    %ymm0, %esi
	0x48, 0x85, 0xc0, //0x00003167 testq        %rax, %rax
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x0000316a je           LBB0_674
	0x0f, 0xbc, 0xc6, //0x00003170 bsfl         %esi, %eax
	0x85, 0xf6, //0x00003173 testl        %esi, %esi
	0xbe, 0x40, 0x00, 0x00, 0x00, //0x00003175 movl         $64, %esi
	0x0f, 0x45, 0xf0, //0x0000317a cmovnel      %eax, %esi
	0x48, 0x39, 0xf1, //0x0000317d cmpq         %rsi, %rcx
	0x0f, 0x87, 0xa7, 0x00, 0x00, 0x00, //0x00003180 ja           LBB0_686
	0x4c, 0x2b, 0x4d, 0xc8, //0x00003186 subq         $-56(%rbp), %r9
	0x49, 0x8d, 0x04, 0x09, //0x0000318a leaq         (%r9,%rcx), %rax
	0x48, 0x83, 0xc0, 0x01, //0x0000318e addq         $1, %rax
	0x4d, 0x89, 0xdd, //0x00003192 movq         %r11, %r13
	0x49, 0x89, 0xc3, //0x00003195 movq         %rax, %r11
	0xe9, 0xb8, 0xdd, 0xff, 0xff, //0x00003198 jmp          LBB0_197
	//0x0000319d LBB0_674
	0x85, 0xf6, //0x0000319d testl        %esi, %esi
	0x0f, 0x85, 0x88, 0x00, 0x00, 0x00, //0x0000319f jne          LBB0_686
	0x49, 0x83, 0xc1, 0x20, //0x000031a5 addq         $32, %r9
	0x49, 0x83, 0xc7, 0xe0, //0x000031a9 addq         $-32, %r15
	//0x000031ad LBB0_676
	0x4d, 0x85, 0xed, //0x000031ad testq        %r13, %r13
	0x0f, 0x85, 0xd9, 0x00, 0x00, 0x00, //0x000031b0 jne          LBB0_692
	0x4c, 0x89, 0xc0, //0x000031b6 movq         %r8, %rax
	0x4d, 0x85, 0xff, //0x000031b9 testq        %r15, %r15
	0x0f, 0x84, 0x03, 0x01, 0x00, 0x00, //0x000031bc je           LBB0_694
	//0x000031c2 LBB0_678
	0x41, 0x0f, 0xb6, 0x09, //0x000031c2 movzbl       (%r9), %ecx
	0x80, 0xf9, 0x22, //0x000031c6 cmpb         $34, %cl
	0x0f, 0x84, 0x74, 0x00, 0x00, 0x00, //0x000031c9 je           LBB0_689
	0x80, 0xf9, 0x5c, //0x000031cf cmpb         $92, %cl
	0x0f, 0x84, 0x26, 0x00, 0x00, 0x00, //0x000031d2 je           LBB0_683
	0x80, 0xf9, 0x20, //0x000031d8 cmpb         $32, %cl
	0x0f, 0x82, 0x4c, 0x00, 0x00, 0x00, //0x000031db jb           LBB0_686
	0x48, 0xc7, 0xc1, 0xff, 0xff, 0xff, 0xff, //0x000031e1 movq         $-1, %rcx
	0xbe, 0x01, 0x00, 0x00, 0x00, //0x000031e8 movl         $1, %esi
	//0x000031ed LBB0_682
	0x49, 0x01, 0xf1, //0x000031ed addq         %rsi, %r9
	0x49, 0x01, 0xcf, //0x000031f0 addq         %rcx, %r15
	0x0f, 0x85, 0xc9, 0xff, 0xff, 0xff, //0x000031f3 jne          LBB0_678
	0xe9, 0xc7, 0x00, 0x00, 0x00, //0x000031f9 jmp          LBB0_694
	//0x000031fe LBB0_683
	0x49, 0x83, 0xff, 0x01, //0x000031fe cmpq         $1, %r15
	0x0f, 0x84, 0xbd, 0x00, 0x00, 0x00, //0x00003202 je           LBB0_694
	0x48, 0xc7, 0xc1, 0xfe, 0xff, 0xff, 0xff, //0x00003208 movq         $-2, %rcx
	0xbe, 0x02, 0x00, 0x00, 0x00, //0x0000320f movl         $2, %esi
	0x48, 0x83, 0xf8, 0xff, //0x00003214 cmpq         $-1, %rax
	0x0f, 0x85, 0xcf, 0xff, 0xff, 0xff, //0x00003218 jne          LBB0_682
	0x4d, 0x89, 0xc8, //0x0000321e movq         %r9, %r8
	0x4c, 0x2b, 0x45, 0xc8, //0x00003221 subq         $-56(%rbp), %r8
	0x4c, 0x89, 0xc0, //0x00003225 movq         %r8, %rax
	0xe9, 0xc0, 0xff, 0xff, 0xff, //0x00003228 jmp          LBB0_682
	//0x0000322d LBB0_686
	0x4d, 0x89, 0xdd, //0x0000322d movq         %r11, %r13
	//0x00003230 LBB0_687
	0x49, 0xc7, 0xc3, 0xfe, 0xff, 0xff, 0xff, //0x00003230 movq         $-2, %r11
	//0x00003237 LBB0_688
	0x4d, 0x89, 0x5d, 0x00, //0x00003237 movq         %r11, (%r13)
	0x4d, 0x89, 0xf3, //0x0000323b movq         %r14, %r11
	0xe9, 0xc5, 0xe0, 0xff, 0xff, //0x0000323e jmp          LBB0_263
	//0x00003243 LBB0_689
	0x4c, 0x2b, 0x4d, 0xc8, //0x00003243 subq         $-56(%rbp), %r9
	0x49, 0x83, 0xc1, 0x01, //0x00003247 addq         $1, %r9
	0x4d, 0x89, 0xdd, //0x0000324b movq         %r11, %r13
	0x4d, 0x89, 0xcb, //0x0000324e movq         %r9, %r11
	0xe9, 0xff, 0xdc, 0xff, 0xff, //0x00003251 jmp          LBB0_197
	//0x00003256 LBB0_690
	0x4d, 0x85, 0xff, //0x00003256 testq        %r15, %r15
	0x0f, 0x84, 0x75, 0x00, 0x00, 0x00, //0x00003259 je           LBB0_695
	0x48, 0x8b, 0x45, 0xc8, //0x0000325f movq         $-56(%rbp), %rax
	0x48, 0xf7, 0xd0, //0x00003263 notq         %rax
	0x49, 0x8d, 0x34, 0x01, //0x00003266 leaq         (%r9,%rax), %rsi
	0x49, 0x83, 0xf8, 0xff, //0x0000326a cmpq         $-1, %r8
	0x4c, 0x89, 0xc1, //0x0000326e movq         %r8, %rcx
	0x4c, 0x0f, 0x44, 0xc6, //0x00003271 cmoveq       %rsi, %r8
	0x48, 0x0f, 0x44, 0xce, //0x00003275 cmoveq       %rsi, %rcx
	0x49, 0x83, 0xc1, 0x01, //0x00003279 addq         $1, %r9
	0x49, 0x83, 0xc7, 0xff, //0x0000327d addq         $-1, %r15
	0x4d, 0x85, 0xff, //0x00003281 testq        %r15, %r15
	0x0f, 0x85, 0xc9, 0xfd, 0xff, 0xff, //0x00003284 jne          LBB0_656
	0xe9, 0x50, 0xfe, 0xff, 0xff, //0x0000328a jmp          LBB0_664
	//0x0000328f LBB0_692
	0x4d, 0x85, 0xff, //0x0000328f testq        %r15, %r15
	0x0f, 0x84, 0x2d, 0x00, 0x00, 0x00, //0x00003292 je           LBB0_694
	0x48, 0x8b, 0x45, 0xc8, //0x00003298 movq         $-56(%rbp), %rax
	0x48, 0xf7, 0xd0, //0x0000329c notq         %rax
	0x4c, 0x01, 0xc8, //0x0000329f addq         %r9, %rax
	0x49, 0x83, 0xf8, 0xff, //0x000032a2 cmpq         $-1, %r8
	0x4c, 0x89, 0xc1, //0x000032a6 movq         %r8, %rcx
	0x48, 0x0f, 0x44, 0xc8, //0x000032a9 cmoveq       %rax, %rcx
	0x49, 0x0f, 0x45, 0xc0, //0x000032ad cmovneq      %r8, %rax
	0x49, 0x83, 0xc1, 0x01, //0x000032b1 addq         $1, %r9
	0x49, 0x83, 0xc7, 0xff, //0x000032b5 addq         $-1, %r15
	0x49, 0x89, 0xc8, //0x000032b9 movq         %rcx, %r8
	0x4d, 0x85, 0xff, //0x000032bc testq        %r15, %r15
	0x0f, 0x85, 0xfd, 0xfe, 0xff, 0xff, //0x000032bf jne          LBB0_678
	//0x000032c5 LBB0_694
	0x4d, 0x89, 0xdd, //0x000032c5 movq         %r11, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000032c8 movq         $-1, %r11
	0xe9, 0x63, 0xff, 0xff, 0xff, //0x000032cf jmp          LBB0_688
	//0x000032d4 LBB0_695
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000032d4 movq         $-1, %r11
	0x49, 0x89, 0xfd, //0x000032db movq         %rdi, %r13
	0xe9, 0x54, 0xff, 0xff, 0xff, //0x000032de jmp          LBB0_688
	//0x000032e3 LBB0_696
	0x49, 0x89, 0xfd, //0x000032e3 movq         %rdi, %r13
	0x49, 0xc7, 0xc3, 0xff, 0xff, 0xff, 0xff, //0x000032e6 movq         $-1, %r11
	0xe9, 0x45, 0xff, 0xff, 0xff, //0x000032ed jmp          LBB0_688
	0x90, 0x90, //0x000032f2 .p2align 2, 0x90
	// // .set L0_0_set_261, LBB0_261-LJTI0_0
	// // .set L0_0_set_131, LBB0_131-LJTI0_0
	// // .set L0_0_set_113, LBB0_113-LJTI0_0
	// // .set L0_0_set_126, LBB0_126-LJTI0_0
	// // .set L0_0_set_34, LBB0_34-LJTI0_0
	// // .set L0_0_set_132, LBB0_132-LJTI0_0
	// // .set L0_0_set_133, LBB0_133-LJTI0_0
	// // .set L0_0_set_140, LBB0_140-LJTI0_0
	// // .set L0_0_set_134, LBB0_134-LJTI0_0
	// // .set L0_0_set_127, LBB0_127-LJTI0_0
	// // .set L0_0_set_137, LBB0_137-LJTI0_0
	// // .set L0_0_set_142, LBB0_142-LJTI0_0
	// // .set L0_0_set_130, LBB0_130-LJTI0_0
	//0x000032f4 LJTI0_0
	0x09, 0xe0, 0xff, 0xff, //0x000032f4 .long L0_0_set_261
	0x26, 0xd8, 0xff, 0xff, //0x000032f8 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000032fc .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003300 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003304 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003308 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000330c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003310 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003314 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003318 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000331c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003320 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003324 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003328 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000332c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003330 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003334 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003338 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000333c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003340 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003344 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003348 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000334c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003350 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003354 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003358 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000335c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003360 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003364 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003368 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000336c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003370 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003374 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003378 .long L0_0_set_131
	0x87, 0xd6, 0xff, 0xff, //0x0000337c .long L0_0_set_113
	0x26, 0xd8, 0xff, 0xff, //0x00003380 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003384 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003388 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000338c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003390 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003394 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003398 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000339c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033a0 .long L0_0_set_131
	0xcd, 0xd7, 0xff, 0xff, //0x000033a4 .long L0_0_set_126
	0x23, 0xd1, 0xff, 0xff, //0x000033a8 .long L0_0_set_34
	0x26, 0xd8, 0xff, 0xff, //0x000033ac .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033b0 .long L0_0_set_131
	0x23, 0xd1, 0xff, 0xff, //0x000033b4 .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033b8 .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033bc .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033c0 .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033c4 .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033c8 .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033cc .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033d0 .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033d4 .long L0_0_set_34
	0x23, 0xd1, 0xff, 0xff, //0x000033d8 .long L0_0_set_34
	0x36, 0xd8, 0xff, 0xff, //0x000033dc .long L0_0_set_132
	0x26, 0xd8, 0xff, 0xff, //0x000033e0 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033e4 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033e8 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033ec .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033f0 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033f4 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033f8 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000033fc .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003400 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003404 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003408 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000340c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003410 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003414 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003418 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000341c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003420 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003424 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003428 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000342c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003430 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003434 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003438 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000343c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003440 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003444 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003448 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000344c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003450 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003454 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003458 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000345c .long L0_0_set_131
	0x4a, 0xd8, 0xff, 0xff, //0x00003460 .long L0_0_set_133
	0x26, 0xd8, 0xff, 0xff, //0x00003464 .long L0_0_set_131
	0xb9, 0xd8, 0xff, 0xff, //0x00003468 .long L0_0_set_140
	0x26, 0xd8, 0xff, 0xff, //0x0000346c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003470 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003474 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003478 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000347c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003480 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003484 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003488 .long L0_0_set_131
	0x57, 0xd8, 0xff, 0xff, //0x0000348c .long L0_0_set_134
	0x26, 0xd8, 0xff, 0xff, //0x00003490 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003494 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x00003498 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x0000349c .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034a0 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034a4 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034a8 .long L0_0_set_131
	0xe1, 0xd7, 0xff, 0xff, //0x000034ac .long L0_0_set_127
	0x26, 0xd8, 0xff, 0xff, //0x000034b0 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034b4 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034b8 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034bc .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034c0 .long L0_0_set_131
	0x88, 0xd8, 0xff, 0xff, //0x000034c4 .long L0_0_set_137
	0x26, 0xd8, 0xff, 0xff, //0x000034c8 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034cc .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034d0 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034d4 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034d8 .long L0_0_set_131
	0x26, 0xd8, 0xff, 0xff, //0x000034dc .long L0_0_set_131
	0xe2, 0xd8, 0xff, 0xff, //0x000034e0 .long L0_0_set_142
	0x26, 0xd8, 0xff, 0xff, //0x000034e4 .long L0_0_set_131
	0x12, 0xd8, 0xff, 0xff, //0x000034e8 .long L0_0_set_130
	// // .set L0_1_set_92, LBB0_92-LJTI0_1
	// // .set L0_1_set_144, LBB0_144-LJTI0_1
	// // .set L0_1_set_86, LBB0_86-LJTI0_1
	// // .set L0_1_set_95, LBB0_95-LJTI0_1
	//0x000034ec LJTI0_1
	0x77, 0xd3, 0xff, 0xff, //0x000034ec .long L0_1_set_92
	0x08, 0xd7, 0xff, 0xff, //0x000034f0 .long L0_1_set_144
	0x77, 0xd3, 0xff, 0xff, //0x000034f4 .long L0_1_set_92
	0x2c, 0xd3, 0xff, 0xff, //0x000034f8 .long L0_1_set_86
	0x08, 0xd7, 0xff, 0xff, //0x000034fc .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003500 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003504 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003508 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x0000350c .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003510 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003514 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003518 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x0000351c .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003520 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003524 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003528 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x0000352c .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003530 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003534 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003538 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x0000353c .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003540 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003544 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003548 .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x0000354c .long L0_1_set_144
	0x08, 0xd7, 0xff, 0xff, //0x00003550 .long L0_1_set_144
	0x93, 0xd3, 0xff, 0xff, //0x00003554 .long L0_1_set_95
	//0x00003558 .p2align 2, 0x00
	//0x00003558 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00003558 .long 2
	0x00, 0x00, 0x00, 0x00, //0x0000355c .p2align 4, 0x00
	//0x00003560 _P10_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x3f, //0x00003560 .quad 0x3ff0000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, //0x00003568 .quad 0x4024000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, //0x00003570 .quad 0x4059000000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x8f, 0x40, //0x00003578 .quad 0x408f400000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0xc3, 0x40, //0x00003580 .quad 0x40c3880000000000
	0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0xf8, 0x40, //0x00003588 .quad 0x40f86a0000000000
	0x00, 0x00, 0x00, 0x00, 0x80, 0x84, 0x2e, 0x41, //0x00003590 .quad 0x412e848000000000
	0x00, 0x00, 0x00, 0x00, 0xd0, 0x12, 0x63, 0x41, //0x00003598 .quad 0x416312d000000000
	0x00, 0x00, 0x00, 0x00, 0x84, 0xd7, 0x97, 0x41, //0x000035a0 .quad 0x4197d78400000000
	0x00, 0x00, 0x00, 0x00, 0x65, 0xcd, 0xcd, 0x41, //0x000035a8 .quad 0x41cdcd6500000000
	0x00, 0x00, 0x00, 0x20, 0x5f, 0xa0, 0x02, 0x42, //0x000035b0 .quad 0x4202a05f20000000
	0x00, 0x00, 0x00, 0xe8, 0x76, 0x48, 0x37, 0x42, //0x000035b8 .quad 0x42374876e8000000
	0x00, 0x00, 0x00, 0xa2, 0x94, 0x1a, 0x6d, 0x42, //0x000035c0 .quad 0x426d1a94a2000000
	0x00, 0x00, 0x40, 0xe5, 0x9c, 0x30, 0xa2, 0x42, //0x000035c8 .quad 0x42a2309ce5400000
	0x00, 0x00, 0x90, 0x1e, 0xc4, 0xbc, 0xd6, 0x42, //0x000035d0 .quad 0x42d6bcc41e900000
	0x00, 0x00, 0x34, 0x26, 0xf5, 0x6b, 0x0c, 0x43, //0x000035d8 .quad 0x430c6bf526340000
	0x00, 0x80, 0xe0, 0x37, 0x79, 0xc3, 0x41, 0x43, //0x000035e0 .quad 0x4341c37937e08000
	0x00, 0xa0, 0xd8, 0x85, 0x57, 0x34, 0x76, 0x43, //0x000035e8 .quad 0x4376345785d8a000
	0x00, 0xc8, 0x4e, 0x67, 0x6d, 0xc1, 0xab, 0x43, //0x000035f0 .quad 0x43abc16d674ec800
	0x00, 0x3d, 0x91, 0x60, 0xe4, 0x58, 0xe1, 0x43, //0x000035f8 .quad 0x43e158e460913d00
	0x40, 0x8c, 0xb5, 0x78, 0x1d, 0xaf, 0x15, 0x44, //0x00003600 .quad 0x4415af1d78b58c40
	0x50, 0xef, 0xe2, 0xd6, 0xe4, 0x1a, 0x4b, 0x44, //0x00003608 .quad 0x444b1ae4d6e2ef50
	0x92, 0xd5, 0x4d, 0x06, 0xcf, 0xf0, 0x80, 0x44, //0x00003610 .quad 0x4480f0cf064dd592
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00003618 .p2align 4, 0x00
	//0x00003620 _POW10_M128_TAB
	0x53, 0xe4, 0x60, 0xcd, 0x69, 0xc8, 0x32, 0x17, //0x00003620 .quad 1671618768450675795
	0x88, 0x02, 0x1c, 0x08, 0xa0, 0xd5, 0x8f, 0xfa, //0x00003628 .quad -391859759250406776
	0xb4, 0x8e, 0x5c, 0x20, 0x42, 0xbd, 0x7f, 0x0e, //0x00003630 .quad 1044761730281672372
	0x95, 0x81, 0x11, 0x05, 0x84, 0xe5, 0x99, 0x9c, //0x00003638 .quad -7162441377172586091
	0x61, 0xb2, 0x73, 0xa8, 0x92, 0xac, 0x1f, 0x52, //0x00003640 .quad 5917638181279478369
	0xfa, 0xe1, 0x55, 0x06, 0xe5, 0x5e, 0xc0, 0xc3, //0x00003648 .quad -4341365703038344710
	0xf9, 0x9e, 0x90, 0x52, 0xb7, 0x97, 0xa7, 0xe6, //0x00003650 .quad -1826324310255427847
	0x78, 0x5a, 0xeb, 0x47, 0x9e, 0x76, 0xb0, 0xf4, //0x00003658 .quad -815021110370542984
	0x5c, 0x63, 0x9a, 0x93, 0xd2, 0xbe, 0x28, 0x90, //0x00003660 .quad -8058981721550724260
	0x8b, 0x18, 0xf3, 0xec, 0x22, 0x4a, 0xee, 0x98, //0x00003668 .quad -7426917221622671221
	0x33, 0xfc, 0x80, 0x38, 0x87, 0xee, 0x32, 0x74, //0x00003670 .quad 8373016921771146291
	0xae, 0xde, 0x2f, 0xa8, 0xab, 0xdc, 0x29, 0xbf, //0x00003678 .quad -4671960508600951122
	0x3f, 0x3b, 0xa1, 0x06, 0x29, 0xaa, 0x3f, 0x11, //0x00003680 .quad 1242899115359157055
	0x5a, 0xd6, 0x3b, 0x92, 0xd6, 0x53, 0xf4, 0xee, //0x00003688 .quad -1228264617323800998
	0x07, 0xc5, 0x24, 0xa4, 0x59, 0xca, 0xc7, 0x4a, //0x00003690 .quad 5388497965526861063
	0xf8, 0x65, 0x65, 0x1b, 0x66, 0xb4, 0x58, 0x95, //0x00003698 .quad -7685194413468457480
	0x49, 0xf6, 0x2d, 0x0d, 0xf0, 0xbc, 0x79, 0x5d, //0x000036a0 .quad 6735622456908576329
	0x76, 0xbf, 0x3e, 0xa2, 0x7f, 0xe1, 0xae, 0xba, //0x000036a8 .quad -4994806998408183946
	0xdc, 0x73, 0x79, 0x10, 0x2c, 0x2c, 0xd8, 0xf4, //0x000036b0 .quad -803843965719055396
	0x53, 0x6f, 0xce, 0x8a, 0xdf, 0x99, 0x5a, 0xe9, //0x000036b8 .quad -1631822729582842029
	0x69, 0xe8, 0x4b, 0x8a, 0x9b, 0x1b, 0x07, 0x79, //0x000036c0 .quad 8720969558280366185
	0x94, 0x05, 0xc1, 0xb6, 0x2b, 0xa0, 0xd8, 0x91, //0x000036c8 .quad -7937418233630358124
	0x84, 0xe2, 0xde, 0x6c, 0x82, 0xe2, 0x48, 0x97, //0x000036d0 .quad -7545532125859093884
	0xf9, 0x46, 0x71, 0xa4, 0x36, 0xc8, 0x4e, 0xb6, //0x000036d8 .quad -5310086773610559751
	0x25, 0x9b, 0x16, 0x08, 0x23, 0x1b, 0x1b, 0xfd, //0x000036e0 .quad -208543120469091547
	0xb7, 0x98, 0x8d, 0x4d, 0x44, 0x7a, 0xe2, 0xe3, //0x000036e8 .quad -2025922448585811785
	0xf7, 0x20, 0x0e, 0xe5, 0xf5, 0xf0, 0x30, 0xfe, //0x000036f0 .quad -130339450293182217
	0x72, 0x7f, 0x78, 0xb0, 0x6a, 0x8c, 0x6d, 0x8e, //0x000036f8 .quad -8183730558007214222
	0x35, 0xa9, 0x51, 0x5e, 0x33, 0x2d, 0xbd, 0xbd, //0x00003700 .quad -4774610331293865675
	0x4f, 0x9f, 0x96, 0x5c, 0x85, 0xef, 0x08, 0xb2, //0x00003708 .quad -5617977179081629873
	0x82, 0x13, 0xe6, 0x35, 0x80, 0x78, 0x2c, 0xad, //0x00003710 .quad -5968262914117332094
	0x23, 0x47, 0xbc, 0xb3, 0x66, 0x2b, 0x8b, 0xde, //0x00003718 .quad -2410785455424649437
	0x31, 0xcc, 0xaf, 0x21, 0x50, 0xcb, 0x3b, 0x4c, //0x00003720 .quad 5493207715531443249
	0x76, 0xac, 0x55, 0x30, 0x20, 0xfb, 0x16, 0x8b, //0x00003728 .quad -8424269937281487754
	0x3d, 0xbf, 0x1b, 0x2a, 0x24, 0xbe, 0x4a, 0xdf, //0x00003730 .quad -2356862392440471747
	0x93, 0x17, 0x6b, 0x3c, 0xe8, 0xb9, 0xdc, 0xad, //0x00003738 .quad -5918651403174471789
	0x0d, 0xaf, 0xa2, 0x34, 0xad, 0x6d, 0x1d, 0xd7, //0x00003740 .quad -2946077990550589683
	0x78, 0xdd, 0x85, 0x4b, 0x62, 0xe8, 0x53, 0xd9, //0x00003748 .quad -2786628235540701832
	0x68, 0xad, 0xe5, 0x40, 0x8c, 0x64, 0x72, 0x86, //0x00003750 .quad -8758827771735200408
	0x6b, 0xaa, 0x33, 0x6f, 0x3d, 0x71, 0xd4, 0x87, //0x00003758 .quad -8659171674854020501
	0xc2, 0x18, 0x1f, 0x51, 0xaf, 0xfd, 0x0e, 0x68, //0x00003760 .quad 7498209359040551106
	0x06, 0x95, 0x00, 0xcb, 0x8c, 0x8d, 0xc9, 0xa9, //0x00003768 .quad -6212278575140137722
	0xf2, 0xde, 0x66, 0x25, 0x1b, 0xbd, 0x12, 0x02, //0x00003770 .quad 149389661945913074
	0x48, 0xba, 0xc0, 0xfd, 0xef, 0xf0, 0x3b, 0xd4, //0x00003778 .quad -3153662200497784248
	0x57, 0x4b, 0x60, 0xf7, 0x30, 0xb6, 0x4b, 0x01, //0x00003780 .quad 93368538716195671
	0x6d, 0x74, 0x98, 0xfe, 0x95, 0x76, 0xa5, 0x84, //0x00003788 .quad -8888567902952197011
	0x2d, 0x5e, 0x38, 0x35, 0xbd, 0xa3, 0x9e, 0x41, //0x00003790 .quad 4728396691822632493
	0x88, 0x91, 0x3e, 0x7e, 0x3b, 0xd4, 0xce, 0xa5, //0x00003798 .quad -6499023860262858360
	0xb9, 0x75, 0x86, 0x82, 0xac, 0x4c, 0x06, 0x52, //0x000037a0 .quad 5910495864778290617
	0xea, 0x35, 0xce, 0x5d, 0x4a, 0x89, 0x42, 0xcf, //0x000037a8 .quad -3512093806901185046
	0x93, 0x09, 0x94, 0xd1, 0xeb, 0xef, 0x43, 0x73, //0x000037b0 .quad 8305745933913819539
	0xb2, 0xe1, 0xa0, 0x7a, 0xce, 0x95, 0x89, 0x81, //0x000037b8 .quad -9112587656954322510
	0xf8, 0x0b, 0xf9, 0xc5, 0xe6, 0xeb, 0x14, 0x10, //0x000037c0 .quad 1158810380537498616
	0x1f, 0x1a, 0x49, 0x19, 0x42, 0xfb, 0xeb, 0xa1, //0x000037c8 .quad -6779048552765515233
	0xf6, 0x4e, 0x77, 0x77, 0xe0, 0x26, 0x1a, 0xd4, //0x000037d0 .quad -3163173042755514634
	0xa6, 0x60, 0x9b, 0x9f, 0x12, 0xfa, 0x66, 0xca, //0x000037d8 .quad -3862124672529506138
	0xb4, 0x22, 0x55, 0x95, 0x98, 0xb0, 0x20, 0x89, //0x000037e0 .quad -8565652321871781196
	0xd0, 0x38, 0x82, 0x47, 0x97, 0xb8, 0x00, 0xfd, //0x000037e8 .quad -215969822234494768
	0xb0, 0x35, 0x55, 0x5d, 0x5f, 0x6e, 0xb4, 0x55, //0x000037f0 .quad 6175682344898606512
	0x82, 0x63, 0xb1, 0x8c, 0x5e, 0x73, 0x20, 0x9e, //0x000037f8 .quad -7052510166537641086
	0x1d, 0x83, 0xaa, 0x34, 0xf7, 0x89, 0x21, 0xeb, //0x00003800 .quad -1503769105731517667
	0x62, 0xbc, 0xdd, 0x2f, 0x36, 0x90, 0xa8, 0xc5, //0x00003808 .quad -4203951689744663454
	0xe4, 0x23, 0xd5, 0x01, 0x75, 0xec, 0xe9, 0xa5, //0x00003810 .quad -6491397400591784988
	0x7b, 0x2b, 0xd5, 0xbb, 0x43, 0xb4, 0x12, 0xf7, //0x00003818 .quad -643253593753441413
	0x6e, 0x36, 0x25, 0x21, 0xc9, 0x33, 0xb2, 0x47, //0x00003820 .quad 5166248661484910190
	0x2d, 0x3b, 0x65, 0x55, 0xaa, 0xb0, 0x6b, 0x9a, //0x00003828 .quad -7319562523736982739
	0x0a, 0x84, 0x6e, 0x69, 0xbb, 0xc0, 0x9e, 0x99, //0x00003830 .quad -7377247228426025974
	0xf8, 0x89, 0xbe, 0xea, 0xd4, 0x9c, 0x06, 0xc1, //0x00003838 .quad -4537767136243840520
	0x0d, 0x25, 0xca, 0x43, 0xea, 0x70, 0x06, 0xc0, //0x00003840 .quad -4609873017105144563
	0x76, 0x2c, 0x6e, 0x25, 0x0a, 0x44, 0x48, 0xf1, //0x00003848 .quad -1060522901877412746
	0x28, 0x57, 0x5e, 0x6a, 0x92, 0x06, 0x04, 0x38, //0x00003850 .quad 4036358391950366504
	0xca, 0xdb, 0x64, 0x57, 0x86, 0x2a, 0xcd, 0x96, //0x00003858 .quad -7580355841314464822
	0xf2, 0xec, 0xf5, 0x04, 0x37, 0x08, 0x05, 0xc6, //0x00003860 .quad -4177924046916817678
	0xbc, 0x12, 0x3e, 0xed, 0x27, 0x75, 0x80, 0xbc, //0x00003868 .quad -4863758783215693124
	0x2e, 0x68, 0x33, 0xc6, 0x44, 0x4a, 0x86, 0xf7, //0x00003870 .quad -610719040218634194
	0x6b, 0x97, 0x8d, 0xe8, 0x71, 0x92, 0xa0, 0xeb, //0x00003878 .quad -1468012460592228501
	0x1d, 0x21, 0xe0, 0xfb, 0x6a, 0xee, 0xb3, 0x7a, //0x00003880 .quad 8841672636718129437
	0xa3, 0x7e, 0x58, 0x31, 0x87, 0x5b, 0x44, 0x93, //0x00003888 .quad -7835036815511224669
	0x64, 0x29, 0xd8, 0xba, 0x05, 0xea, 0x60, 0x59, //0x00003890 .quad 6440404777470273892
	0x4c, 0x9e, 0xae, 0xfd, 0x68, 0x72, 0x15, 0xb8, //0x00003898 .quad -5182110000961642932
	0xbd, 0x33, 0x8e, 0x29, 0x87, 0x24, 0xb9, 0x6f, //0x000038a0 .quad 8050505971837842365
	0xdf, 0x45, 0x1a, 0x3d, 0x03, 0xcf, 0x1a, 0xe6, //0x000038a8 .quad -1865951482774665761
	0x56, 0xe0, 0xf8, 0x79, 0xd4, 0xb6, 0xd3, 0xa5, //0x000038b0 .quad -6497648813669818282
	0xab, 0x6b, 0x30, 0x06, 0x62, 0xc1, 0xd0, 0x8f, //0x000038b8 .quad -8083748704375247957
	0x6c, 0x18, 0x77, 0x98, 0x89, 0xa4, 0x48, 0x8f, //0x000038c0 .quad -8122061017087272852
	0x96, 0x86, 0xbc, 0x87, 0xba, 0xf1, 0xc4, 0xb3, //0x000038c8 .quad -5492999862041672042
	0x87, 0xde, 0x94, 0xfe, 0xab, 0xcd, 0x1a, 0x33, //0x000038d0 .quad 3682481783923072647
	0x3c, 0xa8, 0xab, 0x29, 0x29, 0x2e, 0xb6, 0xe0, //0x000038d8 .quad -2254563809124702148
	0x14, 0x0b, 0x1d, 0x7f, 0x8b, 0xc0, 0xf0, 0x9f, //0x000038e0 .quad -6921820921902855404
	0x25, 0x49, 0x0b, 0xba, 0xd9, 0xdc, 0x71, 0x8c, //0x000038e8 .quad -8326631408344020699
	0xd9, 0x4d, 0xe4, 0x5e, 0xae, 0xf0, 0xec, 0x07, //0x000038f0 .quad 571095884476206553
	0x6f, 0x1b, 0x8e, 0x28, 0x10, 0x54, 0x8e, 0xaf, //0x000038f8 .quad -5796603242002637969
	0x50, 0x61, 0x9d, 0xf6, 0xd9, 0x2c, 0xe8, 0xc9, //0x00003900 .quad -3897816162832129712
	0x4a, 0xa2, 0xb1, 0x32, 0x14, 0xe9, 0x71, 0xdb, //0x00003908 .quad -2634068034075909558
	0xd2, 0x5c, 0x22, 0x3a, 0x08, 0x1c, 0x31, 0xbe, //0x00003910 .quad -4741978110983775022
	0x6e, 0x05, 0xaf, 0x9f, 0xac, 0x31, 0x27, 0x89, //0x00003918 .quad -8563821548938525330
	0x06, 0xf4, 0xaa, 0x48, 0x0a, 0x63, 0xbd, 0x6d, //0x00003920 .quad 7907585416552444934
	0xca, 0xc6, 0x9a, 0xc7, 0x17, 0xfe, 0x70, 0xab, //0x00003928 .quad -6093090917745768758
	0x08, 0xb1, 0xd5, 0xda, 0xcc, 0xbb, 0x2c, 0x09, //0x00003930 .quad 661109733835780360
	0x7d, 0x78, 0x81, 0xb9, 0x9d, 0x3d, 0x4d, 0xd6, //0x00003938 .quad -3004677628754823043
	0xa5, 0x8e, 0xc5, 0x08, 0x60, 0xf5, 0xbb, 0x25, //0x00003940 .quad 2719036592861056677
	0x4e, 0xeb, 0xf0, 0x93, 0x82, 0x46, 0xf0, 0x85, //0x00003948 .quad -8795452545612846258
	0x4e, 0xf2, 0xf6, 0x0a, 0xb8, 0xf2, 0x2a, 0xaf, //0x00003950 .quad -5824576295778454962
	0x21, 0x26, 0xed, 0x38, 0x23, 0x58, 0x6c, 0xa7, //0x00003958 .quad -6382629663588669919
	0xe1, 0xae, 0xb4, 0x0d, 0x66, 0xaf, 0xf5, 0x1a, //0x00003960 .quad 1942651667131707105
	0xaa, 0x6f, 0x28, 0x07, 0x2c, 0x6e, 0x47, 0xd1, //0x00003968 .quad -3366601061058449494
	0x4d, 0xed, 0x90, 0xc8, 0x9f, 0x8d, 0xd9, 0x50, //0x00003970 .quad 5825843310384704845
	0xca, 0x45, 0x79, 0x84, 0xdb, 0xa4, 0xcc, 0x82, //0x00003978 .quad -9021654690802612790
	0xa0, 0x28, 0xb5, 0xba, 0x07, 0xf1, 0x0f, 0xe5, //0x00003980 .quad -1941067898873894752
	0x3c, 0x97, 0x97, 0x65, 0x12, 0xce, 0x7f, 0xa3, //0x00003988 .quad -6665382345075878084
	0xc8, 0x72, 0x62, 0xa9, 0x49, 0xed, 0x53, 0x1e, //0x00003990 .quad 2185351144835019464
	0x0c, 0x7d, 0xfd, 0xfe, 0x96, 0xc1, 0x5f, 0xcc, //0x00003998 .quad -3720041912917459700
	0x7a, 0x0f, 0xbb, 0x13, 0x9c, 0xe8, 0xe8, 0x25, //0x000039a0 .quad 2731688931043774330
	0x4f, 0xdc, 0xbc, 0xbe, 0xfc, 0xb1, 0x77, 0xff, //0x000039a8 .quad -38366372719436721
	0xac, 0xe9, 0x54, 0x8c, 0x61, 0x91, 0xb1, 0x77, //0x000039b0 .quad 8624834609543440812
	0xb1, 0x09, 0x36, 0xf7, 0x3d, 0xcf, 0xaa, 0x9f, //0x000039b8 .quad -6941508010590729807
	0x17, 0x24, 0x6a, 0xef, 0xb9, 0xf5, 0x9d, 0xd5, //0x000039c0 .quad -3054014793352862697
	0x1d, 0x8c, 0x03, 0x75, 0x0d, 0x83, 0x95, 0xc7, //0x000039c8 .quad -4065198994811024355
	0x1d, 0xad, 0x44, 0x6b, 0x28, 0x73, 0x05, 0x4b, //0x000039d0 .quad 5405853545163697437
	0x25, 0x6f, 0x44, 0xd2, 0xd0, 0xe3, 0x7a, 0xf9, //0x000039d8 .quad -469812725086392539
	0x32, 0xec, 0x0a, 0x43, 0xf9, 0x67, 0xe3, 0x4e, //0x000039e0 .quad 5684501474941004850
	0x77, 0xc5, 0x6a, 0x83, 0x62, 0xce, 0xec, 0x9b, //0x000039e8 .quad -7211161980820077193
	0x3f, 0xa7, 0xcd, 0x93, 0xf7, 0x41, 0x9c, 0x22, //0x000039f0 .quad 2493940825248868159
	0xd5, 0x76, 0x45, 0x24, 0xfb, 0x01, 0xe8, 0xc2, //0x000039f8 .quad -4402266457597708587
	0x0f, 0x11, 0xc1, 0x78, 0x75, 0x52, 0x43, 0x6b, //0x00003a00 .quad 7729112049988473103
	0x8a, 0xd4, 0x56, 0xed, 0x79, 0x02, 0xa2, 0xf3, //0x00003a08 .quad -891147053569747830
	0xa9, 0xaa, 0x78, 0x6b, 0x89, 0x13, 0x0a, 0x83, //0x00003a10 .quad -9004363024039368023
	0xd6, 0x44, 0x56, 0x34, 0x8c, 0x41, 0x45, 0x98, //0x00003a18 .quad -7474495936122174250
	0x53, 0xd5, 0x56, 0xc6, 0x6b, 0x98, 0xcc, 0x23, //0x00003a20 .quad 2579604275232953683
	0x0c, 0xd6, 0x6b, 0x41, 0xef, 0x91, 0x56, 0xbe, //0x00003a28 .quad -4731433901725329908
	0xa8, 0x8a, 0xec, 0xb7, 0x86, 0xbe, 0xbf, 0x2c, //0x00003a30 .quad 3224505344041192104
	0x8f, 0xcb, 0xc6, 0x11, 0x6b, 0x36, 0xec, 0xed, //0x00003a38 .quad -1302606358729274481
	0xa9, 0xd6, 0xf3, 0x32, 0x14, 0xd7, 0xf7, 0x7b, //0x00003a40 .quad 8932844867666826921
	0x39, 0x3f, 0x1c, 0xeb, 0x02, 0xa2, 0xb3, 0x94, //0x00003a48 .quad -7731658001846878407
	0x53, 0xcc, 0xb0, 0x3f, 0xd9, 0xcc, 0xf5, 0xda, //0x00003a50 .quad -2669001970698630061
	0x07, 0x4f, 0xe3, 0xa5, 0x83, 0x8a, 0xe0, 0xb9, //0x00003a58 .quad -5052886483881210105
	0x68, 0xff, 0x9c, 0x8f, 0x0f, 0x40, 0xb3, 0xd1, //0x00003a60 .quad -3336252463373287576
	0xc9, 0x22, 0x5c, 0x8f, 0x24, 0xad, 0x58, 0xe8, //0x00003a68 .quad -1704422086424124727
	0xa1, 0x1f, 0xc2, 0xb9, 0x09, 0x08, 0x10, 0x23, //0x00003a70 .quad 2526528228819083169
	0xbe, 0x95, 0x99, 0xd9, 0x36, 0x6c, 0x37, 0x91, //0x00003a78 .quad -7982792831656159810
	0x8a, 0xa7, 0x32, 0x28, 0x0c, 0x0a, 0xd4, 0xab, //0x00003a80 .quad -6065211750830921846
	0x2d, 0xfb, 0xff, 0x8f, 0x44, 0x47, 0x85, 0xb5, //0x00003a88 .quad -5366805021142811859
	0x6c, 0x51, 0x3f, 0x32, 0x8f, 0x0c, 0xc9, 0x16, //0x00003a90 .quad 1641857348316123500
	0xf9, 0xf9, 0xff, 0xb3, 0x15, 0x99, 0xe6, 0xe2, //0x00003a98 .quad -2096820258001126919
	0xe3, 0x92, 0x67, 0x7f, 0xd9, 0xa7, 0x3d, 0xae, //0x00003aa0 .quad -5891368184943504669
	0x3b, 0xfc, 0x7f, 0x90, 0xad, 0x1f, 0xd0, 0x8d, //0x00003aa8 .quad -8228041688891786181
	0x9c, 0x77, 0x41, 0xdf, 0xcf, 0x11, 0xcd, 0x99, //0x00003ab0 .quad -7364210231179380836
	0x4a, 0xfb, 0x9f, 0xf4, 0x98, 0x27, 0x44, 0xb1, //0x00003ab8 .quad -5673366092687344822
	0x83, 0xd5, 0x11, 0xd7, 0x43, 0x56, 0x40, 0x40, //0x00003ac0 .quad 4629795266307937667
	0x1d, 0xfa, 0xc7, 0x31, 0x7f, 0x31, 0x95, 0xdd, //0x00003ac8 .quad -2480021597431793123
	0x72, 0x25, 0x6b, 0x66, 0xea, 0x35, 0x28, 0x48, //0x00003ad0 .quad 5199465050656154994
	0x52, 0xfc, 0x1c, 0x7f, 0xef, 0x3e, 0x7d, 0x8a, //0x00003ad8 .quad -8467542526035952558
	0xcf, 0xee, 0x05, 0x00, 0x65, 0x43, 0x32, 0xda, //0x00003ae0 .quad -2724040723534582065
	0x66, 0x3b, 0xe4, 0x5e, 0xab, 0x8e, 0x1c, 0xad, //0x00003ae8 .quad -5972742139117552794
	0x82, 0x6a, 0x07, 0x40, 0x3e, 0xd4, 0xbe, 0x90, //0x00003af0 .quad -8016736922845615486
	0x40, 0x4a, 0x9d, 0x36, 0x56, 0xb2, 0x63, 0xd8, //0x00003af8 .quad -2854241655469553088
	0x91, 0xa2, 0x04, 0xe8, 0xa6, 0x44, 0x77, 0x5a, //0x00003b00 .quad 6518754469289960081
	0x68, 0x4e, 0x22, 0xe2, 0x75, 0x4f, 0x3e, 0x87, //0x00003b08 .quad -8701430062309552536
	0x36, 0xcb, 0x05, 0xa2, 0xd0, 0x15, 0x15, 0x71, //0x00003b10 .quad 8148443086612450102
	0x02, 0xe2, 0xaa, 0x5a, 0x53, 0xe3, 0x0d, 0xa9, //0x00003b18 .quad -6265101559459552766
	0x03, 0x3e, 0x87, 0xca, 0x44, 0x5b, 0x5a, 0x0d, //0x00003b20 .quad 962181821410786819
	0x83, 0x9a, 0x55, 0x31, 0x28, 0x5c, 0x51, 0xd3, //0x00003b28 .quad -3219690930897053053
	0xc2, 0x86, 0x94, 0xfe, 0x0a, 0x79, 0x58, 0xe8, //0x00003b30 .quad -1704479370831952190
	0x91, 0x80, 0xd5, 0x1e, 0x99, 0xd9, 0x12, 0x84, //0x00003b38 .quad -8929835859451740015
	0x72, 0xa8, 0x39, 0xbe, 0x4d, 0x97, 0x6e, 0x62, //0x00003b40 .quad 7092772823314835570
	0xb6, 0xe0, 0x8a, 0x66, 0xff, 0x8f, 0x17, 0xa5, //0x00003b48 .quad -6550608805887287114
	0x8f, 0x12, 0xc8, 0x2d, 0x21, 0x3d, 0x0a, 0xfb, //0x00003b50 .quad -357406007711231345
	0xe3, 0x98, 0x2d, 0x40, 0xff, 0x73, 0x5d, 0xce, //0x00003b58 .quad -3576574988931720989
	0x99, 0x0b, 0x9d, 0xbc, 0x34, 0x66, 0xe6, 0x7c, //0x00003b60 .quad 8999993282035256217
	0x8e, 0x7f, 0x1c, 0x88, 0x7f, 0x68, 0xfa, 0x80, //0x00003b68 .quad -9152888395723407474
	0x80, 0x4e, 0xc4, 0xeb, 0xc1, 0xff, 0x1f, 0x1c, //0x00003b70 .quad 2026619565689294464
	0x72, 0x9f, 0x23, 0x6a, 0x9f, 0x02, 0x39, 0xa1, //0x00003b78 .quad -6829424476226871438
	0x20, 0x62, 0xb5, 0x66, 0xb2, 0xff, 0x27, 0xa3, //0x00003b80 .quad -6690097579743157728
	0x4e, 0x87, 0xac, 0x44, 0x47, 0x43, 0x87, 0xc9, //0x00003b88 .quad -3925094576856201394
	0xa8, 0xba, 0x62, 0x00, 0x9f, 0xff, 0xf1, 0x4b, //0x00003b90 .quad 5472436080603216552
	0x22, 0xa9, 0xd7, 0x15, 0x19, 0x14, 0xe9, 0xfb, //0x00003b98 .quad -294682202642863838
	0xa9, 0xb4, 0x3d, 0x60, 0xc3, 0x3f, 0x77, 0x6f, //0x00003ba0 .quad 8031958568804398249
	0xb5, 0xc9, 0xa6, 0xad, 0x8f, 0xac, 0x71, 0x9d, //0x00003ba8 .quad -7101705404292871755
	0xd3, 0x21, 0x4d, 0x38, 0xb4, 0x0f, 0x55, 0xcb, //0x00003bb0 .quad -3795109844276665901
	0x22, 0x7c, 0x10, 0x99, 0xb3, 0x17, 0xce, 0xc4, //0x00003bb8 .quad -4265445736938701790
	0x48, 0x6a, 0x60, 0x46, 0xa1, 0x53, 0x2a, 0x7e, //0x00003bc0 .quad 9091170749936331336
	0x2b, 0x9b, 0x54, 0x7f, 0xa0, 0x9d, 0x01, 0xf6, //0x00003bc8 .quad -720121152745989333
	0x6d, 0x42, 0xfc, 0xcb, 0x44, 0x74, 0xda, 0x2e, //0x00003bd0 .quad 3376138709496513133
	0xfb, 0xe0, 0x94, 0x4f, 0x84, 0x02, 0xc1, 0x99, //0x00003bd8 .quad -7367604748107325189
	0x08, 0x53, 0xfb, 0xfe, 0x55, 0x11, 0x91, 0xfa, //0x00003be0 .quad -391512631556746488
	0x39, 0x19, 0x7a, 0x63, 0x25, 0x43, 0x31, 0xc0, //0x00003be8 .quad -4597819916706768583
	0xca, 0x27, 0xba, 0x7e, 0xab, 0x55, 0x35, 0x79, //0x00003bf0 .quad 8733981247408842698
	0x88, 0x9f, 0x58, 0xbc, 0xee, 0x93, 0x3d, 0xf0, //0x00003bf8 .quad -1135588877456072824
	0xde, 0x58, 0x34, 0x2f, 0x8b, 0x55, 0xc1, 0x4b, //0x00003c00 .quad 5458738279630526686
	0xb5, 0x63, 0xb7, 0x35, 0x75, 0x7c, 0x26, 0x96, //0x00003c08 .quad -7627272076051127371
	0x16, 0x6f, 0x01, 0xfb, 0xed, 0xaa, 0xb1, 0x9e, //0x00003c10 .quad -7011635205744005354
	0xa2, 0x3c, 0x25, 0x83, 0x92, 0x1b, 0xb0, 0xbb, //0x00003c18 .quad -4922404076636521310
	0xdc, 0xca, 0xc1, 0x79, 0xa9, 0x15, 0x5e, 0x46, //0x00003c20 .quad 5070514048102157020
	0xcb, 0x8b, 0xee, 0x23, 0x77, 0x22, 0x9c, 0xea, //0x00003c28 .quad -1541319077368263733
	0xc9, 0x1e, 0x19, 0xec, 0x89, 0xcd, 0xfa, 0x0b, //0x00003c30 .quad 863228270850154185
	0x5f, 0x17, 0x75, 0x76, 0x8a, 0x95, 0xa1, 0x92, //0x00003c38 .quad -7880853450996246689
	0x7b, 0x66, 0x1f, 0x67, 0xec, 0x80, 0xf9, 0xce, //0x00003c40 .quad -3532650679864695173
	0x36, 0x5d, 0x12, 0x14, 0xed, 0xfa, 0x49, 0xb7, //0x00003c48 .quad -5239380795317920458
	0x1a, 0x40, 0xe7, 0x80, 0x27, 0xe1, 0xb7, 0x82, //0x00003c50 .quad -9027499368258256870
	0x84, 0xf4, 0x16, 0x59, 0xa8, 0x79, 0x1c, 0xe5, //0x00003c58 .quad -1937539975720012668
	0x10, 0x88, 0x90, 0xb0, 0xb8, 0xec, 0xb2, 0xd1, //0x00003c60 .quad -3336344095947716592
	0xd2, 0x58, 0xae, 0x37, 0x09, 0xcc, 0x31, 0x8f, //0x00003c68 .quad -8128491512466089774
	0x15, 0xaa, 0xb4, 0xdc, 0xe6, 0xa7, 0x1f, 0x86, //0x00003c70 .quad -8782116138362033643
	0x07, 0xef, 0x99, 0x85, 0x0b, 0x3f, 0xfe, 0xb2, //0x00003c78 .quad -5548928372155224313
	0x9a, 0xd4, 0xe1, 0x93, 0xe0, 0x91, 0xa7, 0x67, //0x00003c80 .quad 7469098900757009562
	0xc9, 0x6a, 0x00, 0x67, 0xce, 0xce, 0xbd, 0xdf, //0x00003c88 .quad -2324474446766642487
	0xe0, 0x24, 0x6d, 0x5c, 0x2c, 0xbb, 0xc8, 0xe0, //0x00003c90 .quad -2249342214667950880
	0xbd, 0x42, 0x60, 0x00, 0x41, 0xa1, 0xd6, 0x8b, //0x00003c98 .quad -8370325556870233411
	0x18, 0x6e, 0x88, 0x73, 0xf7, 0xe9, 0xfa, 0x58, //0x00003ca0 .quad 6411694268519837208
	0x6d, 0x53, 0x78, 0x40, 0x91, 0x49, 0xcc, 0xae, //0x00003ca8 .quad -5851220927660403859
	0x9e, 0x89, 0x6a, 0x50, 0x75, 0xa4, 0x39, 0xaf, //0x00003cb0 .quad -5820440219632367202
	0x48, 0x68, 0x96, 0x90, 0xf5, 0x5b, 0x7f, 0xda, //0x00003cb8 .quad -2702340141148116920
	0x03, 0x96, 0x42, 0x52, 0xc9, 0x06, 0x84, 0x6d, //0x00003cc0 .quad 7891439908798240259
	0x2d, 0x01, 0x5e, 0x7a, 0x79, 0x99, 0x8f, 0x88, //0x00003cc8 .quad -8606491615858654931
	0x83, 0x3b, 0xd3, 0xa6, 0x7b, 0x08, 0xe5, 0xc8, //0x00003cd0 .quad -3970758169284363389
	0x78, 0x81, 0xf5, 0xd8, 0xd7, 0x7f, 0xb3, 0xaa, //0x00003cd8 .quad -6146428501395930760
	0x64, 0x0a, 0x88, 0x90, 0x9a, 0x4a, 0x1e, 0xfb, //0x00003ce0 .quad -351761693178066332
	0xd6, 0xe1, 0x32, 0xcf, 0xcd, 0x5f, 0x60, 0xd5, //0x00003ce8 .quad -3071349608317525546
	0x7f, 0x06, 0x55, 0x9a, 0xa0, 0xee, 0xf2, 0x5c, //0x00003cf0 .quad 6697677969404790399
	0x26, 0xcd, 0x7f, 0xa1, 0xe0, 0x3b, 0x5c, 0x85, //0x00003cf8 .quad -8837122532839535322
	0x1e, 0x48, 0xea, 0xc0, 0x48, 0xaa, 0x2f, 0xf4, //0x00003d00 .quad -851274575098787810
	0x6f, 0xc0, 0xdf, 0xc9, 0xd8, 0x4a, 0xb3, 0xa6, //0x00003d08 .quad -6434717147622031249
	0x26, 0xda, 0x24, 0xf1, 0xda, 0x94, 0x3b, 0xf1, //0x00003d10 .quad -1064093218873484762
	0x8b, 0xb0, 0x57, 0xfc, 0x8e, 0x1d, 0x60, 0xd0, //0x00003d18 .quad -3431710416100151157
	0x58, 0x08, 0xb7, 0xd6, 0x08, 0x3d, 0xc5, 0x76, //0x00003d20 .quad 8558313775058847832
	0x57, 0xce, 0xb6, 0x5d, 0x79, 0x12, 0x3c, 0x82, //0x00003d28 .quad -9062348037703676329
	0x6e, 0xca, 0x64, 0x0c, 0x4b, 0x8c, 0x76, 0x54, //0x00003d30 .quad 6086206200396171886
	0xed, 0x81, 0x24, 0xb5, 0x17, 0x17, 0xcb, 0xa2, //0x00003d38 .quad -6716249028702207507
	0x09, 0xfd, 0x7d, 0xcf, 0x5d, 0x2f, 0x94, 0xa9, //0x00003d40 .quad -6227300304786948855
	0x68, 0xa2, 0x6d, 0xa2, 0xdd, 0xdc, 0x7d, 0xcb, //0x00003d48 .quad -3783625267450371480
	0x4c, 0x7c, 0x5d, 0x43, 0x35, 0x3b, 0xf9, 0xd3, //0x00003d50 .quad -3172439362556298164
	0x02, 0x0b, 0x09, 0x0b, 0x15, 0x54, 0x5d, 0xfe, //0x00003d58 .quad -117845565885576446
	0xaf, 0x6d, 0x1a, 0x4a, 0x01, 0xc5, 0x7b, 0xc4, //0x00003d60 .quad -4288617610811380305
	0xe1, 0xa6, 0xe5, 0x26, 0x8d, 0x54, 0xfa, 0x9e, //0x00003d68 .quad -6991182506319567135
	0x1b, 0x09, 0xa1, 0x9c, 0x41, 0xb6, 0x9a, 0x35, //0x00003d70 .quad 3862600023340550427
	0x9a, 0x10, 0x9f, 0x70, 0xb0, 0xe9, 0xb8, 0xc6, //0x00003d78 .quad -4127292114472071014
	0x62, 0x4b, 0xc9, 0x03, 0xd2, 0x63, 0x01, 0xc3, //0x00003d80 .quad -4395122007679087774
	0xc0, 0xd4, 0xc6, 0x8c, 0x1c, 0x24, 0x67, 0xf8, //0x00003d88 .quad -547429124662700864
	0x1d, 0xcf, 0x5d, 0x42, 0x63, 0xde, 0xe0, 0x79, //0x00003d90 .quad 8782263791269039901
	0xf8, 0x44, 0xfc, 0xd7, 0x91, 0x76, 0x40, 0x9b, //0x00003d98 .quad -7259672230555269896
	0xe4, 0x42, 0xf5, 0x12, 0xfc, 0x15, 0x59, 0x98, //0x00003da0 .quad -7468914334623251740
	0x36, 0x56, 0xfb, 0x4d, 0x36, 0x94, 0x10, 0xc2, //0x00003da8 .quad -4462904269766699466
	0x9d, 0x93, 0xb2, 0x17, 0x7b, 0x5b, 0x6f, 0x3e, //0x00003db0 .quad 4498915137003099037
	0xc4, 0x2b, 0x7a, 0xe1, 0x43, 0xb9, 0x94, 0xf2, //0x00003db8 .quad -966944318780986428
	0x42, 0x9c, 0xcf, 0xee, 0x2c, 0x99, 0x05, 0xa7, //0x00003dc0 .quad -6411550076227838910
	0x5a, 0x5b, 0xec, 0x6c, 0xca, 0xf3, 0x9c, 0x97, //0x00003dc8 .quad -7521869226879198374
	0x53, 0x83, 0x83, 0x2a, 0x78, 0xff, 0xc6, 0x50, //0x00003dd0 .quad 5820620459997365075
	0x31, 0x72, 0x27, 0x08, 0xbd, 0x30, 0x84, 0xbd, //0x00003dd8 .quad -4790650515171610063
	0x28, 0x64, 0x24, 0x35, 0x56, 0xbf, 0xf8, 0xa4, //0x00003de0 .quad -6559282480285457368
	0xbd, 0x4e, 0x31, 0x4a, 0xec, 0x3c, 0xe5, 0xec, //0x00003de8 .quad -1376627125537124675
	0x99, 0xbe, 0x36, 0xe1, 0x95, 0x77, 0x1b, 0x87, //0x00003df0 .quad -8711237568605798759
	0x36, 0xd1, 0x5e, 0xae, 0x13, 0x46, 0x0f, 0x94, //0x00003df8 .quad -7777920981101784778
	0x3f, 0x6e, 0x84, 0x59, 0x7b, 0x55, 0xe2, 0x28, //0x00003e00 .quad 2946011094524915263
	0x84, 0x85, 0xf6, 0x99, 0x98, 0x17, 0x13, 0xb9, //0x00003e08 .quad -5110715207949843068
	0xcf, 0x89, 0xe5, 0x2f, 0xda, 0xea, 0x1a, 0x33, //0x00003e10 .quad 3682513868156144079
	0xe5, 0x26, 0x74, 0xc0, 0x7e, 0xdd, 0x57, 0xe7, //0x00003e18 .quad -1776707991509915931
	0x21, 0x76, 0xef, 0x5d, 0xc8, 0xd2, 0xf0, 0x3f, //0x00003e20 .quad 4607414176811284001
	0x4f, 0x98, 0x48, 0x38, 0x6f, 0xea, 0x96, 0x90, //0x00003e28 .quad -8027971522334779313
	0xa9, 0x53, 0x6b, 0x75, 0x7a, 0x07, 0xed, 0x0f, //0x00003e30 .quad 1147581702586717097
	0x63, 0xbe, 0x5a, 0x06, 0x0b, 0xa5, 0xbc, 0xb4, //0x00003e38 .quad -5423278384491086237
	0x94, 0x28, 0xc6, 0x12, 0x59, 0x49, 0xe8, 0xd3, //0x00003e40 .quad -3177208890193991532
	0xfb, 0x6d, 0xf1, 0xc7, 0x4d, 0xce, 0xeb, 0xe1, //0x00003e48 .quad -2167411962186469893
	0x5c, 0xd9, 0xbb, 0xab, 0xd7, 0x2d, 0x71, 0x64, //0x00003e50 .quad 7237616480483531100
	0xbd, 0xe4, 0xf6, 0x9c, 0xf0, 0x60, 0x33, 0x8d, //0x00003e58 .quad -8272161504007625539
	0xb3, 0xcf, 0xaa, 0x96, 0x4d, 0x79, 0x8d, 0xbd, //0x00003e60 .quad -4788037454677749837
	0xec, 0x9d, 0x34, 0xc4, 0x2c, 0x39, 0x80, 0xb0, //0x00003e68 .quad -5728515861582144020
	0xa0, 0x83, 0x55, 0xfc, 0xa0, 0xd7, 0xf0, 0xec, //0x00003e70 .quad -1373360799919799392
	0x67, 0xc5, 0x41, 0xf5, 0x77, 0x47, 0xa0, 0xdc, //0x00003e78 .quad -2548958808550292121
	0x44, 0x72, 0xb5, 0x9d, 0xc4, 0x86, 0x16, 0xf4, //0x00003e80 .quad -858350499949874620
	0x60, 0x1b, 0x49, 0xf9, 0xaa, 0x2c, 0xe4, 0x89, //0x00003e88 .quad -8510628282985014432
	0xd5, 0xce, 0x22, 0xc5, 0x75, 0x28, 0x1c, 0x31, //0x00003e90 .quad 3538747893490044629
	0x39, 0x62, 0x9b, 0xb7, 0xd5, 0x37, 0x5d, 0xac, //0x00003e98 .quad -6026599335303880135
	0x8b, 0x82, 0x6b, 0x36, 0x93, 0x32, 0x63, 0x7d, //0x00003ea0 .quad 9035120885289943691
	0xc7, 0x3a, 0x82, 0x25, 0xcb, 0x85, 0x74, 0xd7, //0x00003ea8 .quad -2921563150702462265
	0x97, 0x31, 0x03, 0x02, 0x9c, 0xff, 0x5d, 0xae, //0x00003eb0 .quad -5882264492762254953
	0xbc, 0x64, 0x71, 0xf7, 0x9e, 0xd3, 0xa8, 0x86, //0x00003eb8 .quad -8743505996830120772
	0xfc, 0xfd, 0x83, 0x02, 0x83, 0x7f, 0xf5, 0xd9, //0x00003ec0 .quad -2741144597525430788
	0xeb, 0xbd, 0x4d, 0xb5, 0x86, 0x08, 0x53, 0xa8, //0x00003ec8 .quad -6317696477610263061
	0x7b, 0xfd, 0x24, 0xc3, 0x63, 0xdf, 0x72, 0xd0, //0x00003ed0 .quad -3426430746906788485
	0x66, 0x2d, 0xa1, 0x62, 0xa8, 0xca, 0x67, 0xd2, //0x00003ed8 .quad -3285434578585440922
	0x6d, 0x1e, 0xf7, 0x59, 0x9e, 0xcb, 0x47, 0x42, //0x00003ee0 .quad 4776009810824339053
	0x60, 0xbc, 0xa4, 0x3d, 0xa9, 0xde, 0x80, 0x83, //0x00003ee8 .quad -8970925639256982432
	0x08, 0xe6, 0x74, 0xf0, 0x85, 0xbe, 0xd9, 0x52, //0x00003ef0 .quad 5970012263530423816
	0x78, 0xeb, 0x0d, 0x8d, 0x53, 0x16, 0x61, 0xa4, //0x00003ef8 .quad -6601971030643840136
	0x8b, 0x1f, 0x92, 0x6c, 0x27, 0x2e, 0x90, 0x67, //0x00003f00 .quad 7462515329413029771
	0x56, 0x66, 0x51, 0x70, 0xe8, 0x5b, 0x79, 0xcd, //0x00003f08 .quad -3640777769877412266
	0xb6, 0x53, 0xdb, 0xa3, 0xd8, 0x1c, 0xba, 0x00, //0x00003f10 .quad 52386062455755702
	0xf6, 0xdf, 0x32, 0x46, 0x71, 0xd9, 0x6b, 0x80, //0x00003f18 .quad -9193015133814464522
	0xa4, 0x28, 0xd2, 0xcc, 0x0e, 0xa4, 0xe8, 0x80, //0x00003f20 .quad -9157889458785081180
	0xf3, 0x97, 0xbf, 0x97, 0xcd, 0xcf, 0x86, 0xa0, //0x00003f28 .quad -6879582898840692749
	0xcd, 0xb2, 0x06, 0x80, 0x12, 0xcd, 0x22, 0x61, //0x00003f30 .quad 6999382250228200141
	0xf0, 0x7d, 0xaf, 0xfd, 0xc0, 0x83, 0xa8, 0xc8, //0x00003f38 .quad -3987792605123478032
	0x81, 0x5f, 0x08, 0x20, 0x57, 0x80, 0x6b, 0x79, //0x00003f40 .quad 8749227812785250177
	0x6c, 0x5d, 0x1b, 0x3d, 0xb1, 0xa4, 0xd2, 0xfa, //0x00003f48 .quad -373054737976959636
	0xb0, 0x3b, 0x05, 0x74, 0x36, 0x30, 0xe3, 0xcb, //0x00003f50 .quad -3755104653863994448
	0x63, 0x1a, 0x31, 0xc6, 0xee, 0xa6, 0xc3, 0x9c, //0x00003f58 .quad -7150688238876681629
	0x9c, 0x8a, 0x06, 0x11, 0x44, 0xfc, 0xdb, 0xbe, //0x00003f60 .quad -4693880817329993060
	0xfc, 0x60, 0xbd, 0x77, 0xaa, 0x90, 0xf4, 0xc3, //0x00003f68 .quad -4326674280168464132
	0x44, 0x2d, 0x48, 0x15, 0x55, 0xfb, 0x92, 0xee, //0x00003f70 .quad -1255665003235103420
	0x3b, 0xb9, 0xac, 0x15, 0xd5, 0xb4, 0xf1, 0xf4, //0x00003f78 .quad -796656831783192261
	0x4a, 0x1c, 0x4d, 0x2d, 0x15, 0xdd, 0x1b, 0x75, //0x00003f80 .quad 8438581409832836170
	0xc5, 0xf3, 0x8b, 0x2d, 0x05, 0x11, 0x17, 0x99, //0x00003f88 .quad -7415439547505577019
	0x5d, 0x63, 0xa0, 0x78, 0x5a, 0xd4, 0x62, 0xd2, //0x00003f90 .quad -3286831292991118499
	0xb6, 0xf0, 0xee, 0x78, 0x46, 0xd5, 0x5c, 0xbf, //0x00003f98 .quad -4657613415954583370
	0x34, 0x7c, 0xc8, 0x16, 0x71, 0x89, 0xfb, 0x86, //0x00003fa0 .quad -8720225134666286028
	0xe4, 0xac, 0x2a, 0x17, 0x98, 0x0a, 0x34, 0xef, //0x00003fa8 .quad -1210330751515841308
	0xa0, 0x4d, 0x3d, 0xae, 0xe6, 0x35, 0x5d, 0xd4, //0x00003fb0 .quad -3144297699952734816
	0x0e, 0xac, 0x7a, 0x0e, 0x9f, 0x86, 0x80, 0x95, //0x00003fb8 .quad -7673985747338482674
	0x09, 0xa1, 0xcc, 0x59, 0x60, 0x83, 0x74, 0x89, //0x00003fc0 .quad -8542058143368306423
	0x12, 0x57, 0x19, 0xd2, 0x46, 0xa8, 0xe0, 0xba, //0x00003fc8 .quad -4980796165745715438
	0x4b, 0xc9, 0x3f, 0x70, 0x38, 0xa4, 0xd1, 0x2b, //0x00003fd0 .quad 3157485376071780683
	0xd7, 0xac, 0x9f, 0x86, 0x58, 0xd2, 0x98, 0xe9, //0x00003fd8 .quad -1614309188754756393
	0xcf, 0xdd, 0x27, 0x46, 0xa3, 0x06, 0x63, 0x7b, //0x00003fe0 .quad 8890957387685944783
	0x06, 0xcc, 0x23, 0x54, 0x77, 0x83, 0xff, 0x91, //0x00003fe8 .quad -7926472270612804602
	0x42, 0xd5, 0xb1, 0x17, 0x4c, 0xc8, 0x3b, 0x1a, //0x00003ff0 .quad 1890324697752655170
	0x08, 0xbf, 0x2c, 0x29, 0x55, 0x64, 0x7f, 0xb6, //0x00003ff8 .quad -5296404319838617848
	0x93, 0x4a, 0x9e, 0x1d, 0x5f, 0xba, 0xca, 0x20, //0x00004000 .quad 2362905872190818963
	0xca, 0xee, 0x77, 0x73, 0x6a, 0x3d, 0x1f, 0xe4, //0x00004008 .quad -2008819381370884406
	0x9c, 0xee, 0x82, 0x72, 0x7b, 0xb4, 0x7e, 0x54, //0x00004010 .quad 6088502188546649756
	0x3e, 0xf5, 0x2a, 0x88, 0x62, 0x86, 0x93, 0x8e, //0x00004018 .quad -8173041140997884610
	0x43, 0xaa, 0x23, 0x4f, 0x9a, 0x61, 0x9e, 0xe9, //0x00004020 .quad -1612744301171463613
	0x8d, 0xb2, 0x35, 0x2a, 0xfb, 0x67, 0x38, 0xb2, //0x00004028 .quad -5604615407819967859
	0xd4, 0x94, 0xec, 0xe2, 0x00, 0xfa, 0x05, 0x64, //0x00004030 .quad 7207441660390446292
	0x31, 0x1f, 0xc3, 0xf4, 0xf9, 0x81, 0xc6, 0xde, //0x00004038 .quad -2394083241347571919
	0x04, 0xdd, 0xd3, 0x8d, 0x40, 0xbc, 0x83, 0xde, //0x00004040 .quad -2412877989897052924
	0x7e, 0xf3, 0xf9, 0x38, 0x3c, 0x11, 0x3c, 0x8b, //0x00004048 .quad -8413831053483314306
	0x45, 0xd4, 0x48, 0xb1, 0x50, 0xab, 0x24, 0x96, //0x00004050 .quad -7627783505798704059
	0x5e, 0x70, 0x38, 0x47, 0x8b, 0x15, 0x0b, 0xae, //0x00004058 .quad -5905602798426754978
	0x57, 0x09, 0x9b, 0xdd, 0x24, 0xd6, 0xad, 0x3b, //0x00004060 .quad 4300328673033783639
	0x76, 0x8c, 0x06, 0x19, 0xee, 0xda, 0x8d, 0xd9, //0x00004068 .quad -2770317479606055818
	0xd6, 0xe5, 0x80, 0x0a, 0xd7, 0xa5, 0x4c, 0xe5, //0x00004070 .quad -1923980597781273130
	0xc9, 0x17, 0xa4, 0xcf, 0xd4, 0xa8, 0xf8, 0x87, //0x00004078 .quad -8648977452394866743
	0x4c, 0x1f, 0x21, 0xcd, 0x4c, 0xcf, 0x9f, 0x5e, //0x00004080 .quad 6818396289628184396
	0xbc, 0x1d, 0x8d, 0x03, 0x0a, 0xd3, 0xf6, 0xa9, //0x00004088 .quad -6199535797066195524
	0x1f, 0x67, 0x69, 0x00, 0x20, 0xc3, 0x47, 0x76, //0x00004090 .quad 8522995362035230495
	0x2b, 0x65, 0x70, 0x84, 0xcc, 0x87, 0x74, 0xd4, //0x00004098 .quad -3137733727905356501
	0x73, 0xe0, 0x41, 0x00, 0xf4, 0xd9, 0xec, 0x29, //0x000040a0 .quad 3021029092058325107
	0x3b, 0x3f, 0xc6, 0xd2, 0xdf, 0xd4, 0xc8, 0x84, //0x000040a8 .quad -8878612607581929669
	0x90, 0x58, 0x52, 0x00, 0x71, 0x10, 0x68, 0xf4, //0x000040b0 .quad -835399653354481520
	0x09, 0xcf, 0x77, 0xc7, 0x17, 0x0a, 0xfb, 0xa5, //0x000040b8 .quad -6486579741050024183
	0xb4, 0xee, 0x66, 0x40, 0x8d, 0x14, 0x82, 0x71, //0x000040c0 .quad 8179122470161673908
	0xcc, 0xc2, 0x55, 0xb9, 0x9d, 0xcc, 0x79, 0xcf, //0x000040c8 .quad -3496538657885142324
	0x30, 0x55, 0x40, 0x48, 0xd8, 0x4c, 0xf1, 0xc6, //0x000040d0 .quad -4111420493003729616
	0xbf, 0x99, 0xd5, 0x93, 0xe2, 0x1f, 0xac, 0x81, //0x000040d8 .quad -9102865688819295809
	0x7c, 0x6a, 0x50, 0x5a, 0x0e, 0xa0, 0xad, 0xb8, //0x000040e0 .quad -5139275616254662020
	0x2f, 0x00, 0xcb, 0x38, 0xdb, 0x27, 0x17, 0xa2, //0x000040e8 .quad -6766896092596731857
	0x1c, 0x85, 0xe4, 0xf0, 0x11, 0x08, 0xd9, 0xa6, //0x000040f0 .quad -6424094520318327524
	0x3b, 0xc0, 0xfd, 0x06, 0xd2, 0xf1, 0x9c, 0xca, //0x000040f8 .quad -3846934097318526917
	0x63, 0xa6, 0x1d, 0x6d, 0x16, 0x4a, 0x8f, 0x90, //0x00004100 .quad -8030118150397909405
	0x4a, 0x30, 0xbd, 0x88, 0x46, 0x2e, 0x44, 0xfd, //0x00004108 .quad -196981603220770742
	0xfe, 0x87, 0x32, 0x04, 0x4e, 0x8e, 0x59, 0x9a, //0x00004110 .quad -7324666853212387330
	0x2e, 0x3e, 0x76, 0x15, 0xec, 0x9c, 0x4a, 0x9e, //0x00004118 .quad -7040642529654063570
	0xfd, 0x29, 0x3f, 0x85, 0xe1, 0xf1, 0xef, 0x40, //0x00004120 .quad 4679224488766679549
	0xba, 0xcd, 0xd3, 0x1a, 0x27, 0x44, 0xdd, 0xc5, //0x00004128 .quad -4189117143640191558
	0x7c, 0xf4, 0x8e, 0xe6, 0x59, 0xee, 0x2b, 0xd1, //0x00004130 .quad -3374341425896426372
	0x28, 0xc1, 0x88, 0xe1, 0x30, 0x95, 0x54, 0xf7, //0x00004138 .quad -624710411122851544
	0xce, 0x58, 0x19, 0x30, 0xf8, 0x74, 0xbb, 0x82, //0x00004140 .quad -9026492418826348338
	0xb9, 0x78, 0xf5, 0x8c, 0x3e, 0xdd, 0x94, 0x9a, //0x00004148 .quad -7307973034592864071
	0x01, 0xaf, 0x1f, 0x3c, 0x36, 0x52, 0x6a, 0xe3, //0x00004150 .quad -2059743486678159615
	0xe7, 0xd6, 0x32, 0x30, 0x8e, 0x14, 0x3a, 0xc1, //0x00004158 .quad -4523280274813692185
	0xc1, 0x9a, 0x27, 0xcb, 0xc3, 0xe6, 0x44, 0xdc, //0x00004160 .quad -2574679358347699519
	0xa1, 0x8c, 0x3f, 0xbc, 0xb1, 0x99, 0x88, 0xf1, //0x00004168 .quad -1042414325089727327
	0xb9, 0xc0, 0xf8, 0x5e, 0x3a, 0x10, 0xab, 0x29, //0x00004170 .quad 3002511419460075705
	0xe5, 0xb7, 0xa7, 0x15, 0x0f, 0x60, 0xf5, 0x96, //0x00004178 .quad -7569037980822161435
	0xe7, 0xf0, 0xb6, 0xf6, 0x48, 0xd4, 0x15, 0x74, //0x00004180 .quad 8364825292752482535
	0xde, 0xa5, 0x11, 0xdb, 0x12, 0xb8, 0xb2, 0xbc, //0x00004188 .quad -4849611457600313890
	0x21, 0xad, 0x64, 0x34, 0x5b, 0x49, 0x1b, 0x11, //0x00004190 .quad 1232659579085827361
	0x56, 0x0f, 0xd6, 0x91, 0x17, 0x66, 0xdf, 0xeb, //0x00004198 .quad -1450328303573004458
	0x34, 0xec, 0xbe, 0x00, 0xd9, 0x0d, 0xb1, 0xca, //0x000041a0 .quad -3841273781498745804
	0x95, 0xc9, 0x25, 0xbb, 0xce, 0x9f, 0x6b, 0x93, //0x000041a8 .quad -7823984217374209643
	0x42, 0xa7, 0xee, 0x40, 0x4f, 0x51, 0x5d, 0x3d, //0x000041b0 .quad 4421779809981343554
	0xfb, 0x3b, 0xef, 0x69, 0xc2, 0x87, 0x46, 0xb8, //0x000041b8 .quad -5168294253290374149
	0x12, 0x51, 0x2a, 0x11, 0xa3, 0xa5, 0xb4, 0x0c, //0x000041c0 .quad 915538744049291538
	0xfa, 0x0a, 0x6b, 0x04, 0xb3, 0x29, 0x58, 0xe6, //0x000041c8 .quad -1848681798185579782
	0xab, 0x72, 0xba, 0xea, 0x85, 0xe7, 0xf0, 0x47, //0x000041d0 .quad 5183897733458195115
	0xdc, 0xe6, 0xc2, 0xe2, 0x0f, 0x1a, 0xf7, 0x8f, //0x000041d8 .quad -8072955151507069220
	0x56, 0x0f, 0x69, 0x65, 0x67, 0x21, 0xed, 0x59, //0x000041e0 .quad 6479872166822743894
	0x93, 0xa0, 0x73, 0xdb, 0x93, 0xe0, 0xf4, 0xb3, //0x000041e8 .quad -5479507920956448621
	0x2c, 0x53, 0xc3, 0x3e, 0xc1, 0x69, 0x68, 0x30, //0x000041f0 .quad 3488154190101041964
	0xb8, 0x88, 0x50, 0xd2, 0xb8, 0x18, 0xf2, 0xe0, //0x000041f8 .quad -2237698882768172872
	0xfb, 0x13, 0x3a, 0xc7, 0x18, 0x42, 0x41, 0x1e, //0x00004200 .quad 2180096368813151227
	0x73, 0x55, 0x72, 0x83, 0x73, 0x4f, 0x97, 0x8c, //0x00004208 .quad -8316090829371189901
	0xfa, 0x98, 0x08, 0xf9, 0x9e, 0x92, 0xd1, 0xe5, //0x00004210 .quad -1886565557410948870
	0xcf, 0xea, 0x4e, 0x64, 0x50, 0x23, 0xbd, 0xaf, //0x00004218 .quad -5783427518286599473
	0x39, 0xbf, 0x4a, 0xb7, 0x46, 0xf7, 0x45, 0xdf, //0x00004220 .quad -2358206946763686087
	0x83, 0xa5, 0x62, 0x7d, 0x24, 0x6c, 0xac, 0xdb, //0x00004228 .quad -2617598379430861437
	0x83, 0xb7, 0x8e, 0x32, 0x8c, 0xba, 0x8b, 0x6b, //0x00004230 .quad 7749492695127472003
	0x72, 0xa7, 0x5d, 0xce, 0x96, 0xc3, 0x4b, 0x89, //0x00004238 .quad -8553528014785370254
	0x64, 0x65, 0x32, 0x3f, 0x2f, 0xa9, 0x6e, 0x06, //0x00004240 .quad 463493832054564196
	0x4f, 0x11, 0xf5, 0x81, 0x7c, 0xb4, 0x9e, 0xab, //0x00004248 .quad -6080224000054324913
	0xbd, 0xfe, 0xfe, 0x0e, 0x7b, 0x53, 0x0a, 0xc8, //0x00004250 .quad -4032318728359182659
	0xa2, 0x55, 0x72, 0xa2, 0x9b, 0x61, 0x86, 0xd6, //0x00004258 .quad -2988593981640518238
	0x36, 0x5f, 0x5f, 0xe9, 0x2c, 0x74, 0x06, 0xbd, //0x00004260 .quad -4826042214438183114
	0x85, 0x75, 0x87, 0x45, 0x01, 0xfd, 0x13, 0x86, //0x00004268 .quad -8785400266166405755
	0x04, 0x37, 0xb7, 0x23, 0x38, 0x11, 0x48, 0x2c, //0x00004270 .quad 3190819268807046916
	0xe7, 0x52, 0xe9, 0x96, 0x41, 0xfc, 0x98, 0xa7, //0x00004278 .quad -6370064314280619289
	0xc5, 0x04, 0xa5, 0x2c, 0x86, 0x15, 0x5a, 0xf7, //0x00004280 .quad -623161932418579259
	0xa0, 0xa7, 0xa3, 0xfc, 0x51, 0x3b, 0x7f, 0xd1, //0x00004288 .quad -3350894374423386208
	0xfb, 0x22, 0xe7, 0xdb, 0x73, 0x4d, 0x98, 0x9a, //0x00004290 .quad -7307005235402693893
	0xc4, 0x48, 0xe6, 0x3d, 0x13, 0x85, 0xef, 0x82, //0x00004298 .quad -9011838011655698236
	0xba, 0xeb, 0xe0, 0xd2, 0xd0, 0x60, 0x3e, 0xc1, //0x000042a0 .quad -4522070525825979462
	0xf5, 0xda, 0x5f, 0x0d, 0x58, 0x66, 0xab, 0xa3, //0x000042a8 .quad -6653111496142234891
	0xa8, 0x26, 0x99, 0x07, 0x05, 0xf9, 0x8d, 0x31, //0x000042b0 .quad 3570783879572301480
	0xb3, 0xd1, 0xb7, 0x10, 0xee, 0x3f, 0x96, 0xcc, //0x000042b8 .quad -3704703351750405709
	0x52, 0x70, 0x7f, 0x49, 0x46, 0x77, 0xf1, 0xfd, //0x000042c0 .quad -148206168962011054
	0x1f, 0xc6, 0xe5, 0x94, 0xe9, 0xcf, 0xbb, 0xff, //0x000042c8 .quad -19193171260619233
	0x33, 0xa6, 0xef, 0xed, 0x8b, 0xea, 0xb6, 0xfe, //0x000042d0 .quad -92628855601256909
	0xd3, 0x9b, 0x0f, 0xfd, 0xf1, 0x61, 0xd5, 0x9f, //0x000042d8 .quad -6929524759678968877
	0xc0, 0x8f, 0x6b, 0xe9, 0x2e, 0xa5, 0x64, 0xfe, //0x000042e0 .quad -115786069501571136
	0xc8, 0x82, 0x53, 0x7c, 0x6e, 0xba, 0xca, 0xc7, //0x000042e8 .quad -4050219931171323192
	0xb0, 0x73, 0xc6, 0xa3, 0x7a, 0xce, 0xfd, 0x3d, //0x000042f0 .quad 4466953431550423984
	0x7b, 0x63, 0x68, 0x1b, 0x0a, 0x69, 0xbd, 0xf9, //0x000042f8 .quad -451088895536766085
	0x4e, 0x08, 0x5c, 0xa6, 0x0c, 0xa1, 0xbe, 0x06, //0x00004300 .quad 486002885505321038
	0x2d, 0x3e, 0x21, 0x51, 0xa6, 0x61, 0x16, 0x9c, //0x00004308 .quad -7199459587351560659
	0x62, 0x0a, 0xf3, 0xcf, 0x4f, 0x49, 0x6e, 0x48, //0x00004310 .quad 5219189625309039202
	0xb8, 0x8d, 0x69, 0xe5, 0x0f, 0xfa, 0x1b, 0xc3, //0x00004318 .quad -4387638465762062920
	0xfa, 0xcc, 0xef, 0xc3, 0xa3, 0xdb, 0x89, 0x5a, //0x00004320 .quad 6523987031636299002
	0x26, 0xf1, 0xc3, 0xde, 0x93, 0xf8, 0xe2, 0xf3, //0x00004328 .quad -872862063775190746
	0x1c, 0xe0, 0x75, 0x5a, 0x46, 0x29, 0x96, 0xf8, //0x00004330 .quad -534194123654701028
	0xb7, 0x76, 0x3a, 0x6b, 0x5c, 0xdb, 0x6d, 0x98, //0x00004338 .quad -7463067817500576073
	0x23, 0x58, 0x13, 0xf1, 0x97, 0xb3, 0xbb, 0xf6, //0x00004340 .quad -667742654568376285
	0x65, 0x14, 0x09, 0x86, 0x33, 0x52, 0x89, 0xbe, //0x00004348 .quad -4717148753448332187
	0x2c, 0x2e, 0x58, 0xed, 0x7d, 0xa0, 0x6a, 0x74, //0x00004350 .quad 8388693718644305452
	0x7f, 0x59, 0x8b, 0x67, 0xc0, 0xa6, 0x2b, 0xee, //0x00004358 .quad -1284749923383027329
	0xdc, 0x1c, 0x57, 0xb4, 0x4e, 0xa4, 0xc2, 0xa8, //0x00004360 .quad -6286281471915778852
	0xef, 0x17, 0xb7, 0x40, 0x38, 0x48, 0xdb, 0x94, //0x00004368 .quad -7720497729755473937
	0x13, 0xe4, 0x6c, 0x61, 0x62, 0x4d, 0xf3, 0x92, //0x00004370 .quad -7857851839894723565
	0xeb, 0xdd, 0xe4, 0x50, 0x46, 0x1a, 0x12, 0xba, //0x00004378 .quad -5038936143766954517
	0x17, 0x1d, 0xc8, 0xf9, 0xba, 0x20, 0xb0, 0x77, //0x00004380 .quad 8624429273841147159
	0x66, 0x15, 0x1e, 0xe5, 0xd7, 0xa0, 0x96, 0xe8, //0x00004388 .quad -1686984161281305242
	0x2e, 0x12, 0x1d, 0xdc, 0x74, 0x14, 0xce, 0x0a, //0x00004390 .quad 778582277723329070
	0x60, 0xcd, 0x32, 0xef, 0x86, 0x24, 0x5e, 0x91, //0x00004398 .quad -7971894128441897632
	0xba, 0x56, 0x24, 0x13, 0x92, 0x99, 0x81, 0x0d, //0x000043a0 .quad 973227847154161338
	0xb8, 0x80, 0xff, 0xaa, 0xa8, 0xad, 0xb5, 0xb5, //0x000043a8 .quad -5353181642124984136
	0x69, 0x6c, 0xed, 0x97, 0xf6, 0xff, 0xe1, 0x10, //0x000043b0 .quad 1216534808942701673
	0xe6, 0x60, 0xbf, 0xd5, 0x12, 0x19, 0x23, 0xe3, //0x000043b8 .quad -2079791034228842266
	0xc1, 0x63, 0xf4, 0x1e, 0xfa, 0x3f, 0x8d, 0xca, //0x000043c0 .quad -3851351762838199359
	0x8f, 0x9c, 0x97, 0xc5, 0xab, 0xef, 0xf5, 0x8d, //0x000043c8 .quad -8217398424034108273
	0xb2, 0x7c, 0xb1, 0xa6, 0xf8, 0x8f, 0x30, 0xbd, //0x000043d0 .quad -4814189703547749198
	0xb3, 0x83, 0xfd, 0xb6, 0x96, 0x6b, 0x73, 0xb1, //0x000043d8 .quad -5660062011615247437
	0xde, 0xdb, 0x5d, 0xd0, 0xf6, 0xb3, 0x7c, 0xac, //0x000043e0 .quad -6017737129434686498
	0xa0, 0xe4, 0xbc, 0x64, 0x7c, 0x46, 0xd0, 0xdd, //0x000043e8 .quad -2463391496091671392
	0x6b, 0xa9, 0x3a, 0x42, 0x7a, 0xf0, 0xcd, 0x6b, //0x000043f0 .quad 7768129340171790699
	0xe4, 0x0e, 0xf6, 0xbe, 0x0d, 0x2c, 0xa2, 0x8a, //0x000043f8 .quad -8457148712698376476
	0xc6, 0x53, 0xc9, 0xd2, 0x98, 0x6c, 0xc1, 0x86, //0x00004400 .quad -8736582398494813242
	0x9d, 0x92, 0xb3, 0x2e, 0x11, 0xb7, 0x4a, 0xad, //0x00004408 .quad -5959749872445582691
	0xb7, 0xa8, 0x7b, 0x07, 0xbf, 0xc7, 0x71, 0xe8, //0x00004410 .quad -1697355961263740745
	0x44, 0x77, 0x60, 0x7a, 0xd5, 0x64, 0x9d, 0xd8, //0x00004418 .quad -2838001322129590460
	0x72, 0x49, 0xad, 0x64, 0xd7, 0x1c, 0x47, 0x11, //0x00004420 .quad 1244995533423855986
	0x8b, 0x4a, 0x7c, 0x6c, 0x05, 0x5f, 0x62, 0x87, //0x00004428 .quad -8691279853972075893
	0xcf, 0x9b, 0xd8, 0x3d, 0x0d, 0xe4, 0x98, 0xd5, //0x00004430 .quad -3055441601647567921
	0x2d, 0x5d, 0x9b, 0xc7, 0xc6, 0xf6, 0x3a, 0xa9, //0x00004438 .quad -6252413799037706963
	0xc3, 0xc2, 0x4e, 0x8d, 0x10, 0x1d, 0xff, 0x4a, //0x00004440 .quad 5404070034795315907
	0x79, 0x34, 0x82, 0x79, 0x78, 0xb4, 0x89, 0xd3, //0x00004448 .quad -3203831230369745799
	0xba, 0x39, 0x51, 0x58, 0x2a, 0x72, 0xdf, 0xce, //0x00004450 .quad -3539985255894009414
	0xcb, 0x60, 0xf1, 0x4b, 0xcb, 0x10, 0x36, 0x84, //0x00004458 .quad -8919923546622172981
	0x28, 0x88, 0x65, 0xee, 0xb4, 0x4e, 0x97, 0xc2, //0x00004460 .quad -4424981569867511768
	0xfe, 0xb8, 0xed, 0x1e, 0xfe, 0x94, 0x43, 0xa5, //0x00004468 .quad -6538218414850328322
	0x32, 0xea, 0xfe, 0x29, 0x62, 0x22, 0x3d, 0x73, //0x00004470 .quad 8303831092947774002
	0x3e, 0x27, 0xa9, 0xa6, 0x3d, 0x7a, 0x94, 0xce, //0x00004478 .quad -3561087000135522498
	0x5f, 0x52, 0x3f, 0x5a, 0x7d, 0x35, 0x06, 0x08, //0x00004480 .quad 578208414664970847
	0x87, 0xb8, 0x29, 0x88, 0x66, 0xcc, 0x1c, 0x81, //0x00004488 .quad -9143208402725783417
	0xf7, 0x26, 0xcf, 0xb0, 0xdc, 0xc2, 0x07, 0xca, //0x00004490 .quad -3888925500096174345
	0xa8, 0x26, 0x34, 0x2a, 0x80, 0xff, 0x63, 0xa1, //0x00004498 .quad -6817324484979841368
	0xb5, 0xf0, 0x02, 0xdd, 0x93, 0xb3, 0x89, 0xfc, //0x000044a0 .quad -249470856692830027
	0x52, 0x30, 0xc1, 0x34, 0x60, 0xff, 0xbc, 0xc9, //0x000044a8 .quad -3909969587797413806
	0xe2, 0xac, 0x43, 0xd4, 0x78, 0x20, 0xac, 0xbb, //0x000044b0 .quad -4923524589293425438
	0x67, 0x7c, 0xf1, 0x41, 0x38, 0x3f, 0x2c, 0xfc, //0x000044b8 .quad -275775966319379353
	0x0d, 0x4c, 0xaa, 0x84, 0x4b, 0x94, 0x4b, 0xd5, //0x000044c0 .quad -3077202868308390899
	0xc0, 0xed, 0x36, 0x29, 0x83, 0xa7, 0x9b, 0x9d, //0x000044c8 .quad -7089889006590693952
	0x11, 0xdf, 0xd4, 0x65, 0x5e, 0x79, 0x9e, 0x0a, //0x000044d0 .quad 765182433041899281
	0x31, 0xa9, 0x84, 0xf3, 0x63, 0x91, 0x02, 0xc5, //0x000044d8 .quad -4250675239810979535
	0xd5, 0x16, 0x4a, 0xff, 0xb5, 0x17, 0x46, 0x4d, //0x000044e0 .quad 5568164059729762005
	0x7d, 0xd3, 0x65, 0xf0, 0xbc, 0x35, 0x43, 0xf6, //0x000044e8 .quad -701658031336336515
	0x45, 0x4e, 0x8e, 0xbf, 0xd1, 0xce, 0x4b, 0x50, //0x000044f0 .quad 5785945546544795205
	0x2e, 0xa4, 0x3f, 0x16, 0x96, 0x01, 0xea, 0x99, //0x000044f8 .quad -7356065297226292178
	0xd6, 0xe1, 0x71, 0x2f, 0x86, 0xc2, 0x5e, 0xe4, //0x00004500 .quad -1990940103673781802
	0x39, 0x8d, 0xcf, 0x9b, 0xfb, 0x81, 0x64, 0xc0, //0x00004508 .quad -4583395603105477319
	0x4c, 0x5a, 0x4e, 0xbb, 0x27, 0x73, 0x76, 0x5d, //0x00004510 .quad 6734696907262548556
	0x88, 0x70, 0xc3, 0x82, 0x7a, 0xa2, 0x7d, 0xf0, //0x00004518 .quad -1117558485454458744
	0x6f, 0xf8, 0x10, 0xd5, 0xf8, 0x07, 0x6a, 0x3a, //0x00004520 .quad 4209185567039092847
	0x55, 0x26, 0xba, 0x91, 0x8c, 0x85, 0x4e, 0x96, //0x00004528 .quad -7616003081050118571
	0x8b, 0x36, 0x55, 0x0a, 0xf7, 0x89, 0x04, 0x89, //0x00004530 .quad -8573576096483297653
	0xea, 0xaf, 0x28, 0xb6, 0xef, 0x26, 0xe2, 0xbb, //0x00004538 .quad -4908317832885260310
	0x2e, 0x84, 0xea, 0xcc, 0x74, 0xac, 0x45, 0x2b, //0x00004540 .quad 3118087934678041646
	0xe5, 0xdb, 0xb2, 0xa3, 0xab, 0xb0, 0xda, 0xea, //0x00004548 .quad -1523711272679187483
	0x9d, 0x92, 0x12, 0x00, 0xc9, 0x8b, 0x0b, 0x3b, //0x00004550 .quad 4254647968387469981
	0x6f, 0xc9, 0x4f, 0x46, 0x6b, 0xae, 0xc8, 0x92, //0x00004558 .quad -7869848573065574033
	0x44, 0x37, 0x17, 0x40, 0xbb, 0x6e, 0xce, 0x09, //0x00004560 .quad 706623942056949572
	0xcb, 0xbb, 0xe3, 0x17, 0x06, 0xda, 0x7a, 0xb7, //0x00004568 .quad -5225624697904579637
	0x15, 0x05, 0x1d, 0x10, 0x6a, 0x0a, 0x42, 0xcc, //0x00004570 .quad -3728406090856200939
	0xbd, 0xaa, 0xdc, 0x9d, 0x87, 0x90, 0x59, 0xe5, //0x00004578 .quad -1920344853953336643
	0x2d, 0x23, 0x12, 0x4a, 0x82, 0x46, 0xa9, 0x9f, //0x00004580 .quad -6941939825212513491
	0xb6, 0xea, 0xa9, 0xc2, 0x54, 0xfa, 0x57, 0x8f, //0x00004588 .quad -8117744561361917258
	0xf9, 0xab, 0x96, 0xdc, 0x22, 0x98, 0x93, 0x47, //0x00004590 .quad 5157633273766521849
	0x64, 0x65, 0x54, 0xf3, 0xe9, 0xf8, 0x2d, 0xb3, //0x00004598 .quad -5535494683275008668
	0xf7, 0x56, 0xbc, 0x93, 0x2b, 0x7e, 0x78, 0x59, //0x000045a0 .quad 6447041592208152311
	0xbd, 0x7e, 0x29, 0x70, 0x24, 0x77, 0xf9, 0xdf, //0x000045a8 .quad -2307682335666372931
	0x5a, 0xb6, 0x55, 0x3c, 0xdb, 0x4e, 0xeb, 0x57, //0x000045b0 .quad 6335244004343789146
	0x36, 0xef, 0x19, 0xc6, 0x76, 0xea, 0xfb, 0x8b, //0x000045b8 .quad -8359830487432564938
	0xf1, 0x23, 0x6b, 0x0b, 0x92, 0x22, 0xe6, 0xed, //0x000045c0 .quad -1304317031425039375
	0x03, 0x6b, 0xa0, 0x77, 0x14, 0xe5, 0xfa, 0xae, //0x000045c8 .quad -5838102090863318269
	0xed, 0xec, 0x45, 0x8e, 0x36, 0xab, 0x5f, 0xe9, //0x000045d0 .quad -1630396289281299219
	0xc4, 0x85, 0x88, 0x95, 0x59, 0x9e, 0xb9, 0xda, //0x000045d8 .quad -2685941595151759932
	0x14, 0xb4, 0xeb, 0x18, 0x02, 0xcb, 0xdb, 0x11, //0x000045e0 .quad 1286845328412881940
	0x9b, 0x53, 0x75, 0xfd, 0xf7, 0x02, 0xb4, 0x88, //0x000045e8 .quad -8596242524610931813
	0x19, 0xa1, 0x26, 0x9f, 0xc2, 0xbd, 0x52, 0xd6, //0x000045f0 .quad -3003129357911285479
	0x81, 0xa8, 0xd2, 0xfc, 0xb5, 0x03, 0xe1, 0xaa, //0x000045f8 .quad -6133617137336276863
	0x5f, 0x49, 0xf0, 0x46, 0x33, 0x6d, 0xe7, 0x4b, //0x00004600 .quad 5469460339465668959
	0xa2, 0x52, 0x07, 0x7c, 0xa3, 0x44, 0x99, 0xd5, //0x00004608 .quad -3055335403242958174
	0xdb, 0x2d, 0x56, 0x0c, 0x40, 0xa4, 0x70, 0x6f, //0x00004610 .quad 8030098730593431003
	0xa5, 0x93, 0x84, 0x2d, 0xe6, 0xca, 0x7f, 0x85, //0x00004618 .quad -8827113654667930715
	0x52, 0xb9, 0x6b, 0x0f, 0x50, 0xcd, 0x4c, 0xcb, //0x00004620 .quad -3797434642040374958
	0x8e, 0xb8, 0xe5, 0xb8, 0x9f, 0xbd, 0xdf, 0xa6, //0x00004628 .quad -6422206049907525490
	0xa7, 0xa7, 0x46, 0x13, 0xa4, 0x00, 0x20, 0x7e, //0x00004630 .quad 9088264752731695015
	0xb2, 0x26, 0x1f, 0xa7, 0x07, 0xad, 0x97, 0xd0, //0x00004638 .quad -3416071543957018958
	0xc8, 0x28, 0x0c, 0x8c, 0x66, 0x00, 0xd4, 0x8e, //0x00004640 .quad -8154892584824854328
	0x2f, 0x78, 0x73, 0xc8, 0x24, 0xcc, 0x5e, 0x82, //0x00004648 .quad -9052573742614218705
	0xfa, 0x32, 0x0f, 0x2f, 0x80, 0x00, 0x89, 0x72, //0x00004650 .quad 8253128342678483706
	0x3b, 0x56, 0x90, 0xfa, 0x2d, 0x7f, 0xf6, 0xa2, //0x00004658 .quad -6704031159840385477
	0xb9, 0xff, 0xd2, 0x3a, 0xa0, 0x40, 0x2b, 0x4f, //0x00004660 .quad 5704724409920716729
	0xca, 0x6b, 0x34, 0x79, 0xf9, 0x1e, 0xb4, 0xcb, //0x00004668 .quad -3768352931373093942
	0xa8, 0xbf, 0x87, 0x49, 0xc8, 0x10, 0xf6, 0xe2, //0x00004670 .quad -2092466524453879896
	0xbc, 0x86, 0x81, 0xd7, 0xb7, 0x26, 0xa1, 0xfe, //0x00004678 .quad -98755145788979524
	0xc9, 0xd7, 0xf4, 0x2d, 0x7d, 0xca, 0xd9, 0x0d, //0x00004680 .quad 998051431430019017
	0x36, 0xf4, 0xb0, 0xe6, 0x32, 0xb8, 0x24, 0x9f, //0x00004688 .quad -6979250993759194058
	0xbb, 0x0d, 0x72, 0x79, 0x1c, 0x3d, 0x50, 0x91, //0x00004690 .quad -7975807747567252037
	0x43, 0x31, 0x5d, 0xa0, 0x3f, 0xe6, 0xed, 0xc6, //0x00004698 .quad -4112377723771604669
	0x2a, 0x91, 0xce, 0x97, 0x63, 0x4c, 0xa4, 0x75, //0x000046a0 .quad 8476984389250486570
	0x94, 0x7d, 0x74, 0x88, 0xcf, 0x5f, 0xa9, 0xf8, //0x000046a8 .quad -528786136287117932
	0xba, 0x1a, 0xe1, 0x3e, 0xbe, 0xaf, 0x86, 0xc9, //0x000046b0 .quad -3925256793573221702
	0x7c, 0xce, 0x48, 0xb5, 0xe1, 0xdb, 0x69, 0x9b, //0x000046b8 .quad -7248020362820530564
	0x68, 0x61, 0x99, 0xce, 0xad, 0x5b, 0xe8, 0xfb, //0x000046c0 .quad -294884973539139224
	0x1b, 0x02, 0x9b, 0x22, 0xda, 0x52, 0x44, 0xc2, //0x000046c8 .quad -4448339435098275301
	0xc3, 0xb9, 0x3f, 0x42, 0x99, 0x72, 0xe2, 0xfa, //0x000046d0 .quad -368606216923924029
	0xa2, 0xc2, 0x41, 0xab, 0x90, 0x67, 0xd5, 0xf2, //0x000046d8 .quad -948738275445456222
	0x1a, 0xd4, 0x67, 0xc9, 0x9f, 0x87, 0xcd, 0xdc, //0x000046e0 .quad -2536221894791146470
	0xa5, 0x19, 0x09, 0x6b, 0xba, 0x60, 0xc5, 0x97, //0x000046e8 .quad -7510490449794491995
	0x20, 0xc9, 0xc1, 0xbb, 0x87, 0xe9, 0x00, 0x54, //0x000046f0 .quad 6053094668365842720
	0x0f, 0x60, 0xcb, 0x05, 0xe9, 0xb8, 0xb6, 0xbd, //0x000046f8 .quad -4776427043815727089
	0x68, 0x3b, 0xb2, 0xaa, 0xe9, 0x23, 0x01, 0x29, //0x00004700 .quad 2954682317029915496
	0x13, 0x38, 0x3e, 0x47, 0x23, 0x67, 0x24, 0xed, //0x00004708 .quad -1358847786342270957
	0x21, 0x65, 0xaf, 0x0a, 0x72, 0xb6, 0xa0, 0xf9, //0x00004710 .quad -459166561069996767
	0x0b, 0xe3, 0x86, 0x0c, 0x76, 0xc0, 0x36, 0x94, //0x00004718 .quad -7766808894105001205
	0x69, 0x3e, 0x5b, 0x8d, 0x0e, 0xe4, 0x08, 0xf8, //0x00004720 .quad -573958201337495959
	0xce, 0x9b, 0xa8, 0x8f, 0x93, 0x70, 0x44, 0xb9, //0x00004728 .quad -5096825099203863602
	0x04, 0x0e, 0xb2, 0x30, 0x12, 0x1d, 0x0b, 0xb6, //0x00004730 .quad -5329133770099257852
	0xc2, 0xc2, 0x92, 0x73, 0xb8, 0x8c, 0x95, 0xe7, //0x00004738 .quad -1759345355577441598
	0xc2, 0x48, 0x6f, 0x5e, 0x2b, 0xf2, 0xc6, 0xb1, //0x00004740 .quad -5636551615525730110
	0xb9, 0xb9, 0x3b, 0x48, 0xf3, 0x77, 0xbd, 0x90, //0x00004748 .quad -8017119874876982855
	0xf3, 0x1a, 0x0b, 0x36, 0xb6, 0xae, 0x38, 0x1e, //0x00004750 .quad 2177682517447613171
	0x28, 0xa8, 0x4a, 0x1a, 0xf0, 0xd5, 0xec, 0xb4, //0x00004758 .quad -5409713825168840664
	0xb0, 0xe1, 0x8d, 0xc3, 0x63, 0xda, 0xc6, 0x25, //0x00004760 .quad 2722103146809516464
	0x32, 0x52, 0xdd, 0x20, 0x6c, 0x0b, 0x28, 0xe2, //0x00004768 .quad -2150456263033662926
	0x0e, 0xad, 0x38, 0x5a, 0x7e, 0x48, 0x9c, 0x57, //0x00004770 .quad 6313000485183335694
	0x5f, 0x53, 0x8a, 0x94, 0x23, 0x07, 0x59, 0x8d, //0x00004778 .quad -8261564192037121185
	0x51, 0xd8, 0xc6, 0xf0, 0x9d, 0x5a, 0x83, 0x2d, //0x00004780 .quad 3279564588051781713
	0x37, 0xe8, 0xac, 0x79, 0xec, 0x48, 0xaf, 0xb0, //0x00004788 .quad -5715269221619013577
	0x65, 0x8e, 0xf8, 0x6c, 0x45, 0x31, 0xe4, 0xf8, //0x00004790 .quad -512230283362660763
	0x44, 0x22, 0x18, 0x98, 0x27, 0x1b, 0xdb, 0xdc, //0x00004798 .quad -2532400508596379068
	0xff, 0x58, 0x1b, 0x64, 0xcb, 0x9e, 0x8e, 0x1b, //0x000047a0 .quad 1985699082112030975
	0x6b, 0x15, 0x0f, 0xbf, 0xf8, 0xf0, 0x08, 0x8a, //0x000047a8 .quad -8500279345513818773
	0x3f, 0x2f, 0x22, 0x3d, 0x7e, 0x46, 0x72, 0xe2, //0x000047b0 .quad -2129562165787349185
	0xc5, 0xda, 0xd2, 0xee, 0x36, 0x2d, 0x8b, 0xac, //0x000047b8 .quad -6013663163464885563
	0x0f, 0xbb, 0x6a, 0xcc, 0x1d, 0xd8, 0x0e, 0x5b, //0x000047c0 .quad 6561419329620589327
	0x77, 0x91, 0x87, 0xaa, 0x84, 0xf8, 0xad, 0xd7, //0x000047c8 .quad -2905392935903719049
	0xe9, 0xb4, 0xc2, 0x9f, 0x12, 0x47, 0xe9, 0x98, //0x000047d0 .quad -7428327965055601431
	0xea, 0xba, 0x94, 0xea, 0x52, 0xbb, 0xcc, 0x86, //0x000047d8 .quad -8733399612580906262
	0x24, 0x62, 0xb3, 0x47, 0xd7, 0x98, 0x23, 0x3f, //0x000047e0 .quad 4549648098962661924
	0xa5, 0xe9, 0x39, 0xa5, 0x27, 0xea, 0x7f, 0xa8, //0x000047e8 .quad -6305063497298744923
	0xad, 0x3a, 0xa0, 0x19, 0x0d, 0x7f, 0xec, 0x8e, //0x000047f0 .quad -8147997931578836307
	0x0e, 0x64, 0x88, 0x8e, 0xb1, 0xe4, 0x9f, 0xd2, //0x000047f8 .quad -3269643353196043250
	0xac, 0x24, 0x04, 0x30, 0x68, 0xcf, 0x53, 0x19, //0x00004800 .quad 1825030320404309164
	0x89, 0x3e, 0x15, 0xf9, 0xee, 0xee, 0xa3, 0x83, //0x00004808 .quad -8961056123388608887
	0xd7, 0x2d, 0x05, 0x3c, 0x42, 0xc3, 0xa8, 0x5f, //0x00004810 .quad 6892973918932774359
	0x2b, 0x8e, 0x5a, 0xb7, 0xaa, 0xea, 0x8c, 0xa4, //0x00004818 .quad -6589634135808373205
	0x4d, 0x79, 0x06, 0xcb, 0x12, 0xf4, 0x92, 0x37, //0x00004820 .quad 4004531380238580045
	0xb6, 0x31, 0x31, 0x65, 0x55, 0x25, 0xb0, 0xcd, //0x00004828 .quad -3625356651333078602
	0xd0, 0x0b, 0xe4, 0xbe, 0x8b, 0xd8, 0xbb, 0xe2, //0x00004830 .quad -2108853905778275376
	0x11, 0xbf, 0x3e, 0x5f, 0x55, 0x17, 0x8e, 0x80, //0x00004838 .quad -9183376934724255983
	0xc4, 0x0e, 0x9d, 0xae, 0xae, 0xce, 0x6a, 0x5b, //0x00004840 .quad 6587304654631931588
	0xd6, 0x6e, 0x0e, 0xb7, 0x2a, 0x9d, 0xb1, 0xa0, //0x00004848 .quad -6867535149977932074
	0x75, 0x52, 0x44, 0x5a, 0x5a, 0x82, 0x45, 0xf2, //0x00004850 .quad -989241218564861323
	0x8b, 0x0a, 0xd2, 0x64, 0x75, 0x04, 0xde, 0xc8, //0x00004858 .quad -3972732919045027189
	0x12, 0x67, 0xd5, 0xf0, 0xf0, 0xe2, 0xd6, 0xee, //0x00004860 .quad -1236551523206076654
	0x2e, 0x8d, 0x06, 0xbe, 0x92, 0x85, 0x15, 0xfb, //0x00004868 .quad -354230130378896082
	0x6b, 0x60, 0x85, 0x96, 0xd6, 0x4d, 0x46, 0x55, //0x00004870 .quad 6144684325637283947
	0x3d, 0x18, 0xc4, 0xb6, 0x7b, 0x73, 0xed, 0x9c, //0x00004878 .quad -7138922859127891907
	0x86, 0xb8, 0x26, 0x3c, 0x4c, 0xe1, 0x97, 0xaa, //0x00004880 .quad -6154202648235558778
	0x4c, 0x1e, 0x75, 0xa4, 0x5a, 0xd0, 0x28, 0xc4, //0x00004888 .quad -4311967555482476980
	0xa8, 0x66, 0x30, 0x4b, 0x9f, 0xd9, 0x3d, 0xd5, //0x00004890 .quad -3081067291867060568
	0xdf, 0x65, 0x92, 0x4d, 0x71, 0x04, 0x33, 0xf5, //0x00004898 .quad -778273425925708321
	0x29, 0x40, 0xfe, 0x8e, 0x03, 0xa8, 0x46, 0xe5, //0x000048a0 .quad -1925667057416912855
	0xab, 0x7f, 0x7b, 0xd0, 0xc6, 0xe2, 0x3f, 0x99, //0x000048a8 .quad -7403949918844649557
	0x33, 0xd0, 0xbd, 0x72, 0x04, 0x52, 0x98, 0xde, //0x000048b0 .quad -2407083821771141069
	0x96, 0x5f, 0x9a, 0x84, 0x78, 0xdb, 0x8f, 0xbf, //0x000048b8 .quad -4643251380128424042
	0x40, 0x44, 0x6d, 0x8f, 0x85, 0x66, 0x3e, 0x96, //0x000048c0 .quad -7620540795641314240
	0x7c, 0xf7, 0xc0, 0xa5, 0x56, 0xd2, 0x73, 0xef, //0x000048c8 .quad -1192378206733142148
	0xa8, 0x4a, 0xa4, 0x79, 0x13, 0x00, 0xe7, 0xdd, //0x000048d0 .quad -2456994988062127448
	0xad, 0x9a, 0x98, 0x27, 0x76, 0x63, 0xa8, 0x95, //0x000048d8 .quad -7662765406849295699
	0x52, 0x5d, 0x0d, 0x58, 0x18, 0xc0, 0x60, 0x55, //0x000048e0 .quad 6152128301777116498
	0x59, 0xc1, 0x7e, 0xb1, 0x53, 0x7c, 0x12, 0xbb, //0x000048e8 .quad -4966770740134231719
	0xa6, 0xb4, 0x10, 0x6e, 0x1e, 0xf0, 0xb8, 0xaa, //0x000048f0 .quad -6144897678060768090
	0xaf, 0x71, 0xde, 0x9d, 0x68, 0x1b, 0xd7, 0xe9, //0x000048f8 .quad -1596777406740401745
	0xe8, 0x70, 0xca, 0x04, 0x13, 0x96, 0xb3, 0xca, //0x00004900 .quad -3840561048787980056
	0x0d, 0x07, 0xab, 0x62, 0x21, 0x71, 0x26, 0x92, //0x00004908 .quad -7915514906853832947
	0x22, 0x0d, 0xfd, 0xc5, 0x97, 0x7b, 0x60, 0x3d, //0x00004910 .quad 4422670725869800738
	0xd1, 0xc8, 0x55, 0xbb, 0x69, 0x0d, 0xb0, 0xb6, //0x00004918 .quad -5282707615139903279
	0x6a, 0x50, 0x7c, 0xb7, 0x7d, 0x9a, 0xb8, 0x8c, //0x00004920 .quad -8306719647944912790
	0x05, 0x3b, 0x2b, 0x2a, 0xc4, 0x10, 0x5c, 0xe4, //0x00004928 .quad -1991698500497491195
	0x42, 0xb2, 0xad, 0x92, 0x8e, 0x60, 0xf3, 0x77, //0x00004930 .quad 8643358275316593218
	0xe3, 0x04, 0x5b, 0x9a, 0x7a, 0x8a, 0xb9, 0x8e, //0x00004938 .quad -8162340590452013853
	0xd3, 0x1e, 0x59, 0x37, 0xb2, 0x38, 0xf0, 0x55, //0x00004940 .quad 6192511825718353619
	0x1c, 0xc6, 0xf1, 0x40, 0x19, 0xed, 0x67, 0xb2, //0x00004948 .quad -5591239719637629412
	0x88, 0x66, 0x2f, 0xc5, 0xde, 0x46, 0x6c, 0x6b, //0x00004950 .quad 7740639782147942024
	0xa3, 0x37, 0x2e, 0x91, 0x5f, 0xe8, 0x01, 0xdf, //0x00004958 .quad -2377363631119648861
	0x15, 0xa0, 0x3d, 0x3b, 0x4b, 0xac, 0x23, 0x23, //0x00004960 .quad 2532056854628769813
	0xc6, 0xe2, 0xbc, 0xba, 0x3b, 0x31, 0x61, 0x8b, //0x00004968 .quad -8403381297090862394
	0x1a, 0x08, 0x0d, 0x0a, 0x5e, 0x97, 0xec, 0xab, //0x00004970 .quad -6058300968568813542
	0x77, 0x1b, 0x6c, 0xa9, 0x8a, 0x7d, 0x39, 0xae, //0x00004978 .quad -5892540602936190089
	0x21, 0x4a, 0x90, 0x8c, 0x35, 0xbd, 0xe7, 0x96, //0x00004980 .quad -7572876210711016927
	0x55, 0x22, 0xc7, 0x53, 0xed, 0xdc, 0xc7, 0xd9, //0x00004988 .quad -2753989735242849707
	0x54, 0x2e, 0xda, 0x77, 0x41, 0xd6, 0x50, 0x7e, //0x00004990 .quad 9102010423587778132
	0x75, 0x75, 0x5c, 0x54, 0x14, 0xea, 0x1c, 0x88, //0x00004998 .quad -8638772612167862923
	0xe9, 0xb9, 0xd0, 0xd5, 0xd1, 0x0b, 0xe5, 0xdd, //0x000049a0 .quad -2457545025797441047
	0xd2, 0x92, 0x73, 0x69, 0x99, 0x24, 0x24, 0xaa, //0x000049a8 .quad -6186779746782440750
	0x64, 0xe8, 0x44, 0x4b, 0xc6, 0x4e, 0x5e, 0x95, //0x000049b0 .quad -7683617300674189212
	0x87, 0x77, 0xd0, 0xc3, 0xbf, 0x2d, 0xad, 0xd4, //0x000049b8 .quad -3121788665050663033
	0x3e, 0x11, 0x0b, 0xef, 0x3b, 0xf1, 0x5a, 0xbd, //0x000049c0 .quad -4802260812921368258
	0xb4, 0x4a, 0x62, 0xda, 0x97, 0x3c, 0xec, 0x84, //0x000049c8 .quad -8868646943297746252
	0x8e, 0xd5, 0xcd, 0xea, 0x8a, 0xad, 0xb1, 0xec, //0x000049d0 .quad -1391139997724322418
	0x61, 0xdd, 0xfa, 0xd0, 0xbd, 0x4b, 0x27, 0xa6, //0x000049d8 .quad -6474122660694794911
	0xf2, 0x4a, 0x81, 0xa5, 0xed, 0x18, 0xde, 0x67, //0x000049e0 .quad 7484447039699372786
	0xba, 0x94, 0x39, 0x45, 0xad, 0x1e, 0xb1, 0xcf, //0x000049e8 .quad -3480967307441105734
	0xd7, 0xce, 0x70, 0x87, 0x94, 0xcf, 0xea, 0x80, //0x000049f0 .quad -9157278655470055721
	0xf4, 0xfc, 0x43, 0x4b, 0x2c, 0xb3, 0xce, 0x81, //0x000049f8 .quad -9093133594791772940
	0x8d, 0x02, 0x4d, 0xa9, 0x79, 0x83, 0x25, 0xa1, //0x00004a00 .quad -6834912300910181747
	0x31, 0xfc, 0x14, 0x5e, 0xf7, 0x5f, 0x42, 0xa2, //0x00004a08 .quad -6754730975062328271
	0x30, 0x43, 0xa0, 0x13, 0x58, 0xe4, 0x6e, 0x09, //0x00004a10 .quad 679731660717048624
	0x3e, 0x3b, 0x9a, 0x35, 0xf5, 0xf7, 0xd2, 0xca, //0x00004a18 .quad -3831727700400522434
	0xfc, 0x53, 0x88, 0x18, 0x6e, 0x9d, 0xca, 0x8b, //0x00004a20 .quad -8373707460958465028
	0x0d, 0xca, 0x00, 0x83, 0xf2, 0xb5, 0x87, 0xfd, //0x00004a28 .quad -177973607073265139
	0x7d, 0x34, 0x55, 0xcf, 0x64, 0xa2, 0x5e, 0x77, //0x00004a30 .quad 8601490892183123069
	0x48, 0x7e, 0xe0, 0x91, 0xb7, 0xd1, 0x74, 0x9e, //0x00004a38 .quad -7028762532061872568
	0x9d, 0x81, 0x2a, 0x03, 0xfe, 0x4a, 0x36, 0x95, //0x00004a40 .quad -7694880458480647779
	0xda, 0x9d, 0x58, 0x76, 0x25, 0x06, 0x12, 0xc6, //0x00004a48 .quad -4174267146649952806
	0x04, 0x22, 0xf5, 0x83, 0xbd, 0xdd, 0x83, 0x3a, //0x00004a50 .quad 4216457482181353988
	0x51, 0xc5, 0xee, 0xd3, 0xae, 0x87, 0x96, 0xf7, //0x00004a58 .quad -606147914885053103
	0x42, 0x35, 0x79, 0x72, 0x96, 0x6a, 0x92, 0xc4, //0x00004a60 .quad -4282243101277735614
	0x52, 0x3b, 0x75, 0x44, 0xcd, 0x14, 0xbe, 0x9a, //0x00004a68 .quad -7296371474444240046
	0x93, 0x82, 0x17, 0x0f, 0x3c, 0x05, 0xb7, 0x75, //0x00004a70 .quad 8482254178684994195
	0x27, 0x8a, 0x92, 0x95, 0x00, 0x9a, 0x6d, 0xc1, //0x00004a78 .quad -4508778324627912153
	0x38, 0x63, 0xdd, 0x12, 0x8b, 0xc6, 0x24, 0x53, //0x00004a80 .quad 5991131704928854840
	0xb1, 0x2c, 0xf7, 0xba, 0x80, 0x00, 0xc9, 0xf1, //0x00004a88 .quad -1024286887357502287
	0x03, 0x5e, 0xca, 0xeb, 0x16, 0xfc, 0xf6, 0xd3, //0x00004a90 .quad -3173071712060547581
	0xee, 0x7b, 0xda, 0x74, 0x50, 0xa0, 0x1d, 0x97, //0x00004a98 .quad -7557708332239520786
	0x84, 0xf5, 0xbc, 0xa6, 0x1c, 0xbb, 0xf4, 0x88, //0x00004aa0 .quad -8578025658503072380
	0xea, 0x1a, 0x11, 0x92, 0x64, 0x08, 0xe5, 0xbc, //0x00004aa8 .quad -4835449396872013078
	0xe5, 0x32, 0x6c, 0xd0, 0xe3, 0xe9, 0x31, 0x2b, //0x00004ab0 .quad 3112525982153323237
	0xa5, 0x61, 0x95, 0xb6, 0x7d, 0x4a, 0x1e, 0xec, //0x00004ab8 .quad -1432625727662628443
	0xcf, 0x9f, 0x43, 0x62, 0x2e, 0x32, 0xff, 0x3a, //0x00004ac0 .quad 4251171748059520975
	0x07, 0x5d, 0x1d, 0x92, 0x8e, 0xee, 0x92, 0x93, //0x00004ac8 .quad -7812920107430224633
	0xc2, 0x87, 0xd4, 0xfa, 0xb9, 0xfe, 0xbe, 0x09, //0x00004ad0 .quad 702278666647013314
	0x49, 0xb4, 0xa4, 0x36, 0x32, 0xaa, 0x77, 0xb8, //0x00004ad8 .quad -5154464115860392887
	0xb3, 0xa9, 0x89, 0x79, 0x68, 0xbe, 0x2e, 0x4c, //0x00004ae0 .quad 5489534351736154547
	0x5b, 0xe1, 0x4d, 0xc4, 0xbe, 0x94, 0x95, 0xe6, //0x00004ae8 .quad -1831394126398103205
	0x10, 0x0a, 0xf6, 0x4b, 0x01, 0x37, 0x9d, 0x0f, //0x00004af0 .quad 1125115960621402640
	0xd9, 0xac, 0xb0, 0x3a, 0xf7, 0x7c, 0x1d, 0x90, //0x00004af8 .quad -8062150356639896359
	0x94, 0x8c, 0xf3, 0x9e, 0xc1, 0x84, 0x84, 0x53, //0x00004b00 .quad 6018080969204141204
	0x0f, 0xd8, 0x5c, 0x09, 0x35, 0xdc, 0x24, 0xb4, //0x00004b08 .quad -5466001927372482545
	0xb9, 0x6f, 0xb0, 0x06, 0xf2, 0xa5, 0x65, 0x28, //0x00004b10 .quad 2910915193077788601
	0x13, 0x0e, 0xb4, 0x4b, 0x42, 0x13, 0x2e, 0xe1, //0x00004b18 .quad -2220816390788215277
	0xd3, 0x45, 0x2e, 0x44, 0xb7, 0x87, 0x3f, 0xf9, //0x00004b20 .quad -486521013540076077
	0xcb, 0x88, 0x50, 0x6f, 0x09, 0xcc, 0xbc, 0x8c, //0x00004b28 .quad -8305539271883716405
	0x48, 0xd7, 0x39, 0x15, 0xa5, 0x69, 0x8f, 0xf7, //0x00004b30 .quad -608151266925095096
	0xfe, 0xaa, 0x24, 0xcb, 0x0b, 0xff, 0xeb, 0xaf, //0x00004b38 .quad -5770238071427257602
	0x1b, 0x4d, 0x88, 0x5a, 0x0e, 0x44, 0x73, 0xb5, //0x00004b40 .quad -5371875102083756773
	0xbe, 0xd5, 0xed, 0xbd, 0xce, 0xfe, 0xe6, 0xdb, //0x00004b48 .quad -2601111570856684098
	0x30, 0x30, 0x95, 0xf8, 0x88, 0x0a, 0x68, 0x31, //0x00004b50 .quad 3560107088838733872
	0x97, 0xa5, 0xb4, 0x36, 0x41, 0x5f, 0x70, 0x89, //0x00004b58 .quad -8543223759426509417
	0x3d, 0x7c, 0xba, 0x36, 0x2b, 0x0d, 0xc2, 0xfd, //0x00004b60 .quad -161552157378970563
	0xfc, 0xce, 0x61, 0x84, 0x11, 0x77, 0xcc, 0xab, //0x00004b68 .quad -6067343680855748868
	0x4c, 0x1b, 0x69, 0x04, 0x76, 0x90, 0x32, 0x3d, //0x00004b70 .quad 4409745821703674700
	0xbc, 0x42, 0x7a, 0xe5, 0xd5, 0x94, 0xbf, 0xd6, //0x00004b78 .quad -2972493582642298180
	0x0f, 0xb1, 0xc1, 0xc2, 0x49, 0x9a, 0x3f, 0xa6, //0x00004b80 .quad -6467280898289979121
	0xb5, 0x69, 0x6c, 0xaf, 0x05, 0xbd, 0x37, 0x86, //0x00004b88 .quad -8775337516792518219
	0x53, 0x1d, 0x72, 0x33, 0xdc, 0x80, 0xcf, 0x0f, //0x00004b90 .quad 1139270913992301907
	0x23, 0x84, 0x47, 0x1b, 0x47, 0xac, 0xc5, 0xa7, //0x00004b98 .quad -6357485877563259869
	0xa8, 0xa4, 0x4e, 0x40, 0x13, 0x61, 0xc3, 0xd3, //0x00004ba0 .quad -3187597375937010520
	0x2b, 0x65, 0x19, 0xe2, 0x58, 0x17, 0xb7, 0xd1, //0x00004ba8 .quad -3335171328526686933
	0xe9, 0x26, 0x31, 0x08, 0xac, 0x1c, 0x5a, 0x64, //0x00004bb0 .quad 7231123676894144233
	0x3b, 0xdf, 0x4f, 0x8d, 0x97, 0x6e, 0x12, 0x83, //0x00004bb8 .quad -9002011107970261189
	0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, 0x70, 0x3d, //0x00004bc0 .quad 4427218577690292387
	0x0a, 0xd7, 0xa3, 0x70, 0x3d, 0x0a, 0xd7, 0xa3, //0x00004bc8 .quad -6640827866535438582
	0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, //0x00004bd0 QUAD $0xcccccccccccccccc; QUAD $0xcccccccccccccccc  // .space 16, '\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc\xcc'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004be0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, //0x00004be8 .quad -9223372036854775808
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004bf0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, //0x00004bf8 .quad -6917529027641081856
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c00 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, //0x00004c08 .quad -4035225266123964416
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c10 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfa, //0x00004c18 .quad -432345564227567616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c20 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x9c, //0x00004c28 .quad -7187745005283311616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c30 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0xc3, //0x00004c38 .quad -4372995238176751616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c40 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0xf4, //0x00004c48 .quad -854558029293551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c50 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x96, 0x98, //0x00004c58 .quad -7451627795949551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c60 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xbc, 0xbe, //0x00004c68 .quad -4702848726509551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c70 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6b, 0xee, //0x00004c78 .quad -1266874889709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c80 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0x02, 0x95, //0x00004c88 .quad -7709325833709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004c90 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x40, 0xb7, 0x43, 0xba, //0x00004c98 .quad -5024971273709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ca0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x10, 0xa5, 0xd4, 0xe8, //0x00004ca8 .quad -1669528073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cb0 .quad 0
	0x00, 0x00, 0x00, 0x00, 0x2a, 0xe7, 0x84, 0x91, //0x00004cb8 .quad -7960984073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cc0 .quad 0
	0x00, 0x00, 0x00, 0x80, 0xf4, 0x20, 0xe6, 0xb5, //0x00004cc8 .quad -5339544073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cd0 .quad 0
	0x00, 0x00, 0x00, 0xa0, 0x31, 0xa9, 0x5f, 0xe3, //0x00004cd8 .quad -2062744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004ce0 .quad 0
	0x00, 0x00, 0x00, 0x04, 0xbf, 0xc9, 0x1b, 0x8e, //0x00004ce8 .quad -8206744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004cf0 .quad 0
	0x00, 0x00, 0x00, 0xc5, 0x2e, 0xbc, 0xa2, 0xb1, //0x00004cf8 .quad -5646744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d00 .quad 0
	0x00, 0x00, 0x40, 0x76, 0x3a, 0x6b, 0x0b, 0xde, //0x00004d08 .quad -2446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d10 .quad 0
	0x00, 0x00, 0xe8, 0x89, 0x04, 0x23, 0xc7, 0x8a, //0x00004d18 .quad -8446744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d20 .quad 0
	0x00, 0x00, 0x62, 0xac, 0xc5, 0xeb, 0x78, 0xad, //0x00004d28 .quad -5946744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d30 .quad 0
	0x00, 0x80, 0x7a, 0x17, 0xb7, 0x26, 0xd7, 0xd8, //0x00004d38 .quad -2821744073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d40 .quad 0
	0x00, 0x90, 0xac, 0x6e, 0x32, 0x78, 0x86, 0x87, //0x00004d48 .quad -8681119073709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d50 .quad 0
	0x00, 0xb4, 0x57, 0x0a, 0x3f, 0x16, 0x68, 0xa9, //0x00004d58 .quad -6239712823709551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d60 .quad 0
	0x00, 0xa1, 0xed, 0xcc, 0xce, 0x1b, 0xc2, 0xd3, //0x00004d68 .quad -3187955011209551616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d70 .quad 0
	0xa0, 0x84, 0x14, 0x40, 0x61, 0x51, 0x59, 0x84, //0x00004d78 .quad -8910000909647051616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d80 .quad 0
	0xc8, 0xa5, 0x19, 0x90, 0xb9, 0xa5, 0x6f, 0xa5, //0x00004d88 .quad -6525815118631426616
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00004d90 .quad 0
	0x3a, 0x0f, 0x20, 0xf4, 0x27, 0x8f, 0xcb, 0xce, //0x00004d98 .quad -3545582879861895366
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, //0x00004da0 .quad 4611686018427387904
	0x84, 0x09, 0x94, 0xf8, 0x78, 0x39, 0x3f, 0x81, //0x00004da8 .quad -9133518327554766460
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, //0x00004db0 .quad 5764607523034234880
	0xe5, 0x0b, 0xb9, 0x36, 0xd7, 0x07, 0x8f, 0xa1, //0x00004db8 .quad -6805211891016070171
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa4, //0x00004dc0 .quad -6629298651489370112
	0xde, 0x4e, 0x67, 0x04, 0xcd, 0xc9, 0xf2, 0xc9, //0x00004dc8 .quad -3894828845342699810
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, //0x00004dd0 .quad 5548434740920451072
	0x96, 0x22, 0x81, 0x45, 0x40, 0x7c, 0x6f, 0xfc, //0x00004dd8 .quad -256850038250986858
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xf0, //0x00004de0 .quad -1143914305352105984
	0x9d, 0xb5, 0x70, 0x2b, 0xa8, 0xad, 0xc5, 0x9d, //0x00004de8 .quad -7078060301547948643
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x6c, //0x00004df0 .quad 7793479155164643328
	0x05, 0xe3, 0x4c, 0x36, 0x12, 0x19, 0x37, 0xc5, //0x00004df8 .quad -4235889358507547899
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0xc7, //0x00004e00 .quad -4093209111326359552
	0xc6, 0x1b, 0xe0, 0xc3, 0x56, 0xdf, 0x84, 0xf6, //0x00004e08 .quad -683175679707046970
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x7f, 0x3c, //0x00004e10 .quad 4359273333062107136
	0x5c, 0x11, 0x6c, 0x3a, 0x96, 0x0b, 0x13, 0x9a, //0x00004e18 .quad -7344513827457986212
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x9f, 0x4b, //0x00004e20 .quad 5449091666327633920
	0xb3, 0x15, 0x07, 0xc9, 0x7b, 0xce, 0x97, 0xc0, //0x00004e28 .quad -4568956265895094861
	0x00, 0x00, 0x00, 0x00, 0x00, 0xd4, 0x86, 0x1e, //0x00004e30 .quad 2199678564482154496
	0x20, 0xdb, 0x48, 0xbb, 0x1a, 0xc2, 0xbd, 0xf0, //0x00004e38 .quad -1099509313941480672
	0x00, 0x00, 0x00, 0x00, 0x80, 0x44, 0x14, 0x13, //0x00004e40 .quad 1374799102801346560
	0xf4, 0x88, 0x0d, 0xb5, 0x50, 0x99, 0x76, 0x96, //0x00004e48 .quad -7604722348854507276
	0x00, 0x00, 0x00, 0x00, 0xa0, 0x55, 0xd9, 0x17, //0x00004e50 .quad 1718498878501683200
	0x31, 0xeb, 0x50, 0xe2, 0xa4, 0x3f, 0x14, 0xbc, //0x00004e58 .quad -4894216917640746191
	0x00, 0x00, 0x00, 0x00, 0x08, 0xab, 0xcf, 0x5d, //0x00004e60 .quad 6759809616554491904
	0xfd, 0x25, 0xe5, 0x1a, 0x8e, 0x4f, 0x19, 0xeb, //0x00004e68 .quad -1506085128623544835
	0x00, 0x00, 0x00, 0x00, 0xe5, 0xca, 0xa1, 0x5a, //0x00004e70 .quad 6530724019560251392
	0xbe, 0x37, 0xcf, 0xd0, 0xb8, 0xd1, 0xef, 0x92, //0x00004e78 .quad -7858832233030797378
	0x00, 0x00, 0x00, 0x40, 0x9e, 0x3d, 0x4a, 0xf1, //0x00004e80 .quad -1059967012404461568
	0xad, 0x05, 0x03, 0x05, 0x27, 0xc6, 0xab, 0xb7, //0x00004e88 .quad -5211854272861108819
	0x00, 0x00, 0x00, 0xd0, 0x05, 0xcd, 0x9c, 0x6d, //0x00004e90 .quad 7898413271349198848
	0x19, 0xc7, 0x43, 0xc6, 0xb0, 0xb7, 0x96, 0xe5, //0x00004e98 .quad -1903131822648998119
	0x00, 0x00, 0x00, 0xa2, 0x23, 0x00, 0x82, 0xe4, //0x00004ea0 .quad -1981020733047832576
	0x6f, 0x5c, 0xea, 0x7b, 0xce, 0x32, 0x7e, 0x8f, //0x00004ea8 .quad -8106986416796705681
	0x00, 0x00, 0x80, 0x8a, 0x2c, 0x80, 0xa2, 0xdd, //0x00004eb0 .quad -2476275916309790720
	0x8b, 0xf3, 0xe4, 0x1a, 0x82, 0xbf, 0x5d, 0xb3, //0x00004eb8 .quad -5522047002568494197
	0x00, 0x00, 0x20, 0xad, 0x37, 0x20, 0x0b, 0xd5, //0x00004ec0 .quad -3095344895387238400
	0x6e, 0x30, 0x9e, 0xa1, 0x62, 0x2f, 0x35, 0xe0, //0x00004ec8 .quad -2290872734783229842
	0x00, 0x00, 0x34, 0xcc, 0x22, 0xf4, 0x26, 0x45, //0x00004ed0 .quad 4982938468024057856
	0x45, 0xde, 0x02, 0xa5, 0x9d, 0x3d, 0x21, 0x8c, //0x00004ed8 .quad -8349324486880600507
	0x00, 0x00, 0x41, 0x7f, 0x2b, 0xb1, 0x70, 0x96, //0x00004ee0 .quad -7606384970252091392
	0xd6, 0x95, 0x43, 0x0e, 0x05, 0x8d, 0x29, 0xaf, //0x00004ee8 .quad -5824969590173362730
	0x00, 0x40, 0x11, 0x5f, 0x76, 0xdd, 0x0c, 0x3c, //0x00004ef0 .quad 4327076842467049472
	0x4c, 0x7b, 0xd4, 0x51, 0x46, 0xf0, 0xf3, 0xda, //0x00004ef8 .quad -2669525969289315508
	0x00, 0xc8, 0x6a, 0xfb, 0x69, 0x0a, 0x88, 0xa5, //0x00004f00 .quad -6518949010312869888
	0x0f, 0xcd, 0x24, 0xf3, 0x2b, 0x76, 0xd8, 0x88, //0x00004f08 .quad -8585982758446904049
	0x00, 0x7a, 0x45, 0x7a, 0x04, 0x0d, 0xea, 0x8e, //0x00004f10 .quad -8148686262891087360
	0x53, 0x00, 0xee, 0xef, 0xb6, 0x93, 0x0e, 0xab, //0x00004f18 .quad -6120792429631242157
	0x80, 0xd8, 0xd6, 0x98, 0x45, 0x90, 0xa4, 0x72, //0x00004f20 .quad 8260886245095692416
	0x68, 0x80, 0xe9, 0xab, 0xa4, 0x38, 0xd2, 0xd5, //0x00004f28 .quad -3039304518611664792
	0x50, 0x47, 0x86, 0x7f, 0x2b, 0xda, 0xa6, 0x47, //0x00004f30 .quad 5163053903184807760
	0x41, 0xf0, 0x71, 0xeb, 0x66, 0x63, 0xa3, 0x85, //0x00004f38 .quad -8817094351773372351
	0x24, 0xd9, 0x67, 0x5f, 0xb6, 0x90, 0x90, 0x99, //0x00004f40 .quad -7381240676301154012
	0x51, 0x6c, 0x4e, 0xa6, 0x40, 0x3c, 0x0c, 0xa7, //0x00004f48 .quad -6409681921289327535
	0x6d, 0xcf, 0x41, 0xf7, 0xe3, 0xb4, 0xf4, 0xff, //0x00004f50 .quad -3178808521666707
	0x65, 0x07, 0xe2, 0xcf, 0x50, 0x4b, 0xcf, 0xd0, //0x00004f58 .quad -3400416383184271515
	0xa4, 0x21, 0x89, 0x7a, 0x0e, 0xf1, 0xf8, 0xbf, //0x00004f60 .quad -4613672773753429596
	0x9f, 0x44, 0xed, 0x81, 0x12, 0x8f, 0x81, 0x82, //0x00004f68 .quad -9042789267131251553
	0x0d, 0x6a, 0x2b, 0x19, 0x52, 0x2d, 0xf7, 0xaf, //0x00004f70 .quad -5767090967191786995
	0xc7, 0x95, 0x68, 0x22, 0xd7, 0xf2, 0x21, 0xa3, //0x00004f78 .quad -6691800565486676537
	0x90, 0x44, 0x76, 0x9f, 0xa6, 0xf8, 0xf4, 0x9b, //0x00004f80 .quad -7208863708989733744
	0x39, 0xbb, 0x02, 0xeb, 0x8c, 0x6f, 0xea, 0xcb, //0x00004f88 .quad -3753064688430957767
	0xb4, 0xd5, 0x53, 0x47, 0xd0, 0x36, 0xf2, 0x02, //0x00004f90 .quad 212292400617608628
	0x08, 0x6a, 0xc3, 0x25, 0x70, 0x0b, 0xe5, 0xfe, //0x00004f98 .quad -79644842111309304
	0x90, 0x65, 0x94, 0x2c, 0x42, 0x62, 0xd7, 0x01, //0x00004fa0 .quad 132682750386005392
	0x45, 0x22, 0x9a, 0x17, 0x26, 0x27, 0x4f, 0x9f, //0x00004fa8 .quad -6967307053960650171
	0xf5, 0x7e, 0xb9, 0xb7, 0xd2, 0x3a, 0x4d, 0x42, //0x00004fb0 .quad 4777539456409894645
	0xd6, 0xaa, 0x80, 0x9d, 0xef, 0xf0, 0x22, 0xc7, //0x00004fb8 .quad -4097447799023424810
	0xb2, 0xde, 0xa7, 0x65, 0x87, 0x89, 0xe0, 0xd2, //0x00004fc0 .quad -3251447716342407502
	0x8b, 0xd5, 0xe0, 0x84, 0x2b, 0xad, 0xeb, 0xf8, //0x00004fc8 .quad -510123730351893109
	0x2f, 0xeb, 0x88, 0x9f, 0xf4, 0x55, 0xcc, 0x63, //0x00004fd0 .quad 7191217214140771119
	0x77, 0x85, 0x0c, 0x33, 0x3b, 0x4c, 0x93, 0x9b, //0x00004fd8 .quad -7236356359111015049
	0xfb, 0x25, 0x6b, 0xc7, 0x71, 0x6b, 0xbf, 0x3c, //0x00004fe0 .quad 4377335499248575995
	0xd5, 0xa6, 0xcf, 0xff, 0x49, 0x1f, 0x78, 0xc2, //0x00004fe8 .quad -4433759430461380907
	0x7a, 0xef, 0x45, 0x39, 0x4e, 0x46, 0xef, 0x8b, //0x00004ff0 .quad -8363388681221443718
	0x8a, 0x90, 0xc3, 0x7f, 0x1c, 0x27, 0x16, 0xf3, //0x00004ff8 .quad -930513269649338230
	0xac, 0xb5, 0xcb, 0xe3, 0xf0, 0x8b, 0x75, 0x97, //0x00005000 .quad -7532960934977096276
	0x56, 0x3a, 0xda, 0xcf, 0x71, 0xd8, 0xed, 0x97, //0x00005008 .quad -7499099821171918250
	0x17, 0xa3, 0xbe, 0x1c, 0xed, 0xee, 0x52, 0x3d, //0x00005010 .quad 4418856886560793367
	0xec, 0xc8, 0xd0, 0x43, 0x8e, 0x4e, 0xe9, 0xbd, //0x00005018 .quad -4762188758037509908
	0xdd, 0x4b, 0xee, 0x63, 0xa8, 0xaa, 0xa7, 0x4c, //0x00005020 .quad 5523571108200991709
	0x27, 0xfb, 0xc4, 0xd4, 0x31, 0xa2, 0x63, 0xed, //0x00005028 .quad -1341049929119499481
	0x6a, 0xef, 0x74, 0x3e, 0xa9, 0xca, 0xe8, 0x8f, //0x00005030 .quad -8076983103442849942
	0xf8, 0x1c, 0xfb, 0x24, 0x5f, 0x45, 0x5e, 0x94, //0x00005038 .quad -7755685233340769032
	0x44, 0x2b, 0x12, 0x8e, 0x53, 0xfd, 0xe2, 0xb3, //0x00005040 .quad -5484542860876174524
	0x36, 0xe4, 0x39, 0xee, 0xb6, 0xd6, 0x75, 0xb9, //0x00005048 .quad -5082920523248573386
	0x16, 0xb6, 0x96, 0x71, 0xa8, 0xbc, 0xdb, 0x60, //0x00005050 .quad 6979379479186945558
	0x44, 0x5d, 0xc8, 0xa9, 0x64, 0x4c, 0xd3, 0xe7, //0x00005058 .quad -1741964635633328828
	0xcd, 0x31, 0xfe, 0x46, 0xe9, 0x55, 0x89, 0xbc, //0x00005060 .quad -4861259862362934835
	0x4a, 0x3a, 0x1d, 0xea, 0xbe, 0x0f, 0xe4, 0x90, //0x00005068 .quad -8006256924911912374
	0x41, 0xbe, 0xbd, 0x98, 0x63, 0xab, 0xab, 0x6b, //0x00005070 .quad 7758483227328495169
	0xdd, 0x88, 0xa4, 0xa4, 0xae, 0x13, 0x1d, 0xb5, //0x00005078 .quad -5396135137712502563
	0xd1, 0x2d, 0xed, 0x7e, 0x3c, 0x96, 0x96, 0xc6, //0x00005080 .quad -4136954021121544751
	0x14, 0xab, 0xcd, 0x4d, 0x9a, 0x58, 0x64, 0xe2, //0x00005088 .quad -2133482903713240300
	0xa2, 0x3c, 0x54, 0xcf, 0xe5, 0x1d, 0x1e, 0xfc, //0x00005090 .quad -279753253987271518
	0xec, 0x8a, 0xa0, 0x70, 0x60, 0xb7, 0x7e, 0x8d, //0x00005098 .quad -8250955842461857044
	0xcb, 0x4b, 0x29, 0x43, 0x5f, 0xa5, 0x25, 0x3b, //0x000050a0 .quad 4261994450943298507
	0xa8, 0xad, 0xc8, 0x8c, 0x38, 0x65, 0xde, 0xb0, //0x000050a8 .quad -5702008784649933400
	0xbe, 0x9e, 0xf3, 0x13, 0xb7, 0x0e, 0xef, 0x49, //0x000050b0 .quad 5327493063679123134
	0x12, 0xd9, 0xfa, 0xaf, 0x86, 0xfe, 0x15, 0xdd, //0x000050b8 .quad -2515824962385028846
	0x37, 0x43, 0x78, 0x6c, 0x32, 0x69, 0x35, 0x6e, //0x000050c0 .quad 7941369183226839863
	0xab, 0xc7, 0xfc, 0x2d, 0x14, 0xbf, 0x2d, 0x8a, //0x000050c8 .quad -8489919629131724885
	0x04, 0x54, 0x96, 0x07, 0x7f, 0xc3, 0xc2, 0x49, //0x000050d0 .quad 5315025460606161924
	0x96, 0xf9, 0x7b, 0x39, 0xd9, 0x2e, 0xb9, 0xac, //0x000050d8 .quad -6000713517987268202
	0x06, 0xe9, 0x7b, 0xc9, 0x5e, 0x74, 0x33, 0xdc, //0x000050e0 .quad -2579590211097073402
	0xfb, 0xf7, 0xda, 0x87, 0x8f, 0x7a, 0xe7, 0xd7, //0x000050e8 .quad -2889205879056697349
	0xa3, 0x71, 0xed, 0x3d, 0xbb, 0x28, 0xa0, 0x69, //0x000050f0 .quad 7611128154919104931
	0xfd, 0xda, 0xe8, 0xb4, 0x99, 0xac, 0xf0, 0x86, //0x000050f8 .quad -8723282702051517699
	0x0c, 0xce, 0x68, 0x0d, 0xea, 0x32, 0x08, 0xc4, //0x00005100 .quad -4321147861633282548
	0xbc, 0x11, 0x23, 0x22, 0xc0, 0xd7, 0xac, 0xa8, //0x00005108 .quad -6292417359137009220
	0x90, 0x01, 0xc3, 0x90, 0xa4, 0x3f, 0x0a, 0xf5, //0x00005110 .quad -789748808614215280
	0x2b, 0xd6, 0xab, 0x2a, 0xb0, 0x0d, 0xd8, 0xd2, //0x00005118 .quad -3253835680493873621
	0xfa, 0xe0, 0x79, 0xda, 0xc6, 0x67, 0x26, 0x79, //0x00005120 .quad 8729779031470891258
	0xdb, 0x65, 0xab, 0x1a, 0x8e, 0x08, 0xc7, 0x83, //0x00005128 .quad -8951176327949752869
	0x38, 0x59, 0x18, 0x91, 0xb8, 0x01, 0x70, 0x57, //0x00005130 .quad 6300537770911226168
	0x52, 0x3f, 0x56, 0xa1, 0xb1, 0xca, 0xb8, 0xa4, //0x00005138 .quad -6577284391509803182
	0x86, 0x6f, 0x5e, 0xb5, 0x26, 0x02, 0x4c, 0xed, //0x00005140 .quad -1347699823215743098
	0x26, 0xcf, 0xab, 0x09, 0x5e, 0xfd, 0xe6, 0xcd, //0x00005148 .quad -3609919470959866074
	0xb4, 0x05, 0x5b, 0x31, 0x58, 0x81, 0x4f, 0x54, //0x00005150 .quad 6075216638131242420
	0x78, 0x61, 0x0b, 0xc6, 0x5a, 0x5e, 0xb0, 0x80, //0x00005158 .quad -9173728696990998152
	0x21, 0xc7, 0xb1, 0x3d, 0xae, 0x61, 0x63, 0x69, //0x00005160 .quad 7594020797664053025
	0xd6, 0x39, 0x8e, 0x77, 0xf1, 0x75, 0xdc, 0xa0, //0x00005168 .quad -6855474852811359786
	0xe9, 0x38, 0x1e, 0xcd, 0x19, 0x3a, 0xbc, 0x03, //0x00005170 .quad 269153960225290473
	0x4c, 0xc8, 0x71, 0xd5, 0x6d, 0x93, 0x13, 0xc9, //0x00005178 .quad -3957657547586811828
	0x23, 0xc7, 0x65, 0x40, 0xa0, 0x48, 0xab, 0x04, //0x00005180 .quad 336442450281613091
	0x5f, 0x3a, 0xce, 0x4a, 0x49, 0x78, 0x58, 0xfb, //0x00005188 .quad -335385916056126881
	0x76, 0x9c, 0x3f, 0x28, 0x64, 0x0d, 0xeb, 0x62, //0x00005190 .quad 7127805559067090038
	0x7b, 0xe4, 0xc0, 0xce, 0x2d, 0x4b, 0x17, 0x9d, //0x00005198 .quad -7127145225176161157
	0x94, 0x83, 0x4f, 0x32, 0xbd, 0xd0, 0xa5, 0x3b, //0x000051a0 .quad 4298070930406474644
	0x9a, 0x1d, 0x71, 0x42, 0xf9, 0x1d, 0x5d, 0xc4, //0x000051a8 .quad -4297245513042813542
	0x79, 0x64, 0xe3, 0x7e, 0xec, 0x44, 0x8f, 0xca, //0x000051b0 .quad -3850783373846682503
	0x00, 0x65, 0x0d, 0x93, 0x77, 0x65, 0x74, 0xf5, //0x000051b8 .quad -759870872876129024
	0xcb, 0x1e, 0x4e, 0xcf, 0x13, 0x8b, 0x99, 0x7e, //0x000051c0 .quad 9122475437414293195
	0x20, 0x5f, 0xe8, 0xbb, 0x6a, 0xbf, 0x68, 0x99, //0x000051c8 .quad -7392448323188662496
	0x7e, 0xa6, 0x21, 0xc3, 0xd8, 0xed, 0x3f, 0x9e, //0x000051d0 .quad -7043649776941685122
	0xe8, 0x76, 0xe2, 0x6a, 0x45, 0xef, 0xc2, 0xbf, //0x000051d8 .quad -4628874385558440216
	0x1e, 0x10, 0xea, 0xf3, 0x4e, 0xe9, 0xcf, 0xc5, //0x000051e0 .quad -4192876202749718498
	0xa2, 0x14, 0x9b, 0xc5, 0x16, 0xab, 0xb3, 0xef, //0x000051e8 .quad -1174406963520662366
	0x12, 0x4a, 0x72, 0x58, 0xd1, 0xf1, 0xa1, 0xbb, //0x000051f0 .quad -4926390635932268014
	0xe5, 0xec, 0x80, 0x3b, 0xee, 0x4a, 0xd0, 0x95, //0x000051f8 .quad -7651533379841495835
	0x97, 0xdc, 0x8e, 0xae, 0x45, 0x6e, 0x8a, 0x2a, //0x00005200 .quad 3065383741939440791
	0x1f, 0x28, 0x61, 0xca, 0xa9, 0x5d, 0x44, 0xbb, //0x00005208 .quad -4952730706374481889
	0xbd, 0x93, 0x32, 0x1a, 0xd7, 0x09, 0x2d, 0xf5, //0x00005210 .quad -779956341003086915
	0x26, 0x72, 0xf9, 0x3c, 0x14, 0x75, 0x15, 0xea, //0x00005218 .quad -1579227364540714458
	0x56, 0x9c, 0x5f, 0x70, 0x26, 0x26, 0x3c, 0x59, //0x00005220 .quad 6430056314514152534
	0x58, 0xe7, 0x1b, 0xa6, 0x2c, 0x69, 0x4d, 0x92, //0x00005228 .quad -7904546130479028392
	0x6c, 0x83, 0x77, 0x0c, 0xb0, 0x2f, 0x8b, 0x6f, //0x00005230 .quad 8037570393142690668
	0x2e, 0xe1, 0xa2, 0xcf, 0x77, 0xc3, 0xe0, 0xb6, //0x00005238 .quad -5268996644671397586
	0x47, 0x64, 0x95, 0x0f, 0x9c, 0xfb, 0x6d, 0x0b, //0x00005240 .quad 823590954573587527
	0x7a, 0x99, 0x8b, 0xc3, 0x55, 0xf4, 0x98, 0xe4, //0x00005248 .quad -1974559787411859078
	0xac, 0x5e, 0xbd, 0x89, 0x41, 0xbd, 0x24, 0x47, //0x00005250 .quad 5126430365035880108
	0xec, 0x3f, 0x37, 0x9a, 0xb5, 0x98, 0xdf, 0x8e, //0x00005258 .quad -8151628894773493780
	0x57, 0xb6, 0x2c, 0xec, 0x91, 0xec, 0xed, 0x58, //0x00005260 .quad 6408037956294850135
	0xe7, 0x0f, 0xc5, 0x00, 0xe3, 0x7e, 0x97, 0xb2, //0x00005268 .quad -5577850100039479321
	0xed, 0xe3, 0x37, 0x67, 0xb6, 0x67, 0x29, 0x2f, //0x00005270 .quad 3398361426941174765
	0xe1, 0x53, 0xf6, 0xc0, 0x9b, 0x5e, 0x3d, 0xdf, //0x00005278 .quad -2360626606621961247
	0x74, 0xee, 0x82, 0x00, 0xd2, 0xe0, 0x79, 0xbd, //0x00005280 .quad -4793553135802847628
	0x6c, 0xf4, 0x99, 0x58, 0x21, 0x5b, 0x86, 0x8b, //0x00005288 .quad -8392920656779807636
	0x11, 0xaa, 0xa3, 0x80, 0x06, 0x59, 0xd8, 0xec, //0x00005290 .quad -1380255401326171631
	0x87, 0x71, 0xc0, 0xae, 0xe9, 0xf1, 0x67, 0xae, //0x00005298 .quad -5879464802547371641
	0x95, 0x94, 0xcc, 0x20, 0x48, 0x6f, 0x0e, 0xe8, //0x000052a0 .quad -1725319251657714539
	0xe9, 0x8d, 0x70, 0x1a, 0x64, 0xee, 0x01, 0xda, //0x000052a8 .quad -2737644984756826647
	0xdd, 0xdc, 0x7f, 0x14, 0x8d, 0x05, 0x09, 0x31, //0x000052b0 .quad 3533361486141316317
	0xb2, 0x58, 0x86, 0x90, 0xfe, 0x34, 0x41, 0x88, //0x000052b8 .quad -8628557143114098510
	0x15, 0xd4, 0x9f, 0x59, 0xf0, 0x46, 0x4b, 0xbd, //0x000052c0 .quad -4806670179178130411
	0xde, 0xee, 0xa7, 0x34, 0x3e, 0x82, 0x51, 0xaa, //0x000052c8 .quad -6174010410465235234
	0x1a, 0xc9, 0x07, 0x70, 0xac, 0x18, 0x9e, 0x6c, //0x000052d0 .quad 7826720331309500698
	0x96, 0xea, 0xd1, 0xc1, 0xcd, 0xe2, 0xe5, 0xd4, //0x000052d8 .quad -3105826994654156138
	0xb0, 0xdd, 0x04, 0xc6, 0x6b, 0xcf, 0xe2, 0x03, //0x000052e0 .quad 280014188641050032
	0x9e, 0x32, 0x23, 0x99, 0xc0, 0xad, 0x0f, 0x85, //0x000052e8 .quad -8858670899299929442
	0x1c, 0x15, 0x86, 0xb7, 0x46, 0x83, 0xdb, 0x84, //0x000052f0 .quad -8873354301053463268
	0x45, 0xff, 0x6b, 0xbf, 0x30, 0x99, 0x53, 0xa6, //0x000052f8 .quad -6461652605697523899
	0x63, 0x9a, 0x67, 0x65, 0x18, 0x64, 0x12, 0xe6, //0x00005300 .quad -1868320839462053277
	0x16, 0xff, 0x46, 0xef, 0x7c, 0x7f, 0xe8, 0xcf, //0x00005308 .quad -3465379738694516970
	0x7e, 0xc0, 0x60, 0x3f, 0x8f, 0x7e, 0xcb, 0x4f, //0x00005310 .quad 5749828502977298558
	0x6e, 0x5f, 0x8c, 0x15, 0xae, 0x4f, 0xf1, 0x81, //0x00005318 .quad -9083391364325154962
	0x9d, 0xf0, 0x38, 0x0f, 0x33, 0x5e, 0xbe, 0xe3, //0x00005320 .quad -2036086408133152611
	0x49, 0x77, 0xef, 0x9a, 0x99, 0xa3, 0x6d, 0xa2, //0x00005328 .quad -6742553186979055799
	0xc5, 0x2c, 0x07, 0xd3, 0xbf, 0xf5, 0xad, 0x5c, //0x00005330 .quad 6678264026688335045
	0x1c, 0x55, 0xab, 0x01, 0x80, 0x0c, 0x09, 0xcb, //0x00005338 .quad -3816505465296431844
	0xf6, 0xf7, 0xc8, 0xc7, 0x2f, 0x73, 0xd9, 0x73, //0x00005340 .quad 8347830033360418806
	0x63, 0x2a, 0x16, 0x02, 0xa0, 0x4f, 0xcb, 0xfd, //0x00005348 .quad -158945813193151901
	0xfa, 0x9a, 0xdd, 0xdc, 0xfd, 0xe7, 0x67, 0x28, //0x00005350 .quad 2911550761636567802
	0x7e, 0xda, 0x4d, 0x01, 0xc4, 0x11, 0x9f, 0x9e, //0x00005358 .quad -7016870160886801794
	0xb8, 0x01, 0x15, 0x54, 0xfd, 0xe1, 0x81, 0xb2, //0x00005360 .quad -5583933584809066056
	0x1d, 0x51, 0xa1, 0x01, 0x35, 0xd6, 0x46, 0xc6, //0x00005368 .quad -4159401682681114339
	0x26, 0x42, 0x1a, 0xa9, 0x7c, 0x5a, 0x22, 0x1f, //0x00005370 .quad 2243455055843443238
	0x65, 0xa5, 0x09, 0x42, 0xc2, 0x8b, 0xd8, 0xf7, //0x00005378 .quad -587566084924005019
	0x58, 0x69, 0xb0, 0xe9, 0x8d, 0x78, 0x75, 0x33, //0x00005380 .quad 3708002419115845976
	0x5f, 0x07, 0x46, 0x69, 0x59, 0x57, 0xe7, 0x9a, //0x00005388 .quad -7284757830718584993
	0xae, 0x83, 0x1c, 0x64, 0xb1, 0xd6, 0x52, 0x00, //0x00005390 .quad 23317005467419566
	0x37, 0x89, 0x97, 0xc3, 0x2f, 0x2d, 0xa1, 0xc1, //0x00005398 .quad -4494261269970843337
	0x9a, 0xa4, 0x23, 0xbd, 0x5d, 0x8c, 0x67, 0xc0, //0x000053a0 .quad -4582539761593113446
	0x84, 0x6b, 0x7d, 0xb4, 0x7b, 0x78, 0x09, 0xf2, //0x000053a8 .quad -1006140569036166268
	0xe0, 0x46, 0x36, 0x96, 0xba, 0xb7, 0x40, 0xf8, //0x000053b0 .quad -558244341782001952
	0x32, 0x63, 0xce, 0x50, 0x4d, 0xeb, 0x45, 0x97, //0x000053b8 .quad -7546366883288685774
	0x98, 0xd8, 0xc3, 0x3b, 0xa9, 0xe5, 0x50, 0xb6, //0x000053c0 .quad -5309491445654890344
	0xff, 0xfb, 0x01, 0xa5, 0x20, 0x66, 0x17, 0xbd, //0x000053c8 .quad -4821272585683469313
	0xbe, 0xce, 0xb4, 0x8a, 0x13, 0x1f, 0xe5, 0xa3, //0x000053d0 .quad -6636864307068612930
	0xff, 0x7a, 0x42, 0xce, 0xa8, 0x3f, 0x5d, 0xec, //0x000053d8 .quad -1414904713676948737
	0x37, 0x01, 0xb1, 0x36, 0x6c, 0x33, 0x6f, 0xc6, //0x000053e0 .quad -4148040191917883081
	0xdf, 0x8c, 0xe9, 0x80, 0xc9, 0x47, 0xba, 0x93, //0x000053e8 .quad -7801844473689174817
	0x84, 0x41, 0x5d, 0x44, 0x47, 0x00, 0x0b, 0xb8, //0x000053f0 .quad -5185050239897353852
	0x17, 0xf0, 0x23, 0xe1, 0xbb, 0xd9, 0xa8, 0xb8, //0x000053f8 .quad -5140619573684080617
	0xe5, 0x91, 0x74, 0x15, 0x59, 0xc0, 0x0d, 0xa6, //0x00005400 .quad -6481312799871692315
	0x1d, 0xec, 0x6c, 0xd9, 0x2a, 0x10, 0xd3, 0xe6, //0x00005408 .quad -1814088448677712867
	0x2f, 0xdb, 0x68, 0xad, 0x37, 0x98, 0xc8, 0x87, //0x00005410 .quad -8662506518347195601
	0x92, 0x13, 0xe4, 0xc7, 0x1a, 0xea, 0x43, 0x90, //0x00005418 .quad -8051334308064652398
	0xfb, 0x11, 0xc3, 0x98, 0x45, 0xbe, 0xba, 0x29, //0x00005420 .quad 3006924907348169211
	0x77, 0x18, 0xdd, 0x79, 0xa1, 0xe4, 0x54, 0xb4, //0x00005428 .quad -5452481866653427593
	0x7a, 0xd6, 0xf3, 0xfe, 0xd6, 0x6d, 0x29, 0xf4, //0x00005430 .quad -853029884242176390
	0x94, 0x5e, 0x54, 0xd8, 0xc9, 0x1d, 0x6a, 0xe1, //0x00005438 .quad -2203916314889396588
	0x0c, 0x66, 0x58, 0x5f, 0xa6, 0xe4, 0x99, 0x18, //0x00005440 .quad 1772699331562333708
	0x1d, 0xbb, 0x34, 0x27, 0x9e, 0x52, 0xe2, 0x8c, //0x00005448 .quad -8294976724446954723
	0x8f, 0x7f, 0x2e, 0xf7, 0xcf, 0x5d, 0xc0, 0x5e, //0x00005450 .quad 6827560182880305039
	0xe4, 0xe9, 0x01, 0xb1, 0x45, 0xe7, 0x1a, 0xb0, //0x00005458 .quad -5757034887131305500
	0x73, 0x1f, 0xfa, 0xf4, 0x43, 0x75, 0x70, 0x76, //0x00005460 .quad 8534450228600381299
	0x5d, 0x64, 0x42, 0x1d, 0x17, 0xa1, 0x21, 0xdc, //0x00005468 .quad -2584607590486743971
	0xa8, 0x53, 0x1c, 0x79, 0x4a, 0x49, 0x06, 0x6a, //0x00005470 .quad 7639874402088932264
	0xba, 0x7e, 0x49, 0x72, 0xae, 0x04, 0x95, 0x89, //0x00005478 .quad -8532908771695296838
	0x92, 0x68, 0x63, 0x17, 0x9d, 0xdb, 0x87, 0x04, //0x00005480 .quad 326470965756389522
	0x69, 0xde, 0xdb, 0x0e, 0xda, 0x45, 0xfa, 0xab, //0x00005488 .quad -6054449946191733143
	0xb6, 0x42, 0x3c, 0x5d, 0x84, 0xd2, 0xa9, 0x45, //0x00005490 .quad 5019774725622874806
	0x03, 0xd6, 0x92, 0x92, 0x50, 0xd7, 0xf8, 0xd6, //0x00005498 .quad -2956376414312278525
	0xb2, 0xa9, 0x45, 0xba, 0x92, 0x23, 0x8a, 0x0b, //0x000054a0 .quad 831516194300602802
	0xc2, 0xc5, 0x9b, 0x5b, 0x92, 0x86, 0x5b, 0x86, //0x000054a8 .quad -8765264286586255934
	0x1e, 0x14, 0xd7, 0x68, 0x77, 0xac, 0x6c, 0x8e, //0x000054b0 .quad -8183976793979022306
	0x32, 0xb7, 0x82, 0xf2, 0x36, 0x68, 0xf2, 0xa7, //0x000054b8 .quad -6344894339805432014
	0x26, 0xd9, 0x0c, 0x43, 0x95, 0xd7, 0x07, 0x32, //0x000054c0 .quad 3605087062808385830
	0xff, 0x64, 0x23, 0xaf, 0x44, 0x02, 0xef, 0xd1, //0x000054c8 .quad -3319431906329402113
	0xb8, 0x07, 0xe8, 0x49, 0xbd, 0xe6, 0x44, 0x7f, //0x000054d0 .quad 9170708441896323000
	0x1f, 0x1f, 0x76, 0xed, 0x6a, 0x61, 0x35, 0x83, //0x000054d8 .quad -8992173969096958177
	0xa6, 0x09, 0x62, 0x9c, 0x6c, 0x20, 0x16, 0x5f, //0x000054e0 .quad 6851699533943015846
	0xe7, 0xa6, 0xd3, 0xa8, 0xc5, 0xb9, 0x02, 0xa4, //0x000054e8 .quad -6628531442943809817
	0x0f, 0x8c, 0x7a, 0xc3, 0x87, 0xa8, 0xdb, 0x36, //0x000054f0 .quad 3952938399001381903
	0xa1, 0x90, 0x08, 0x13, 0x37, 0x68, 0x03, 0xcd, //0x000054f8 .quad -3673978285252374367
	0x89, 0x97, 0x2c, 0xda, 0x54, 0x49, 0x49, 0xc2, //0x00005500 .quad -4446942528265218167
	0x64, 0x5a, 0xe5, 0x6b, 0x22, 0x21, 0x22, 0x80, //0x00005508 .quad -9213765455923815836
	0x6c, 0xbd, 0xb7, 0x10, 0xaa, 0x9b, 0xdb, 0xf2, //0x00005510 .quad -946992141904134804
	0xfd, 0xb0, 0xde, 0x06, 0x6b, 0xa9, 0x2a, 0xa0, //0x00005518 .quad -6905520801477381891
	0xc7, 0xac, 0xe5, 0x94, 0x94, 0x82, 0x92, 0x6f, //0x00005520 .quad 8039631859474607303
	0x3d, 0x5d, 0x96, 0xc8, 0xc5, 0x53, 0x35, 0xc8, //0x00005528 .quad -4020214983419339459
	0xf9, 0x17, 0x1f, 0xba, 0x39, 0x23, 0x77, 0xcb, //0x00005530 .quad -3785518230938904583
	0x8c, 0xf4, 0xbb, 0x3a, 0xb7, 0xa8, 0x42, 0xfa, //0x00005538 .quad -413582710846786420
	0xfb, 0x6e, 0x53, 0x14, 0x04, 0x76, 0x2a, 0xff, //0x00005540 .quad -60105885123121413
	0xd7, 0x78, 0xb5, 0x84, 0x72, 0xa9, 0x69, 0x9c, //0x00005548 .quad -7176018221920323369
	0xba, 0x4a, 0x68, 0x19, 0x85, 0x13, 0xf5, 0xfe, //0x00005550 .quad -75132356403901766
	0x0d, 0xd7, 0xe2, 0x25, 0xcf, 0x13, 0x84, 0xc3, //0x00005558 .quad -4358336758973016307
	0x69, 0x5d, 0xc2, 0x5f, 0x66, 0x58, 0xb2, 0x7e, //0x00005560 .quad 9129456591349898601
	0xd1, 0x8c, 0x5b, 0xef, 0xc2, 0x18, 0x65, 0xf4, //0x00005568 .quad -836234930288882479
	0x61, 0x7a, 0xd9, 0xfb, 0x3f, 0x77, 0x2f, 0xef, //0x00005570 .quad -1211618658047395231
	0x02, 0x38, 0x99, 0xd5, 0x79, 0x2f, 0xbf, 0x98, //0x00005578 .quad -7440175859071633406
	0xfa, 0xd8, 0xcf, 0xfa, 0x0f, 0x55, 0xfb, 0xaa, //0x00005580 .quad -6126209340986631942
	0x03, 0x86, 0xff, 0x4a, 0x58, 0xfb, 0xee, 0xbe, //0x00005588 .quad -4688533805412153853
	0x38, 0xcf, 0x83, 0xf9, 0x53, 0x2a, 0xba, 0x95, //0x00005590 .quad -7657761676233289928
	0x84, 0x67, 0xbf, 0x5d, 0x2e, 0xba, 0xaa, 0xee, //0x00005598 .quad -1248981238337804412
	0x83, 0x61, 0xf2, 0x7b, 0x74, 0x5a, 0x94, 0xdd, //0x000055a0 .quad -2480258038432112253
	0xb2, 0xa0, 0x97, 0xfa, 0x5c, 0xb4, 0x2a, 0x95, //0x000055a8 .quad -7698142301602209614
	0xe4, 0xf9, 0xee, 0x9a, 0x11, 0x71, 0xf9, 0x94, //0x000055b0 .quad -7712008566467528220
	0xdf, 0x88, 0x3d, 0x39, 0x74, 0x61, 0x75, 0xba, //0x000055b8 .quad -5010991858575374113
	0x5d, 0xb8, 0xaa, 0x01, 0x56, 0xcd, 0x37, 0x7a, //0x000055c0 .quad 8806733365625141341
	0x17, 0xeb, 0x8c, 0x47, 0xd1, 0xb9, 0x12, 0xe9, //0x000055c8 .quad -1652053804791829737
	0x3a, 0xb3, 0x0a, 0xc1, 0x55, 0xe0, 0x62, 0xac, //0x000055d0 .quad -6025006692552756422
	0xee, 0x12, 0xb8, 0xcc, 0x22, 0xb4, 0xab, 0x91, //0x000055d8 .quad -7950062655635975442
	0x09, 0x60, 0x4d, 0x31, 0x6b, 0x98, 0x7b, 0x57, //0x000055e0 .quad 6303799689591218185
	0xaa, 0x17, 0xe6, 0x7f, 0x2b, 0xa1, 0x16, 0xb6, //0x000055e8 .quad -5325892301117581398
	0x0b, 0xb8, 0xa0, 0xfd, 0x85, 0x7e, 0x5a, 0xed, //0x000055f0 .quad -1343622424865753077
	0x94, 0x9d, 0xdf, 0x5f, 0x76, 0x49, 0x9c, 0xe3, //0x000055f8 .quad -2045679357969588844
	0x07, 0x73, 0x84, 0xbe, 0x13, 0x8f, 0x58, 0x14, //0x00005600 .quad 1466078993672598279
	0x7d, 0xc2, 0xeb, 0xfb, 0xe9, 0xad, 0x41, 0x8e, //0x00005608 .quad -8196078626372074883
	0xc8, 0x8f, 0x25, 0xae, 0xd8, 0xb2, 0x6e, 0x59, //0x00005610 .quad 6444284760518135752
	0x1c, 0xb3, 0xe6, 0x7a, 0x64, 0x19, 0xd2, 0xb1, //0x00005618 .quad -5633412264537705700
	0xbb, 0xf3, 0xae, 0xd9, 0x8e, 0x5f, 0xca, 0x6f, //0x00005620 .quad 8055355950647669691
	0xe3, 0x5f, 0xa0, 0x99, 0xbd, 0x9f, 0x46, 0xde, //0x00005628 .quad -2430079312244744221
	0x54, 0x58, 0x0d, 0x48, 0xb9, 0x7b, 0xde, 0x25, //0x00005630 .quad 2728754459941099604
	0xee, 0x3b, 0x04, 0x80, 0xd6, 0x23, 0xec, 0x8a, //0x00005638 .quad -8436328597794046994
	0x6a, 0xae, 0x10, 0x9a, 0xa7, 0x1a, 0x56, 0xaf, //0x00005640 .quad -5812428961928401302
	0xe9, 0x4a, 0x05, 0x20, 0xcc, 0x2c, 0xa7, 0xad, //0x00005648 .quad -5933724728815170839
	0x04, 0xda, 0x94, 0x80, 0x51, 0xa1, 0x2b, 0x1b, //0x00005650 .quad 1957835834444274180
	0xa4, 0x9d, 0x06, 0x28, 0xff, 0xf7, 0x10, 0xd9, //0x00005658 .quad -2805469892591575644
	0x42, 0x08, 0x5d, 0xf0, 0xd2, 0x44, 0xfb, 0x90, //0x00005660 .quad -7999724640327104446
	0x86, 0x22, 0x04, 0x79, 0xff, 0x9a, 0xaa, 0x87, //0x00005668 .quad -8670947710510816634
	0x53, 0x4a, 0x74, 0xac, 0x07, 0x16, 0x3a, 0x35, //0x00005670 .quad 3835402254873283155
	0x28, 0x2b, 0x45, 0x57, 0xbf, 0x41, 0x95, 0xa9, //0x00005678 .quad -6226998619711132888
	0xe8, 0x5c, 0x91, 0x97, 0x89, 0x9b, 0x88, 0x42, //0x00005680 .quad 4794252818591603944
	0xf2, 0x75, 0x16, 0x2d, 0x2f, 0x92, 0xfa, 0xd3, //0x00005688 .quad -3172062256211528206
	0x11, 0xda, 0xba, 0xfe, 0x35, 0x61, 0x95, 0x69, //0x00005690 .quad 7608094030047140369
	0xb7, 0x09, 0x2e, 0x7c, 0x5d, 0x9b, 0x7c, 0x84, //0x00005698 .quad -8900067937773286985
	0x95, 0x90, 0x69, 0x7e, 0x83, 0xb9, 0xfa, 0x43, //0x000056a0 .quad 4898431519131537557
	0x25, 0x8c, 0x39, 0xdb, 0x34, 0xc2, 0x9b, 0xa5, //0x000056a8 .quad -6513398903789220827
	0xbb, 0xf4, 0x03, 0x5e, 0xe4, 0x67, 0xf9, 0x94, //0x000056b0 .quad -7712018656367741765
	0x2e, 0xef, 0x07, 0x12, 0xc2, 0xb2, 0x02, 0xcf, //0x000056b8 .quad -3530062611309138130
	0xf5, 0x78, 0xc2, 0xba, 0xee, 0xe0, 0x1b, 0x1d, //0x000056c0 .quad 2097517367411243253
	0x7d, 0xf5, 0x44, 0x4b, 0xb9, 0xaf, 0x61, 0x81, //0x000056c8 .quad -9123818159709293187
	0x32, 0x17, 0x73, 0x69, 0x2a, 0xd9, 0x62, 0x64, //0x000056d0 .quad 7233582727691441970
	0xdc, 0x32, 0x16, 0x9e, 0xa7, 0x1b, 0xba, 0xa1, //0x000056d8 .quad -6793086681209228580
	0xfe, 0xdc, 0xcf, 0x03, 0x75, 0x8f, 0x7b, 0x7d, //0x000056e0 .quad 9041978409614302462
	0x93, 0xbf, 0x9b, 0x85, 0x91, 0xa2, 0x28, 0xca, //0x000056e8 .quad -3879672333084147821
	0x3e, 0xd4, 0xc3, 0x44, 0x52, 0x73, 0xda, 0x5c, //0x000056f0 .quad 6690786993590490174
	0x78, 0xaf, 0x02, 0xe7, 0x35, 0xcb, 0xb2, 0xfc, //0x000056f8 .quad -237904397927796872
	0xa7, 0x64, 0xfa, 0x6a, 0x13, 0x88, 0x08, 0x3a, //0x00005700 .quad 4181741870994056359
	0xab, 0xad, 0x61, 0xb0, 0x01, 0xbf, 0xef, 0x9d, //0x00005708 .quad -7066219276345954901
	0xd0, 0xfd, 0xb8, 0x45, 0x18, 0xaa, 0x8a, 0x08, //0x00005710 .quad 615491320315182544
	0x16, 0x19, 0x7a, 0x1c, 0xc2, 0xae, 0x6b, 0xc5, //0x00005718 .quad -4221088077005055722
	0x45, 0x3d, 0x27, 0x57, 0x9e, 0x54, 0xad, 0x8a, //0x00005720 .quad -8454007886460797627
	0x5b, 0x9f, 0x98, 0xa3, 0x72, 0x9a, 0xc6, 0xf6, //0x00005728 .quad -664674077828931749
	0x4b, 0x86, 0x78, 0xf6, 0xe2, 0x54, 0xac, 0x36, //0x00005730 .quad 3939617107816777291
	0x99, 0x63, 0x3f, 0xa6, 0x87, 0x20, 0x3c, 0x9a, //0x00005738 .quad -7332950326284164199
	0xdd, 0xa7, 0x16, 0xb4, 0x1b, 0x6a, 0x57, 0x84, //0x00005740 .quad -8910536670511192099
	0x7f, 0x3c, 0xcf, 0x8f, 0xa9, 0x28, 0xcb, 0xc0, //0x00005748 .quad -4554501889427817345
	0xd5, 0x51, 0x1c, 0xa1, 0xa2, 0x44, 0x6d, 0x65, //0x00005750 .quad 7308573235570561493
	0x9f, 0x0b, 0xc3, 0xf3, 0xd3, 0xf2, 0xfd, 0xf0, //0x00005758 .quad -1081441343357383777
	0x25, 0xb3, 0xb1, 0xa4, 0xe5, 0x4a, 0x64, 0x9f, //0x00005760 .quad -6961356773836868827
	0x43, 0xe7, 0x59, 0x78, 0xc4, 0xb7, 0x9e, 0x96, //0x00005768 .quad -7593429867239446717
	0xee, 0x1f, 0xde, 0x0d, 0x9f, 0x5d, 0x3d, 0x87, //0x00005770 .quad -8701695967296086034
	0x14, 0x61, 0x70, 0x96, 0xb5, 0x65, 0x46, 0xbc, //0x00005778 .quad -4880101315621920492
	0xea, 0xa7, 0x55, 0xd1, 0x06, 0xb5, 0x0c, 0xa9, //0x00005780 .quad -6265433940692719638
	0x59, 0x79, 0x0c, 0xfc, 0x22, 0xff, 0x57, 0xeb, //0x00005788 .quad -1488440626100012711
	0xf2, 0x88, 0xd5, 0x42, 0x24, 0xf1, 0xa7, 0x09, //0x00005790 .quad 695789805494438130
	0xd8, 0xcb, 0x87, 0xdd, 0x75, 0xff, 0x16, 0x93, //0x00005798 .quad -7847804418953589800
	0x2f, 0xeb, 0x8a, 0x53, 0x6d, 0xed, 0x11, 0x0c, //0x000057a0 .quad 869737256868047663
	0xce, 0xbe, 0xe9, 0x54, 0x53, 0xbf, 0xdc, 0xb7, //0x000057a8 .quad -5198069505264599346
	0xfa, 0xa5, 0x6d, 0xa8, 0xc8, 0x68, 0x16, 0x8f, //0x000057b0 .quad -8136200465769716230
	0x81, 0x2e, 0x24, 0x2a, 0x28, 0xef, 0xd3, 0xe5, //0x000057b8 .quad -1885900863153361279
	0xbc, 0x87, 0x44, 0x69, 0x7d, 0x01, 0x6e, 0xf9, //0x000057c0 .quad -473439272678684740
	0x10, 0x9d, 0x56, 0x1a, 0x79, 0x75, 0xa4, 0x8f, //0x000057c8 .quad -8096217067111932656
	0xac, 0xa9, 0x95, 0xc3, 0xdc, 0x81, 0xc9, 0x37, //0x000057d0 .quad 4019886927579031980
	0x55, 0x44, 0xec, 0x60, 0xd7, 0x92, 0x8d, 0xb3, //0x000057d8 .quad -5508585315462527915
	0x17, 0x14, 0x7b, 0xf4, 0x53, 0xe2, 0xbb, 0x85, //0x000057e0 .quad -8810199395808373737
	0x6a, 0x55, 0x27, 0x39, 0x8d, 0xf7, 0x70, 0xe0, //0x000057e8 .quad -2274045625900771990
	0x8e, 0xec, 0xcc, 0x78, 0x74, 0x6d, 0x95, 0x93, //0x000057f0 .quad -7812217631593927538
	0x62, 0x95, 0xb8, 0x43, 0xb8, 0x9a, 0x46, 0x8c, //0x000057f8 .quad -8338807543829064350
	0xb2, 0x27, 0x00, 0x97, 0xd1, 0xc8, 0x7a, 0x38, //0x00005800 .quad 4069786015789754290
	0xbb, 0xba, 0xa6, 0x54, 0x66, 0x41, 0x58, 0xaf, //0x00005808 .quad -5811823411358942533
	0x9e, 0x31, 0xc0, 0xfc, 0x05, 0x7b, 0x99, 0x06, //0x00005810 .quad 475546501309804958
	0x6a, 0x69, 0xd0, 0xe9, 0xbf, 0x51, 0x2e, 0xdb, //0x00005818 .quad -2653093245771290262
	0x03, 0x1f, 0xf8, 0xbd, 0xe3, 0xec, 0x1f, 0x44, //0x00005820 .quad 4908902581746016003
	0xe2, 0x41, 0x22, 0xf2, 0x17, 0xf3, 0xfc, 0x88, //0x00005828 .quad -8575712306248138270
	0xc3, 0x26, 0x76, 0xad, 0x1c, 0xe8, 0x27, 0xd5, //0x00005830 .quad -3087243809672255805
	0x5a, 0xd2, 0xaa, 0xee, 0xdd, 0x2f, 0x3c, 0xab, //0x00005838 .quad -6107954364382784934
	0x74, 0xb0, 0xd3, 0xd8, 0x23, 0xe2, 0x71, 0x8a, //0x00005840 .quad -8470740780517707660
	0xf1, 0x86, 0x55, 0x6a, 0xd5, 0x3b, 0x0b, 0xd6, //0x00005848 .quad -3023256937051093263
	0x49, 0x4e, 0x84, 0x67, 0x56, 0x2d, 0x87, 0xf6, //0x00005850 .quad -682526969396179383
	0x56, 0x74, 0x75, 0x62, 0x65, 0x05, 0xc7, 0x85, //0x00005858 .quad -8807064613298015146
	0xdb, 0x61, 0x65, 0x01, 0xac, 0xf8, 0x28, 0xb4, //0x00005860 .quad -5464844730172612133
	0x6c, 0xd1, 0x12, 0xbb, 0xbe, 0xc6, 0x38, 0xa7, //0x00005868 .quad -6397144748195131028
	0x52, 0xba, 0xbe, 0x01, 0xd7, 0x36, 0x33, 0xe1, //0x00005870 .quad -2219369894288377262
	0xc7, 0x85, 0xd7, 0x69, 0x6e, 0xf8, 0x06, 0xd1, //0x00005878 .quad -3384744916816525881
	0x73, 0x34, 0x17, 0x61, 0x46, 0x02, 0xc0, 0xec, //0x00005880 .quad -1387106183930235789
	0x9c, 0xb3, 0x26, 0x02, 0x45, 0x5b, 0xa4, 0x82, //0x00005888 .quad -9032994600651410532
	0x90, 0x01, 0x5d, 0xf9, 0xd7, 0x02, 0xf0, 0x27, //0x00005890 .quad 2877803288514593168
	0x84, 0x60, 0xb0, 0x42, 0x16, 0x72, 0x4d, 0xa3, //0x00005898 .quad -6679557232386875260
	0xf4, 0x41, 0xb4, 0xf7, 0x8d, 0x03, 0xec, 0x31, //0x000058a0 .quad 3597254110643241460
	0xa5, 0x78, 0x5c, 0xd3, 0x9b, 0xce, 0x20, 0xcc, //0x000058a8 .quad -3737760522056206171
	0x71, 0x52, 0xa1, 0x75, 0x71, 0x04, 0x67, 0x7e, //0x000058b0 .quad 9108253656731439729
	0xce, 0x96, 0x33, 0xc8, 0x42, 0x02, 0x29, 0xff, //0x000058b8 .quad -60514634142869810
	0x86, 0xd3, 0x84, 0xe9, 0xc6, 0x62, 0x00, 0x0f, //0x000058c0 .quad 1080972517029761926
	0x41, 0x3e, 0x20, 0xbd, 0x69, 0xa1, 0x79, 0x9f, //0x000058c8 .quad -6955350673980375487
	0x68, 0x08, 0xe6, 0xa3, 0x78, 0x7b, 0xc0, 0x52, //0x000058d0 .quad 5962901664714590312
	0xd1, 0x4d, 0x68, 0x2c, 0xc4, 0x09, 0x58, 0xc7, //0x000058d8 .quad -4082502324048081455
	0x82, 0x8a, 0xdf, 0xcc, 0x56, 0x9a, 0x70, 0xa7, //0x000058e0 .quad -6381430974388925822
	0x45, 0x61, 0x82, 0x37, 0x35, 0x0c, 0x2e, 0xf9, //0x000058e8 .quad -491441886632713915
	0x91, 0xb6, 0x0b, 0x40, 0x76, 0x60, 0xa6, 0x88, //0x000058f0 .quad -8600080377420466543
	0xcb, 0x7c, 0xb1, 0x42, 0xa1, 0xc7, 0xbc, 0x9b, //0x000058f8 .quad -7224680206786528053
	0x35, 0xa4, 0x0e, 0xd0, 0x93, 0xf8, 0xcf, 0x6a, //0x00005900 .quad 7696643601933968437
	0xfe, 0xdb, 0x5d, 0x93, 0x89, 0xf9, 0xab, 0xc2, //0x00005908 .quad -4419164240055772162
	0x43, 0x4d, 0x12, 0xc4, 0xb8, 0xf6, 0x83, 0x05, //0x00005910 .quad 397432465562684739
	0xfe, 0x52, 0x35, 0xf8, 0xeb, 0xf7, 0x56, 0xf3, //0x00005918 .quad -912269281642327298
	0x4a, 0x70, 0x8b, 0x7a, 0x33, 0x7a, 0x72, 0xc3, //0x00005920 .quad -4363290727450709942
	0xde, 0x53, 0x21, 0x7b, 0xf3, 0x5a, 0x16, 0x98, //0x00005928 .quad -7487697328667536418
	0x5c, 0x4c, 0x2e, 0x59, 0xc0, 0x18, 0x4f, 0x74, //0x00005930 .quad 8380944645968776284
	0xd6, 0xa8, 0xe9, 0x59, 0xb0, 0xf1, 0x1b, 0xbe, //0x00005938 .quad -4747935642407032618
	0x73, 0xdf, 0x79, 0x6f, 0xf0, 0xde, 0x62, 0x11, //0x00005940 .quad 1252808770606194547
	0x0c, 0x13, 0x64, 0x70, 0x1c, 0xee, 0xa2, 0xed, //0x00005948 .quad -1323233534581402868
	0xa8, 0x2b, 0xac, 0x45, 0x56, 0xcb, 0xdd, 0x8a, //0x00005950 .quad -8440366555225904216
	0xe7, 0x8b, 0x3e, 0xc6, 0xd1, 0xd4, 0x85, 0x94, //0x00005958 .quad -7744549986754458649
	0x92, 0x36, 0x17, 0xd7, 0x2b, 0x3e, 0x95, 0x6d, //0x00005960 .quad 7896285879677171346
	0xe1, 0x2e, 0xce, 0x37, 0x06, 0x4a, 0xa7, 0xb9, //0x00005968 .quad -5069001465015685407
	0x37, 0x04, 0xdd, 0xcc, 0xb6, 0x8d, 0xfa, 0xc8, //0x00005970 .quad -3964700705685699529
	0x99, 0xba, 0xc1, 0xc5, 0x87, 0x1c, 0x11, 0xe8, //0x00005978 .quad -1724565812842218855
	0xa2, 0x22, 0x0a, 0x40, 0x92, 0x98, 0x9c, 0x1d, //0x00005980 .quad 2133748077373825698
	0xa0, 0x14, 0x99, 0xdb, 0xd4, 0xb1, 0x0a, 0x91, //0x00005988 .quad -7995382660667468640
	0x4b, 0xab, 0x0c, 0xd0, 0xb6, 0xbe, 0x03, 0x25, //0x00005990 .quad 2667185096717282123
	0xc8, 0x59, 0x7f, 0x12, 0x4a, 0x5e, 0x4d, 0xb5, //0x00005998 .quad -5382542307406947896
	0x1d, 0xd6, 0x0f, 0x84, 0x64, 0xae, 0x44, 0x2e, //0x000059a0 .quad 3333981370896602653
	0x3a, 0x30, 0x1f, 0x97, 0xdc, 0xb5, 0xa0, 0xe2, //0x000059a8 .quad -2116491865831296966
	0xd2, 0xe5, 0x89, 0xd2, 0xfe, 0xec, 0xea, 0x5c, //0x000059b0 .quad 6695424375237764562
	0x24, 0x7e, 0x73, 0xde, 0xa9, 0x71, 0xa4, 0x8d, //0x000059b8 .quad -8240336443785642460
	0x47, 0x5f, 0x2c, 0x87, 0x3e, 0xa8, 0x25, 0x74, //0x000059c0 .quad 8369280469047205703
	0xad, 0x5d, 0x10, 0x56, 0x14, 0x8e, 0x0d, 0xb1, //0x000059c8 .quad -5688734536304665171
	0x19, 0x77, 0xf7, 0x28, 0x4e, 0x12, 0x2f, 0xd1, //0x000059d0 .quad -3373457468973156583
	0x18, 0x75, 0x94, 0x6b, 0x99, 0xf1, 0x50, 0xdd, //0x000059d8 .quad -2499232151953443560
	0x6f, 0xaa, 0x9a, 0xd9, 0x70, 0x6b, 0xbd, 0x82, //0x000059e0 .quad -9025939945749304721
	0x2f, 0xc9, 0x3c, 0xe3, 0xff, 0x96, 0x52, 0x8a, //0x000059e8 .quad -8479549122611984081
	0x0b, 0x55, 0x01, 0x10, 0x4d, 0xc6, 0x6c, 0x63, //0x000059f0 .quad 7164319141522920715
	0x7b, 0xfb, 0x0b, 0xdc, 0xbf, 0x3c, 0xe7, 0xac, //0x000059f8 .quad -5987750384837592197
	0x4e, 0xaa, 0x01, 0x54, 0xe0, 0xf7, 0x47, 0x3c, //0x00005a00 .quad 4343712908476262990
	0x5a, 0xfa, 0x0e, 0xd3, 0xef, 0x0b, 0x21, 0xd8, //0x00005a08 .quad -2873001962619602342
	0x71, 0x0a, 0x81, 0x34, 0xec, 0xfa, 0xac, 0x65, //0x00005a10 .quad 7326506586225052273
	0x78, 0x5c, 0xe9, 0xe3, 0x75, 0xa7, 0x14, 0x87, //0x00005a18 .quad -8713155254278333320
	0x0d, 0x4d, 0xa1, 0x41, 0xa7, 0x39, 0x18, 0x7f, //0x00005a20 .quad 9158133232781315341
	0x96, 0xb3, 0xe3, 0x5c, 0x53, 0xd1, 0xd9, 0xa8, //0x00005a28 .quad -6279758049420528746
	0x50, 0xa0, 0x09, 0x12, 0x11, 0x48, 0xde, 0x1e, //0x00005a30 .quad 2224294504121868368
	0x7c, 0xa0, 0x1c, 0x34, 0xa8, 0x45, 0x10, 0xd3, //0x00005a38 .quad -3238011543348273028
	0x32, 0x04, 0x46, 0xab, 0x0a, 0xed, 0x4a, 0x93, //0x00005a40 .quad -7833187971778608078
	0x4d, 0xe4, 0x91, 0x20, 0x89, 0x2b, 0xea, 0x83, //0x00005a48 .quad -8941286242233752499
	0x3f, 0x85, 0x17, 0x56, 0x4d, 0xa8, 0x1d, 0xf8, //0x00005a50 .quad -568112927868484289
	0x60, 0x5d, 0xb6, 0x68, 0x6b, 0xb6, 0xe4, 0xa4, //0x00005a58 .quad -6564921784364802720
	0x8e, 0x66, 0x9d, 0xab, 0x60, 0x12, 0x25, 0x36, //0x00005a60 .quad 3901544858591782542
	0xb9, 0xf4, 0xe3, 0x42, 0x06, 0xe4, 0x1d, 0xce, //0x00005a68 .quad -3594466212028615495
	0x19, 0x60, 0x42, 0x6b, 0x7c, 0x2b, 0xd7, 0xc1, //0x00005a70 .quad -4479063491021217767
	0xf3, 0x78, 0xce, 0xe9, 0x83, 0xae, 0xd2, 0x80, //0x00005a78 .quad -9164070410158966541
	0x1f, 0xf8, 0x12, 0x86, 0x5b, 0xf6, 0x4c, 0xb2, //0x00005a80 .quad -5598829363776522209
	0x30, 0x17, 0x42, 0xe4, 0x24, 0x5a, 0x07, 0xa1, //0x00005a88 .quad -6843401994271320272
	0x27, 0xb6, 0x97, 0x67, 0xf2, 0x33, 0xe0, 0xde, //0x00005a90 .quad -2386850686293264857
	0xfc, 0x9c, 0x52, 0x1d, 0xae, 0x30, 0x49, 0xc9, //0x00005a98 .quad -3942566474411762436
	0xb1, 0xa3, 0x7d, 0x01, 0xef, 0x40, 0x98, 0x16, //0x00005aa0 .quad 1628122660560806833
	0x3c, 0x44, 0xa7, 0xa4, 0xd9, 0x7c, 0x9b, 0xfb, //0x00005aa8 .quad -316522074587315140
	0x4e, 0x86, 0xee, 0x60, 0x95, 0x28, 0x1f, 0x8e, //0x00005ab0 .quad -8205795374004271538
	0xa5, 0x8a, 0xe8, 0x06, 0x08, 0x2e, 0x41, 0x9d, //0x00005ab8 .quad -7115355324258153819
	0xe2, 0x27, 0x2a, 0xb9, 0xba, 0xf2, 0xa6, 0xf1, //0x00005ac0 .quad -1033872180650563614
	0x4e, 0xad, 0xa2, 0x08, 0x8a, 0x79, 0x91, 0xc4, //0x00005ac8 .quad -4282508136895304370
	0xdb, 0xb1, 0x74, 0x67, 0x69, 0xaf, 0x10, 0xae, //0x00005ad0 .quad -5904026244240592421
	0xa2, 0x58, 0xcb, 0x8a, 0xec, 0xd7, 0xb5, 0xf5, //0x00005ad8 .quad -741449152691742558
	0x29, 0xef, 0xa8, 0xe0, 0xa1, 0x6d, 0xca, 0xac, //0x00005ae0 .quad -5995859411864064215
	0x65, 0x17, 0xbf, 0xd6, 0xf3, 0xa6, 0x91, 0x99, //0x00005ae8 .quad -7380934748073420955
	0xf3, 0x2a, 0xd3, 0x58, 0x0a, 0x09, 0xfd, 0x17, //0x00005af0 .quad 1728547772024695539
	0x3f, 0xdd, 0x6e, 0xcc, 0xb0, 0x10, 0xf6, 0xbf, //0x00005af8 .quad -4614482416664388289
	0xb0, 0xf5, 0x07, 0xef, 0x4c, 0x4b, 0xfc, 0xdd, //0x00005b00 .quad -2451001303396518480
	0x8e, 0x94, 0x8a, 0xff, 0xdc, 0x94, 0xf3, 0xef, //0x00005b08 .quad -1156417002403097458
	0x8e, 0xf9, 0x64, 0x15, 0x10, 0xaf, 0xbd, 0x4a, //0x00005b10 .quad 5385653213018257806
	0xd9, 0x9c, 0xb6, 0x1f, 0x0a, 0x3d, 0xf8, 0x95, //0x00005b18 .quad -7640289654143017767
	0xf1, 0x37, 0xbe, 0x1a, 0xd4, 0x1a, 0x6d, 0x9d, //0x00005b20 .quad -7102991539009341455
	0x0f, 0x44, 0xa4, 0xa7, 0x4c, 0x4c, 0x76, 0xbb, //0x00005b28 .quad -4938676049251384305
	0xed, 0xc5, 0x6d, 0x21, 0x89, 0x61, 0xc8, 0x84, //0x00005b30 .quad -8878739423761676819
	0x13, 0x55, 0x8d, 0xd1, 0x5f, 0xdf, 0x53, 0xea, //0x00005b38 .quad -1561659043136842477
	0xb4, 0x9b, 0xe4, 0xb4, 0xf5, 0x3c, 0xfd, 0x32, //0x00005b40 .quad 3674159897003727796
	0x2c, 0x55, 0xf8, 0xe2, 0x9b, 0x6b, 0x74, 0x92, //0x00005b48 .quad -7893565929601608404
	0xa1, 0xc2, 0x1d, 0x22, 0x33, 0x8c, 0xbc, 0x3f, //0x00005b50 .quad 4592699871254659745
	0x77, 0x6a, 0xb6, 0xdb, 0x82, 0x86, 0x11, 0xb7, //0x00005b58 .quad -5255271393574622601
	0x4a, 0x33, 0xa5, 0xea, 0x3f, 0xaf, 0xab, 0x0f, //0x00005b60 .quad 1129188820640936778
	0x15, 0x05, 0xa4, 0x92, 0x23, 0xe8, 0xd5, 0xe4, //0x00005b68 .quad -1957403223540890347
	0x0e, 0x40, 0xa7, 0xf2, 0x87, 0x4d, 0xcb, 0x29, //0x00005b70 .quad 3011586022114279438
	0x2d, 0x83, 0xa6, 0x3b, 0x16, 0xb1, 0x05, 0x8f, //0x00005b78 .quad -8140906042354138323
	0x12, 0x10, 0x51, 0xef, 0xe9, 0x20, 0x3e, 0x74, //0x00005b80 .quad 8376168546070237202
	0xf8, 0x23, 0x90, 0xca, 0x5b, 0x1d, 0xc7, 0xb2, //0x00005b88 .quad -5564446534515285000
	0x16, 0x54, 0x25, 0x6b, 0x24, 0xa9, 0x4d, 0x91, //0x00005b90 .quad -7976533391121755114
	0xf6, 0x2c, 0x34, 0xbd, 0xb2, 0xe4, 0x78, 0xdf, //0x00005b98 .quad -2343872149716718346
	0x8e, 0x54, 0xf7, 0xc2, 0xb6, 0x89, 0xd0, 0x1a, //0x00005ba0 .quad 1932195658189984910
	0x1a, 0x9c, 0x40, 0xb6, 0xef, 0x8e, 0xab, 0x8b, //0x00005ba8 .quad -8382449121214030822
	0xb1, 0x29, 0xb5, 0x73, 0x24, 0xac, 0x84, 0xa1, //0x00005bb0 .quad -6808127464117294671
	0x20, 0xc3, 0xd0, 0xa3, 0xab, 0x72, 0x96, 0xae, //0x00005bb8 .quad -5866375383090150624
	0x1e, 0x74, 0xa2, 0x90, 0x2d, 0xd7, 0xe5, 0xc9, //0x00005bc0 .quad -3898473311719230434
	0xe8, 0xf3, 0xc4, 0x8c, 0x56, 0x0f, 0x3c, 0xda, //0x00005bc8 .quad -2721283210435300376
	0x92, 0x88, 0x65, 0x7a, 0x7c, 0xa6, 0x2f, 0x7e, //0x00005bd0 .quad 9092669226243950738
	0x71, 0x18, 0xfb, 0x17, 0x96, 0x89, 0x65, 0x88, //0x00005bd8 .quad -8618331034163144591
	0xb7, 0xea, 0xfe, 0x98, 0x1b, 0x90, 0xbb, 0xdd, //0x00005be0 .quad -2469221522477225289
	0x8d, 0xde, 0xf9, 0x9d, 0xfb, 0xeb, 0x7e, 0xaa, //0x00005be8 .quad -6161227774276542835
	0x65, 0xa5, 0x3e, 0x7f, 0x22, 0x74, 0x2a, 0x55, //0x00005bf0 .quad 6136845133758244197
	0x31, 0x56, 0x78, 0x85, 0xfa, 0xa6, 0x1e, 0xd5, //0x00005bf8 .quad -3089848699418290639
	0x5f, 0x27, 0x87, 0x8f, 0x95, 0x88, 0x3a, 0xd5, //0x00005c00 .quad -3082000819042179233
	0xde, 0x35, 0x6b, 0x93, 0x5c, 0x28, 0x33, 0x85, //0x00005c08 .quad -8848684464777513506
	0x37, 0xf1, 0x68, 0xf3, 0xba, 0x2a, 0x89, 0x8a, //0x00005c10 .quad -8464187042230111945
	0x56, 0x03, 0x46, 0xb8, 0x73, 0xf2, 0x7f, 0xa6, //0x00005c18 .quad -6449169562544503978
	0x85, 0x2d, 0x43, 0xb0, 0x69, 0x75, 0x2b, 0x2d, //0x00005c20 .quad 3254824252494523781
	0x2c, 0x84, 0x57, 0xa6, 0x10, 0xef, 0x1f, 0xd0, //0x00005c28 .quad -3449775934753242068
	0x73, 0xfc, 0x29, 0x0e, 0x62, 0x29, 0x3b, 0x9c, //0x00005c30 .quad -7189106879045698445
	0x9b, 0xb2, 0xf6, 0x67, 0x6a, 0xf5, 0x13, 0x82, //0x00005c38 .quad -9073638986861858149
	0x8f, 0x7b, 0xb4, 0x91, 0xba, 0xf3, 0x49, 0x83, //0x00005c40 .quad -8986383598807123057
	0x42, 0x5f, 0xf4, 0x01, 0xc5, 0xf2, 0x98, 0xa2, //0x00005c48 .quad -6730362715149934782
	0x73, 0x9a, 0x21, 0x36, 0xa9, 0x70, 0x1c, 0x24, //0x00005c50 .quad 2602078556773259891
	0x13, 0x77, 0x71, 0x42, 0x76, 0x2f, 0x3f, 0xcb, //0x00005c58 .quad -3801267375510030573
	0x10, 0x01, 0xaa, 0x83, 0xd3, 0x8c, 0x23, 0xed, //0x00005c60 .quad -1359087822460813040
	0xd7, 0xd4, 0x0d, 0xd3, 0x53, 0xfb, 0x0e, 0xfe, //0x00005c68 .quad -139898200960150313
	0xaa, 0x40, 0x4a, 0x32, 0x04, 0x38, 0x36, 0xf4, //0x00005c70 .quad -849429889038008150
	0x06, 0xa5, 0xe8, 0x63, 0x14, 0x5d, 0xc9, 0x9e, //0x00005c78 .quad -7004965403241175802
	0xd5, 0xd0, 0xdc, 0x3e, 0x05, 0xc6, 0x43, 0xb1, //0x00005c80 .quad -5673473379724898091
	0x48, 0xce, 0xe2, 0x7c, 0x59, 0xb4, 0x7b, 0xc6, //0x00005c88 .quad -4144520735624081848
	0x0a, 0x05, 0x94, 0x8e, 0x86, 0xb7, 0x94, 0xdd, //0x00005c90 .quad -2480155706228734710
	0xda, 0x81, 0x1b, 0xdc, 0x6f, 0xa1, 0x1a, 0xf8, //0x00005c98 .quad -568964901102714406
	0x26, 0x83, 0x1c, 0x19, 0xb4, 0xf2, 0x7c, 0xca, //0x00005ca0 .quad -3855940325606653146
	0x28, 0x31, 0x91, 0xe9, 0xe5, 0xa4, 0x10, 0x9b, //0x00005ca8 .quad -7273132090830278360
	0xf0, 0xa3, 0x63, 0x1f, 0x61, 0x2f, 0x1c, 0xfd, //0x00005cb0 .quad -208239388580928528
	0x72, 0x7d, 0xf5, 0x63, 0x1f, 0xce, 0xd4, 0xc1, //0x00005cb8 .quad -4479729095110460046
	0xec, 0x8c, 0x3c, 0x67, 0x39, 0x3b, 0x63, 0xbc, //0x00005cc0 .quad -4871985254153548564
	0xcf, 0xdc, 0xf2, 0x3c, 0xa7, 0x01, 0x4a, 0xf2, //0x00005cc8 .quad -987975350460687153
	0x13, 0xd8, 0x85, 0xe0, 0x03, 0x05, 0xbe, 0xd5, //0x00005cd0 .quad -3044990783845967853
	0x01, 0xca, 0x17, 0x86, 0x08, 0x41, 0x6e, 0x97, //0x00005cd8 .quad -7535013621679011327
	0x18, 0x4e, 0xa7, 0xd8, 0x44, 0x86, 0x2d, 0x4b, //0x00005ce0 .quad 5417133557047315992
	0x82, 0xbc, 0x9d, 0xa7, 0x4a, 0xd1, 0x49, 0xbd, //0x00005ce8 .quad -4807081008671376254
	0x9e, 0x21, 0xd1, 0x0e, 0xd6, 0xe7, 0xf8, 0xdd, //0x00005cf0 .quad -2451955090545630818
	0xa2, 0x2b, 0x85, 0x51, 0x9d, 0x45, 0x9c, 0xec, //0x00005cf8 .quad -1397165242411832414
	0x03, 0xb5, 0x42, 0xc9, 0xe5, 0x90, 0xbb, 0xca, //0x00005d00 .quad -3838314940804713213
	0x45, 0x3b, 0xf3, 0x52, 0x82, 0xab, 0xe1, 0x93, //0x00005d08 .quad -7790757304148477115
	0x43, 0x62, 0x93, 0x3b, 0x1f, 0x75, 0x6a, 0x3d, //0x00005d10 .quad 4425478360848884291
	0x17, 0x0a, 0xb0, 0xe7, 0x62, 0x16, 0xda, 0xb8, //0x00005d18 .quad -5126760611758208489
	0xd4, 0x3a, 0x78, 0x0a, 0x67, 0x12, 0xc5, 0x0c, //0x00005d20 .quad 920161932633717460
	0x9d, 0x0c, 0x9c, 0xa1, 0xfb, 0x9b, 0x10, 0xe7, //0x00005d28 .quad -1796764746270372707
	0xc5, 0x24, 0x8b, 0x66, 0x80, 0x2b, 0xfb, 0x27, //0x00005d30 .quad 2880944217109767365
	0xe2, 0x87, 0x01, 0x45, 0x7d, 0x61, 0x6a, 0x90, //0x00005d38 .quad -8040506994060064798
	0xf6, 0xed, 0x2d, 0x80, 0x60, 0xf6, 0xf9, 0xb1, //0x00005d40 .quad -5622191765467566602
	0xda, 0xe9, 0x41, 0x96, 0xdc, 0xf9, 0x84, 0xb4, //0x00005d48 .quad -5438947724147693094
	0x73, 0x69, 0x39, 0xa0, 0xf8, 0x73, 0x78, 0x5e, //0x00005d50 .quad 6807318348447705459
	0x51, 0x64, 0xd2, 0xbb, 0x53, 0x38, 0xa6, 0xe1, //0x00005d58 .quad -2186998636757228463
	0xe8, 0xe1, 0x23, 0x64, 0x7b, 0x48, 0x0b, 0xdb, //0x00005d60 .quad -2662955059861265944
	0xb2, 0x7e, 0x63, 0x55, 0x34, 0xe3, 0x07, 0x8d, //0x00005d68 .quad -8284403175614349646
	0x62, 0xda, 0x2c, 0x3d, 0x9a, 0x1a, 0xce, 0x91, //0x00005d70 .quad -7940379843253970334
	0x5f, 0x5e, 0xbc, 0x6a, 0x01, 0xdc, 0x49, 0xb0, //0x00005d78 .quad -5743817951090549153
	0xfb, 0x10, 0x78, 0xcc, 0x40, 0xa1, 0x41, 0x76, //0x00005d80 .quad 8521269269642088699
	0xf7, 0x75, 0x6b, 0xc5, 0x01, 0x53, 0x5c, 0xdc, //0x00005d88 .quad -2568086420435798537
	0x9d, 0x0a, 0xcb, 0x7f, 0xc8, 0x04, 0xe9, 0xa9, //0x00005d90 .quad -6203421752542164323
	0xba, 0x29, 0x63, 0x1b, 0xe1, 0xb3, 0xb9, 0x89, //0x00005d98 .quad -8522583040413455942
	0x44, 0xcd, 0xbd, 0x9f, 0xfa, 0x45, 0x63, 0x54, //0x00005da0 .quad 6080780864604458308
	0x29, 0xf4, 0x3b, 0x62, 0xd9, 0x20, 0x28, 0xac, //0x00005da8 .quad -6041542782089432023
	0x95, 0x40, 0xad, 0x47, 0x79, 0x17, 0x7c, 0xa9, //0x00005db0 .quad -6234081974526590827
	0x33, 0xf1, 0xca, 0xba, 0x0f, 0x29, 0x32, 0xd7, //0x00005db8 .quad -2940242459184402125
	0x5d, 0x48, 0xcc, 0xcc, 0xab, 0x8e, 0xed, 0x49, //0x00005dc0 .quad 5327070802775656541
	0xc0, 0xd6, 0xbe, 0xd4, 0xa9, 0x59, 0x7f, 0x86, //0x00005dc8 .quad -8755180564631333184
	0x74, 0x5a, 0xff, 0xbf, 0x56, 0xf2, 0x68, 0x5c, //0x00005dd0 .quad 6658838503469570676
	0x70, 0x8c, 0xee, 0x49, 0x14, 0x30, 0x1f, 0xa8, //0x00005dd8 .quad -6332289687361778576
	0x11, 0x31, 0xff, 0x6f, 0xec, 0x2e, 0x83, 0x73, //0x00005de0 .quad 8323548129336963345
	0x8c, 0x2f, 0x6a, 0x5c, 0x19, 0xfc, 0x26, 0xd2, //0x00005de8 .quad -3303676090774835316
	0xab, 0x7e, 0xff, 0xc5, 0x53, 0xfd, 0x31, 0xc8, //0x00005df0 .quad -4021154456019173717
	0xb7, 0x5d, 0xc2, 0xd9, 0x8f, 0x5d, 0x58, 0x83, //0x00005df8 .quad -8982326584375353929
	0x55, 0x5e, 0x7f, 0xb7, 0xa8, 0x7c, 0x3e, 0xba, //0x00005e00 .quad -5026443070023967147
	0x25, 0xf5, 0x32, 0xd0, 0xf3, 0x74, 0x2e, 0xa4, //0x00005e08 .quad -6616222212041804507
	0xeb, 0x35, 0x5f, 0xe5, 0xd2, 0x1b, 0xce, 0x28, //0x00005e10 .quad 2940318199324816875
	0x6f, 0xb2, 0x3f, 0xc4, 0x30, 0x12, 0x3a, 0xcd, //0x00005e18 .quad -3658591746624867729
	0xb3, 0x81, 0x5b, 0xcf, 0x63, 0xd1, 0x80, 0x79, //0x00005e20 .quad 8755227902219092403
	0x85, 0xcf, 0xa7, 0x7a, 0x5e, 0x4b, 0x44, 0x80, //0x00005e28 .quad -9204148869281624187
	0x1f, 0x62, 0x32, 0xc3, 0xbc, 0x05, 0xe1, 0xd7, //0x00005e30 .quad -2891023177508298209
	0x66, 0xc3, 0x51, 0x19, 0x36, 0x5e, 0x55, 0xa0, //0x00005e38 .quad -6893500068174642330
	0xa7, 0xfa, 0xfe, 0xf3, 0x2b, 0x47, 0xd9, 0x8d, //0x00005e40 .quad -8225464990312760665
	0x40, 0x34, 0xa6, 0x9f, 0xc3, 0xb5, 0x6a, 0xc8, //0x00005e48 .quad -4005189066790915008
	0x51, 0xb9, 0xfe, 0xf0, 0xf6, 0x98, 0x4f, 0xb1, //0x00005e50 .quad -5670145219463562927
	0x50, 0xc1, 0x8f, 0x87, 0x34, 0x63, 0x85, 0xfa, //0x00005e58 .quad -394800315061255856
	0xd3, 0x33, 0x9f, 0x56, 0x9a, 0xbf, 0xd1, 0x6e, //0x00005e60 .quad 7985374283903742931
	0xd2, 0xd8, 0xb9, 0xd4, 0x00, 0x5e, 0x93, 0x9c, //0x00005e68 .quad -7164279224554366766
	0xc8, 0x00, 0x47, 0xec, 0x80, 0x2f, 0x86, 0x0a, //0x00005e70 .quad 758345818024902856
	0x07, 0x4f, 0xe8, 0x09, 0x81, 0x35, 0xb8, 0xc3, //0x00005e78 .quad -4343663012265570553
	0xfa, 0xc0, 0x58, 0x27, 0x61, 0xbb, 0x27, 0xcd, //0x00005e80 .quad -3663753745896259334
	0xc8, 0x62, 0x62, 0x4c, 0xe1, 0x42, 0xa6, 0xf4, //0x00005e88 .quad -817892746904575288
	0x9c, 0x78, 0x97, 0xb8, 0x1c, 0xd5, 0x38, 0x80, //0x00005e90 .quad -9207375118826243940
	0xbd, 0x7d, 0xbd, 0xcf, 0xcc, 0xe9, 0xe7, 0x98, //0x00005e98 .quad -7428711994456441411
	0xc3, 0x56, 0xbd, 0xe6, 0x63, 0x0a, 0x47, 0xe0, //0x00005ea0 .quad -2285846861678029117
	0x2c, 0xdd, 0xac, 0x03, 0x40, 0xe4, 0x21, 0xbf, //0x00005ea8 .quad -4674203974643163860
	0x74, 0xac, 0x6c, 0xe0, 0xfc, 0xcc, 0x58, 0x18, //0x00005eb0 .quad 1754377441329851508
	0x78, 0x14, 0x98, 0x04, 0x50, 0x5d, 0xea, 0xee, //0x00005eb8 .quad -1231068949876566920
	0xc8, 0xeb, 0x43, 0x0c, 0x1e, 0x80, 0x37, 0x0f, //0x00005ec0 .quad 1096485900831157192
	0xcb, 0x0c, 0xdf, 0x02, 0x52, 0x7a, 0x52, 0x95, //0x00005ec8 .quad -7686947121313936181
	0xba, 0xe6, 0x54, 0x8f, 0x25, 0x60, 0x05, 0xd3, //0x00005ed0 .quad -3241078642388441414
	0xfd, 0xcf, 0x96, 0x83, 0xe6, 0x18, 0xa7, 0xba, //0x00005ed8 .quad -4996997883215032323
	0x69, 0x20, 0x2a, 0xf3, 0x2e, 0xb8, 0xc6, 0x47, //0x00005ee0 .quad 5172023733869224041
	0xfd, 0x83, 0x7c, 0x24, 0x20, 0xdf, 0x50, 0xe9, //0x00005ee8 .quad -1634561335591402499
	0x41, 0x54, 0xfa, 0x57, 0x1d, 0x33, 0xdc, 0x4c, //0x00005ef0 .quad 5538357842881958977
	0x7e, 0xd2, 0xcd, 0x16, 0x74, 0x8b, 0xd2, 0x91, //0x00005ef8 .quad -7939129862385708418
	0x52, 0xe9, 0xf8, 0xad, 0xe4, 0x3f, 0x13, 0xe0, //0x00005f00 .quad -2300424733252327086
	0x1d, 0x47, 0x81, 0x1c, 0x51, 0x2e, 0x47, 0xb6, //0x00005f08 .quad -5312226309554747619
	0xa6, 0x23, 0x77, 0xd9, 0xdd, 0x0f, 0x18, 0x58, //0x00005f10 .quad 6347841120289366950
	0xe5, 0x98, 0xa1, 0x63, 0xe5, 0xf9, 0xd8, 0xe3, //0x00005f18 .quad -2028596868516046619
	0x48, 0x76, 0xea, 0xa7, 0xea, 0x09, 0x0f, 0x57, //0x00005f20 .quad 6273243709394548296
	0x8f, 0xff, 0x44, 0x5e, 0x2f, 0x9c, 0x67, 0x8e, //0x00005f28 .quad -8185402070463610993
	0xda, 0x13, 0xe5, 0x51, 0x65, 0xcc, 0xd2, 0x2c, //0x00005f30 .quad 3229868618315797466
	0x73, 0x3f, 0xd6, 0x35, 0x3b, 0x83, 0x01, 0xb2, //0x00005f38 .quad -5620066569652125837
	0xd1, 0x58, 0x5e, 0xa6, 0x7e, 0x7f, 0x07, 0xf8, //0x00005f40 .quad -574350245532641071
	0x4f, 0xcf, 0x4b, 0x03, 0x0a, 0xe4, 0x81, 0xde, //0x00005f48 .quad -2413397193637769393
	0x82, 0xf7, 0xfa, 0x27, 0xaf, 0xaf, 0x04, 0xfb, //0x00005f50 .quad -358968903457900670
	0x91, 0x61, 0x0f, 0x42, 0x86, 0x2e, 0x11, 0x8b, //0x00005f58 .quad -8425902273664687727
	0x63, 0xb5, 0xf9, 0xf1, 0x9a, 0xdb, 0xc5, 0x79, //0x00005f60 .quad 8774660907532399971
	0xf6, 0x39, 0x93, 0xd2, 0x27, 0x7a, 0xd5, 0xad, //0x00005f68 .quad -5920691823653471754
	0xbc, 0x22, 0x78, 0xae, 0x81, 0x52, 0x37, 0x18, //0x00005f70 .quad 1744954097560724156
	0x74, 0x08, 0x38, 0xc7, 0xb1, 0xd8, 0x4a, 0xd9, //0x00005f78 .quad -2789178761139451788
	0xb5, 0x15, 0x0b, 0x0d, 0x91, 0x93, 0x22, 0x8f, //0x00005f80 .quad -8132775725879323211
	0x48, 0x05, 0x83, 0x1c, 0x6f, 0xc7, 0xce, 0x87, //0x00005f88 .quad -8660765753353239224
	0x22, 0xdb, 0x4d, 0x50, 0x75, 0x38, 0xeb, 0xb2, //0x00005f90 .quad -5554283638921766110
	0x9a, 0xc6, 0xa3, 0xe3, 0x4a, 0x79, 0xc2, 0xa9, //0x00005f98 .quad -6214271173264161126
	0xeb, 0x51, 0x61, 0xa4, 0x92, 0x06, 0xa6, 0x5f, //0x00005fa0 .quad 6892203506629956075
	0x41, 0xb8, 0x8c, 0x9c, 0x9d, 0x17, 0x33, 0xd4, //0x00005fa8 .quad -3156152948152813503
	0x33, 0xd3, 0xbc, 0xa6, 0x1b, 0xc4, 0xc7, 0xdb, //0x00005fb0 .quad -2609901835997359309
	0x28, 0xf3, 0xd7, 0x81, 0xc2, 0xee, 0x9f, 0x84, //0x00005fb8 .quad -8890124620236590296
	0x00, 0x08, 0x6c, 0x90, 0x22, 0xb5, 0xb9, 0x12, //0x00005fc0 .quad 1349308723430688768
	0xf3, 0xef, 0x4d, 0x22, 0x73, 0xea, 0xc7, 0xa5, //0x00005fc8 .quad -6500969756868349965
	0x00, 0x0a, 0x87, 0x34, 0x6b, 0x22, 0x68, 0xd7, //0x00005fd0 .quad -2925050114139026944
	0xef, 0x6b, 0xe1, 0xea, 0x0f, 0xe5, 0x39, 0xcf, //0x00005fd8 .quad -3514526177658049553
	0x40, 0x66, 0xd4, 0x00, 0x83, 0x15, 0xa1, 0xe6, //0x00005fe0 .quad -1828156321336891840
	0x75, 0xe3, 0xcc, 0xf2, 0x29, 0x2f, 0x84, 0x81, //0x00005fe8 .quad -9114107888677362827
	0xd0, 0x7f, 0x09, 0xc1, 0xe3, 0x5a, 0x49, 0x60, //0x00005ff0 .quad 6938176635183661008
	0x53, 0x1c, 0x80, 0x6f, 0xf4, 0x3a, 0xe5, 0xa1, //0x00005ff8 .quad -6780948842419315629
	0xc4, 0xdf, 0x4b, 0xb1, 0x9c, 0xb1, 0x5b, 0x38, //0x00006000 .quad 4061034775552188356
	0x68, 0x23, 0x60, 0x8b, 0xb1, 0x89, 0x5e, 0xca, //0x00006008 .quad -3864500034596756632
	0xb5, 0xd7, 0x9e, 0xdd, 0x03, 0x9e, 0x72, 0x46, //0x00006010 .quad 5076293469440235445
	0x42, 0x2c, 0x38, 0xee, 0x1d, 0x2c, 0xf6, 0xfc, //0x00006018 .quad -218939024818557886
	0xd1, 0x46, 0x83, 0x6a, 0xc2, 0xa2, 0x07, 0x6c, //0x00006020 .quad 7784369436827535057
	0xa9, 0x1b, 0xe3, 0xb4, 0x92, 0xdb, 0x19, 0x9e, //0x00006028 .quad -7054365918152680535
	0x85, 0x18, 0x24, 0x05, 0x73, 0x8b, 0x09, 0xc7, //0x00006030 .quad -4104596259247744891
	0x93, 0xe2, 0x1b, 0x62, 0x77, 0x52, 0xa0, 0xc5, //0x00006038 .quad -4206271379263462765
	0xa7, 0x1e, 0x6d, 0xc6, 0x4f, 0xee, 0xcb, 0xb8, //0x00006040 .quad -5130745324059681113
	0x38, 0xdb, 0xa2, 0x3a, 0x15, 0x67, 0x08, 0xf7, //0x00006048 .quad -646153205651940552
	0x28, 0x33, 0x04, 0xdc, 0xf1, 0x74, 0x7f, 0x73, //0x00006050 .quad 8322499218531169064
	0x03, 0xc9, 0xa5, 0x44, 0x6d, 0x40, 0x65, 0x9a, //0x00006058 .quad -7321374781173544701
	0xf2, 0x3f, 0x05, 0x53, 0x2e, 0x52, 0x5f, 0x50, //0x00006060 .quad 5791438004736573426
	0x44, 0x3b, 0xcf, 0x95, 0x88, 0x90, 0xfe, 0xc0, //0x00006068 .quad -4540032458039542972
	0xef, 0x8f, 0xc6, 0xe7, 0xb9, 0x26, 0x77, 0x64, //0x00006070 .quad 7239297505920716783
	0x15, 0x0a, 0x43, 0xbb, 0xaa, 0x34, 0x3e, 0xf1, //0x00006078 .quad -1063354554122040811
	0xf5, 0x19, 0xdc, 0x30, 0x34, 0x78, 0xca, 0x5e, //0x00006080 .quad 6830403950414141941
	0x4d, 0xe6, 0x09, 0xb5, 0xea, 0xe0, 0xc6, 0x96, //0x00006088 .quad -7582125623967357363
	0x72, 0x20, 0x13, 0x3d, 0x41, 0x16, 0x7d, 0xb6, //0x00006090 .quad -5297053117264486286
	0xe0, 0x5f, 0x4c, 0x62, 0x25, 0x99, 0x78, 0xbc, //0x00006098 .quad -4865971011531808800
	0x8f, 0xe8, 0x57, 0x8c, 0xd1, 0x5b, 0x1c, 0xe4, //0x000060a0 .quad -2009630378153219953
	0xd8, 0x77, 0xdf, 0xba, 0x6e, 0xbf, 0x96, 0xeb, //0x000060a8 .quad -1470777745987373096
	0x59, 0xf1, 0xb6, 0xf7, 0x62, 0xb9, 0x91, 0x8e, //0x000060b0 .quad -8173548013986844327
	0xe7, 0xaa, 0xcb, 0x34, 0xa5, 0x37, 0x3e, 0x93, //0x000060b8 .quad -7836765118883190041
	0xb0, 0xad, 0xa4, 0xb5, 0xbb, 0x27, 0x36, 0x72, //0x000060c0 .quad 8229809056225996208
	0xa1, 0x95, 0xfe, 0x81, 0x8e, 0xc5, 0x0d, 0xb8, //0x000060c8 .quad -5184270380176599647
	0x1c, 0xd9, 0x0d, 0xa3, 0xaa, 0xb1, 0xc3, 0xce, //0x000060d0 .quad -3547796734999668452
	0x09, 0x3b, 0x7e, 0x22, 0xf2, 0x36, 0x11, 0xe6, //0x000060d8 .quad -1868651956793361655
	0xb1, 0xa7, 0xe8, 0xa5, 0x0a, 0x4f, 0x3a, 0x21, //0x000060e0 .quad 2394313059052595121
	0xe6, 0xe4, 0x8e, 0x55, 0x57, 0xc2, 0xca, 0x8f, //0x000060e8 .quad -8085436500636932890
	0x9d, 0xd1, 0x62, 0x4f, 0xcd, 0xe2, 0x88, 0xa9, //0x000060f0 .quad -6230480713039031907
	0x1f, 0x9e, 0xf2, 0x2a, 0xed, 0x72, 0xbd, 0xb3, //0x000060f8 .quad -5495109607368778209
	0x05, 0x86, 0x3b, 0xa3, 0x80, 0x1b, 0xeb, 0x93, //0x00006100 .quad -7788100891298789883
	0xa7, 0x45, 0xaf, 0x75, 0xa8, 0xcf, 0xac, 0xe0, //0x00006108 .quad -2257200990783584857
	0xc3, 0x33, 0x05, 0x66, 0x30, 0xf1, 0x72, 0xbc, //0x00006110 .quad -4867563057061743677
	0x88, 0x8b, 0x8d, 0x49, 0xc9, 0x01, 0x6c, 0x8c, //0x00006118 .quad -8328279646880822392
	0xb4, 0x80, 0x86, 0x7f, 0x7c, 0xad, 0x8f, 0xeb, //0x00006120 .quad -1472767802899791692
	0x6a, 0xee, 0xf0, 0x9b, 0x3b, 0x02, 0x87, 0xaf, //0x00006128 .quad -5798663540173640086
	0xe1, 0x20, 0x68, 0x9f, 0xdb, 0x98, 0x73, 0xa6, //0x00006130 .quad -6452645772052127519
	0x05, 0x2a, 0xed, 0x82, 0xca, 0xc2, 0x68, 0xdb, //0x00006138 .quad -2636643406789662203
	0x8c, 0x14, 0xa1, 0x43, 0x89, 0x3f, 0x08, 0x88, //0x00006140 .quad -8644589625959967604
	0x43, 0x3a, 0xd4, 0x91, 0xbe, 0x79, 0x21, 0x89, //0x00006148 .quad -8565431156884620733
	0xb0, 0x59, 0x89, 0x94, 0x6b, 0x4f, 0x0a, 0x6a, //0x00006150 .quad 7641007041259592112
	0xd4, 0x48, 0x49, 0x36, 0x2e, 0xd8, 0x69, 0xab, //0x00006158 .quad -6095102927678388012
	0x1c, 0xb0, 0xab, 0x79, 0x46, 0xe3, 0x8c, 0x84, //0x00006160 .quad -8895485272135061476
	0x09, 0x9b, 0xdb, 0xc3, 0x39, 0x4e, 0x44, 0xd6, //0x00006168 .quad -3007192641170597111
	0x11, 0x4e, 0x0b, 0x0c, 0x0c, 0x0e, 0xd8, 0xf2, //0x00006170 .quad -947992276657025519
	0xe5, 0x40, 0x69, 0x1a, 0xe4, 0xb0, 0xea, 0x85, //0x00006178 .quad -8797024428372705051
	0x95, 0x21, 0x0e, 0x0f, 0x8f, 0x11, 0x8e, 0x6f, //0x00006180 .quad 8038381691033493909
	0x1f, 0x91, 0x03, 0x21, 0x1d, 0x5d, 0x65, 0xa7, //0x00006188 .quad -6384594517038493409
	0xfb, 0xa9, 0xd1, 0xd2, 0xf2, 0x95, 0x71, 0x4b, //0x00006190 .quad 5436291095364479483
	0x67, 0x75, 0x44, 0x69, 0x64, 0xb4, 0x3e, 0xd1, //0x00006198 .quad -3369057127870728857
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061a0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	//0x000061b0 .p2align 4, 0x00
	//0x000061b0 _POW_TAB
	0x01, 0x00, 0x00, 0x00, //0x000061b0 .long 1
	0x03, 0x00, 0x00, 0x00, //0x000061b4 .long 3
	0x06, 0x00, 0x00, 0x00, //0x000061b8 .long 6
	0x09, 0x00, 0x00, 0x00, //0x000061bc .long 9
	0x0d, 0x00, 0x00, 0x00, //0x000061c0 .long 13
	0x10, 0x00, 0x00, 0x00, //0x000061c4 .long 16
	0x13, 0x00, 0x00, 0x00, //0x000061c8 .long 19
	0x17, 0x00, 0x00, 0x00, //0x000061cc .long 23
	0x1a, 0x00, 0x00, 0x00, //0x000061d0 .long 26
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061d4 .p2align 4, 0x00
	//0x000061e0 _LSHIFT_TAB
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061e0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000061f0 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006200 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006210 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006220 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006230 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .space 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006240 QUAD $0x0000000000000000  // .space 8, '\x00\x00\x00\x00\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006248 .long 1
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000624c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000625c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000626c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000627c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000628c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000629c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000062ac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x000062b0 .long 1
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062b4 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000062f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006304 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006314 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x01, 0x00, 0x00, 0x00, //0x00006318 .long 1
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000631c QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000632c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000633c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000634c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000635c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000636c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000637c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006380 .long 2
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006384 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006394 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000063e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x000063e8 .long 2
	0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063ec QUAD $0x0000000035323133; QUAD $0x0000000000000000  // .asciz 16, '3125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000063fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000640c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000641c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000642c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000643c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000644c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x02, 0x00, 0x00, 0x00, //0x00006450 .long 2
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006454 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006464 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006474 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006484 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006494 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000064b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x000064b8 .long 3
	0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064bc QUAD $0x0000003532313837; QUAD $0x0000000000000000  // .asciz 16, '78125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000064fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000650c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000651c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006520 .long 3
	0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006524 QUAD $0x0000353236303933; QUAD $0x0000000000000000  // .asciz 16, '390625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006534 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006544 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006554 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006564 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006574 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006584 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x03, 0x00, 0x00, 0x00, //0x00006588 .long 3
	0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000658c QUAD $0x0035323133353931; QUAD $0x0000000000000000  // .asciz 16, '1953125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000659c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000065ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000065f0 .long 4
	0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000065f4 QUAD $0x0035323635363739; QUAD $0x0000000000000000  // .asciz 16, '9765625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006604 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006614 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006624 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006634 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006644 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006654 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006658 .long 4
	0x34, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000665c QUAD $0x3532313832383834; QUAD $0x0000000000000000  // .asciz 16, '48828125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000666c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000667c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000668c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000669c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000066bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x000066c0 .long 4
	0x32, 0x34, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066c4 QUAD $0x3236303431343432; QUAD $0x0000000000000035  // .asciz 16, '244140625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000066f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006704 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006714 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006724 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x04, 0x00, 0x00, 0x00, //0x00006728 .long 4
	0x31, 0x32, 0x32, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000672c QUAD $0x3133303730323231; QUAD $0x0000000000003532  // .asciz 16, '1220703125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000673c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000674c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000675c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000676c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000677c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000678c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006790 .long 5
	0x36, 0x31, 0x30, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006794 QUAD $0x3635313533303136; QUAD $0x0000000000003532  // .asciz 16, '6103515625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000067f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x000067f8 .long 5
	0x33, 0x30, 0x35, 0x31, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000067fc QUAD $0x3837353731353033; QUAD $0x0000000000353231  // .asciz 16, '30517578125\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000680c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000681c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000682c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000683c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000684c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000685c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x05, 0x00, 0x00, 0x00, //0x00006860 .long 5
	0x31, 0x35, 0x32, 0x35, 0x38, 0x37, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00006864 QUAD $0x3938373835323531; QUAD $0x0000000035323630  // .asciz 16, '152587890625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006874 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006884 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006894 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000068c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x000068c8 .long 6
	0x37, 0x36, 0x32, 0x39, 0x33, 0x39, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x000068cc QUAD $0x3534393339323637; QUAD $0x0000000035323133  // .asciz 16, '762939453125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000068fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000690c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000691c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000692c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006930 .long 6
	0x33, 0x38, 0x31, 0x34, 0x36, 0x39, 0x37, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, //0x00006934 QUAD $0x3237393634313833; QUAD $0x0000003532363536  // .asciz 16, '3814697265625\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006944 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006954 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006964 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006974 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006984 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006994 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x06, 0x00, 0x00, 0x00, //0x00006998 .long 6
	0x31, 0x39, 0x30, 0x37, 0x33, 0x34, 0x38, 0x36, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, //0x0000699c QUAD $0x3638343337303931; QUAD $0x0000353231383233  // .asciz 16, '19073486328125\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000069ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000069fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006a00 .long 7
	0x39, 0x35, 0x33, 0x36, 0x37, 0x34, 0x33, 0x31, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00006a04 QUAD $0x3133343736333539; QUAD $0x0000353236303436  // .asciz 16, '95367431640625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006a64 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006a68 .long 7
	0x34, 0x37, 0x36, 0x38, 0x33, 0x37, 0x31, 0x35, 0x38, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, //0x00006a6c QUAD $0x3531373338363734; QUAD $0x0035323133303238  // .asciz 16, '476837158203125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006a9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006aac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006abc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006acc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006ad0 .long 7
	0x32, 0x33, 0x38, 0x34, 0x31, 0x38, 0x35, 0x37, 0x39, 0x31, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, //0x00006ad4 QUAD $0x3735383134383332; QUAD $0x3532363531303139  // .asciz 16, '2384185791015625'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ae4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006af4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b34 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x07, 0x00, 0x00, 0x00, //0x00006b38 .long 7
	0x31, 0x31, 0x39, 0x32, 0x30, 0x39, 0x32, 0x38, 0x39, 0x35, 0x35, 0x30, 0x37, 0x38, 0x31, 0x32, //0x00006b3c QUAD $0x3832393032393131; QUAD $0x3231383730353539  // .asciz 16, '1192092895507812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b4c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006b8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006b9c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006ba0 .long 8
	0x35, 0x39, 0x36, 0x30, 0x34, 0x36, 0x34, 0x34, 0x37, 0x37, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, //0x00006ba4 QUAD $0x3434363430363935; QUAD $0x3236303933353737  // .asciz 16, '5960464477539062'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bb4 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006be4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006bf4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006c04 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006c08 .long 8
	0x32, 0x39, 0x38, 0x30, 0x32, 0x33, 0x32, 0x32, 0x33, 0x38, 0x37, 0x36, 0x39, 0x35, 0x33, 0x31, //0x00006c0c QUAD $0x3232333230383932; QUAD $0x3133353936373833  // .asciz 16, '2980232238769531'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c1c QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c3c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c4c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c5c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006c6c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x08, 0x00, 0x00, 0x00, //0x00006c70 .long 8
	0x31, 0x34, 0x39, 0x30, 0x31, 0x31, 0x36, 0x31, 0x31, 0x39, 0x33, 0x38, 0x34, 0x37, 0x36, 0x35, //0x00006c74 QUAD $0x3136313130393431; QUAD $0x3536373438333931  // .asciz 16, '1490116119384765'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c84 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006c94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ca4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cb4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cc4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006cd4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006cd8 .long 9
	0x37, 0x34, 0x35, 0x30, 0x35, 0x38, 0x30, 0x35, 0x39, 0x36, 0x39, 0x32, 0x33, 0x38, 0x32, 0x38, //0x00006cdc QUAD $0x3530383530353437; QUAD $0x3832383332393639  // .asciz 16, '7450580596923828'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cec QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006cfc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006d3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006d40 .long 9
	0x33, 0x37, 0x32, 0x35, 0x32, 0x39, 0x30, 0x32, 0x39, 0x38, 0x34, 0x36, 0x31, 0x39, 0x31, 0x34, //0x00006d44 QUAD $0x3230393235323733; QUAD $0x3431393136343839  // .asciz 16, '3725290298461914'
	0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d54 QUAD $0x0000000035323630; QUAD $0x0000000000000000  // .asciz 16, '0625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006d94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006da4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x09, 0x00, 0x00, 0x00, //0x00006da8 .long 9
	0x31, 0x38, 0x36, 0x32, 0x36, 0x34, 0x35, 0x31, 0x34, 0x39, 0x32, 0x33, 0x30, 0x39, 0x35, 0x37, //0x00006dac QUAD $0x3135343632363831; QUAD $0x3735393033323934  // .asciz 16, '1862645149230957'
	0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dbc QUAD $0x0000003532313330; QUAD $0x0000000000000000  // .asciz 16, '03125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dcc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ddc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006dfc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006e0c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006e10 .long 10
	0x39, 0x33, 0x31, 0x33, 0x32, 0x32, 0x35, 0x37, 0x34, 0x36, 0x31, 0x35, 0x34, 0x37, 0x38, 0x35, //0x00006e14 QUAD $0x3735323233313339; QUAD $0x3538373435313634  // .asciz 16, '9313225746154785'
	0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e24 QUAD $0x0000003532363531; QUAD $0x0000000000000000  // .asciz 16, '15625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e44 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e54 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e64 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006e74 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006e78 .long 10
	0x34, 0x36, 0x35, 0x36, 0x36, 0x31, 0x32, 0x38, 0x37, 0x33, 0x30, 0x37, 0x37, 0x33, 0x39, 0x32, //0x00006e7c QUAD $0x3832313636353634; QUAD $0x3239333737303337  // .asciz 16, '4656612873077392'
	0x35, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e8c QUAD $0x0000353231383735; QUAD $0x0000000000000000  // .asciz 16, '578125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006e9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006eac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ebc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ecc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006edc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006ee0 .long 10
	0x32, 0x33, 0x32, 0x38, 0x33, 0x30, 0x36, 0x34, 0x33, 0x36, 0x35, 0x33, 0x38, 0x36, 0x39, 0x36, //0x00006ee4 QUAD $0x3436303338323332; QUAD $0x3639363833353633  // .asciz 16, '2328306436538696'
	0x32, 0x38, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ef4 QUAD $0x0035323630393832; QUAD $0x0000000000000000  // .asciz 16, '2890625\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f04 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f14 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f24 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f34 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006f44 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0a, 0x00, 0x00, 0x00, //0x00006f48 .long 10
	0x31, 0x31, 0x36, 0x34, 0x31, 0x35, 0x33, 0x32, 0x31, 0x38, 0x32, 0x36, 0x39, 0x33, 0x34, 0x38, //0x00006f4c QUAD $0x3233353134363131; QUAD $0x3834333936323831  // .asciz 16, '1164153218269348'
	0x31, 0x34, 0x34, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f5c QUAD $0x3532313335343431; QUAD $0x0000000000000000  // .asciz 16, '14453125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f6c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f7c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f8c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006f9c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00006fac LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00006fb0 .long 11
	0x35, 0x38, 0x32, 0x30, 0x37, 0x36, 0x36, 0x30, 0x39, 0x31, 0x33, 0x34, 0x36, 0x37, 0x34, 0x30, //0x00006fb4 QUAD $0x3036363730323835; QUAD $0x3034373634333139  // .asciz 16, '5820766091346740'
	0x37, 0x32, 0x32, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fc4 QUAD $0x3532363536323237; QUAD $0x0000000000000000  // .asciz 16, '72265625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fd4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006fe4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00006ff4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007004 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007014 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00007018 .long 11
	0x32, 0x39, 0x31, 0x30, 0x33, 0x38, 0x33, 0x30, 0x34, 0x35, 0x36, 0x37, 0x33, 0x33, 0x37, 0x30, //0x0000701c QUAD $0x3033383330313932; QUAD $0x3037333337363534  // .asciz 16, '2910383045673370'
	0x33, 0x36, 0x31, 0x33, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000702c QUAD $0x3231383233313633; QUAD $0x0000000000000035  // .asciz 16, '361328125\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000703c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000704c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000705c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000706c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000707c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0b, 0x00, 0x00, 0x00, //0x00007080 .long 11
	0x31, 0x34, 0x35, 0x35, 0x31, 0x39, 0x31, 0x35, 0x32, 0x32, 0x38, 0x33, 0x36, 0x36, 0x38, 0x35, //0x00007084 QUAD $0x3531393135353431; QUAD $0x3538363633383232  // .asciz 16, '1455191522836685'
	0x31, 0x38, 0x30, 0x36, 0x36, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007094 QUAD $0x3630343636303831; QUAD $0x0000000000003532  // .asciz 16, '1806640625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000070e4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x000070e8 .long 12
	0x37, 0x32, 0x37, 0x35, 0x39, 0x35, 0x37, 0x36, 0x31, 0x34, 0x31, 0x38, 0x33, 0x34, 0x32, 0x35, //0x000070ec QUAD $0x3637353935373237; QUAD $0x3532343338313431  // .asciz 16, '7275957614183425'
	0x39, 0x30, 0x33, 0x33, 0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000070fc QUAD $0x3133303233333039; QUAD $0x0000000000003532  // .asciz 16, '9033203125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000710c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000711c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000712c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000713c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000714c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x00007150 .long 12
	0x33, 0x36, 0x33, 0x37, 0x39, 0x37, 0x38, 0x38, 0x30, 0x37, 0x30, 0x39, 0x31, 0x37, 0x31, 0x32, //0x00007154 QUAD $0x3838373937333633; QUAD $0x3231373139303730  // .asciz 16, '3637978807091712'
	0x39, 0x35, 0x31, 0x36, 0x36, 0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007164 QUAD $0x3531303636313539; QUAD $0x0000000000353236  // .asciz 16, '95166015625\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007174 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007184 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007194 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000071b4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0c, 0x00, 0x00, 0x00, //0x000071b8 .long 12
	0x31, 0x38, 0x31, 0x38, 0x39, 0x38, 0x39, 0x34, 0x30, 0x33, 0x35, 0x34, 0x35, 0x38, 0x35, 0x36, //0x000071bc QUAD $0x3439383938313831; QUAD $0x3635383534353330  // .asciz 16, '1818989403545856'
	0x34, 0x37, 0x35, 0x38, 0x33, 0x30, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x000071cc QUAD $0x3730303338353734; QUAD $0x0000000035323138  // .asciz 16, '475830078125\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000071fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000720c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000721c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007220 .long 13
	0x39, 0x30, 0x39, 0x34, 0x39, 0x34, 0x37, 0x30, 0x31, 0x37, 0x37, 0x32, 0x39, 0x32, 0x38, 0x32, //0x00007224 QUAD $0x3037343934393039; QUAD $0x3238323932373731  // .asciz 16, '9094947017729282'
	0x33, 0x37, 0x39, 0x31, 0x35, 0x30, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, //0x00007234 QUAD $0x3933303531393733; QUAD $0x0000000035323630  // .asciz 16, '379150390625\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007244 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007254 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007264 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007274 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007284 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007288 .long 13
	0x34, 0x35, 0x34, 0x37, 0x34, 0x37, 0x33, 0x35, 0x30, 0x38, 0x38, 0x36, 0x34, 0x36, 0x34, 0x31, //0x0000728c QUAD $0x3533373437343534; QUAD $0x3134363436383830  // .asciz 16, '4547473508864641'
	0x31, 0x38, 0x39, 0x35, 0x37, 0x35, 0x31, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, //0x0000729c QUAD $0x3931353735393831; QUAD $0x0000003532313335  // .asciz 16, '1895751953125\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000072dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000072ec LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x000072f0 .long 13
	0x32, 0x32, 0x37, 0x33, 0x37, 0x33, 0x36, 0x37, 0x35, 0x34, 0x34, 0x33, 0x32, 0x33, 0x32, 0x30, //0x000072f4 QUAD $0x3736333733373232; QUAD $0x3032333233343435  // .asciz 16, '2273736754432320'
	0x35, 0x39, 0x34, 0x37, 0x38, 0x37, 0x35, 0x39, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, //0x00007304 QUAD $0x3935373837343935; QUAD $0x0000353236353637  // .asciz 16, '59478759765625\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007314 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007324 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007334 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007344 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007354 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0d, 0x00, 0x00, 0x00, //0x00007358 .long 13
	0x31, 0x31, 0x33, 0x36, 0x38, 0x36, 0x38, 0x33, 0x37, 0x37, 0x32, 0x31, 0x36, 0x31, 0x36, 0x30, //0x0000735c QUAD $0x3338363836333131; QUAD $0x3036313631323737  // .asciz 16, '1136868377216160'
	0x32, 0x39, 0x37, 0x33, 0x39, 0x33, 0x37, 0x39, 0x38, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, //0x0000736c QUAD $0x3937333933373932; QUAD $0x0035323138323838  // .asciz 16, '297393798828125\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000737c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000738c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000739c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000073bc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x000073c0 .long 14
	0x35, 0x36, 0x38, 0x34, 0x33, 0x34, 0x31, 0x38, 0x38, 0x36, 0x30, 0x38, 0x30, 0x38, 0x30, 0x31, //0x000073c4 QUAD $0x3831343334383635; QUAD $0x3130383038303638  // .asciz 16, '5684341886080801'
	0x34, 0x38, 0x36, 0x39, 0x36, 0x38, 0x39, 0x39, 0x34, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, //0x000073d4 QUAD $0x3939383639363834; QUAD $0x0035323630343134  // .asciz 16, '486968994140625\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000073f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007404 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007414 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007424 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007428 .long 14
	0x32, 0x38, 0x34, 0x32, 0x31, 0x37, 0x30, 0x39, 0x34, 0x33, 0x30, 0x34, 0x30, 0x34, 0x30, 0x30, //0x0000742c QUAD $0x3930373132343832; QUAD $0x3030343034303334  // .asciz 16, '2842170943040400'
	0x37, 0x34, 0x33, 0x34, 0x38, 0x34, 0x34, 0x39, 0x37, 0x30, 0x37, 0x30, 0x33, 0x31, 0x32, 0x35, //0x0000743c QUAD $0x3934343834333437; QUAD $0x3532313330373037  // .asciz 16, '7434844970703125'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000744c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000745c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000746c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000747c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000748c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0e, 0x00, 0x00, 0x00, //0x00007490 .long 14
	0x31, 0x34, 0x32, 0x31, 0x30, 0x38, 0x35, 0x34, 0x37, 0x31, 0x35, 0x32, 0x30, 0x32, 0x30, 0x30, //0x00007494 QUAD $0x3435383031323431; QUAD $0x3030323032353137  // .asciz 16, '1421085471520200'
	0x33, 0x37, 0x31, 0x37, 0x34, 0x32, 0x32, 0x34, 0x38, 0x35, 0x33, 0x35, 0x31, 0x35, 0x36, 0x32, //0x000074a4 QUAD $0x3432323437313733; QUAD $0x3236353135333538  // .asciz 16, '3717422485351562'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074b4 QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000074e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000074f4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000074f8 .long 15
	0x37, 0x31, 0x30, 0x35, 0x34, 0x32, 0x37, 0x33, 0x35, 0x37, 0x36, 0x30, 0x31, 0x30, 0x30, 0x31, //0x000074fc QUAD $0x3337323435303137; QUAD $0x3130303130363735  // .asciz 16, '7105427357601001'
	0x38, 0x35, 0x38, 0x37, 0x31, 0x31, 0x32, 0x34, 0x32, 0x36, 0x37, 0x35, 0x37, 0x38, 0x31, 0x32, //0x0000750c QUAD $0x3432313137383538; QUAD $0x3231383735373632  // .asciz 16, '8587112426757812'
	0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000751c QUAD $0x0000000000000035; QUAD $0x0000000000000000  // .asciz 16, '5\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000752c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000753c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000754c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000755c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x00007560 .long 15
	0x33, 0x35, 0x35, 0x32, 0x37, 0x31, 0x33, 0x36, 0x37, 0x38, 0x38, 0x30, 0x30, 0x35, 0x30, 0x30, //0x00007564 QUAD $0x3633313732353533; QUAD $0x3030353030383837  // .asciz 16, '3552713678800500'
	0x39, 0x32, 0x39, 0x33, 0x35, 0x35, 0x36, 0x32, 0x31, 0x33, 0x33, 0x37, 0x38, 0x39, 0x30, 0x36, //0x00007574 QUAD $0x3236353533393239; QUAD $0x3630393837333331  // .asciz 16, '9293556213378906'
	0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007584 QUAD $0x0000000000003532; QUAD $0x0000000000000000  // .asciz 16, '25\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007594 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000075c4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x0f, 0x00, 0x00, 0x00, //0x000075c8 .long 15
	0x31, 0x37, 0x37, 0x36, 0x33, 0x35, 0x36, 0x38, 0x33, 0x39, 0x34, 0x30, 0x30, 0x32, 0x35, 0x30, //0x000075cc QUAD $0x3836353336373731; QUAD $0x3035323030343933  // .asciz 16, '1776356839400250'
	0x34, 0x36, 0x34, 0x36, 0x37, 0x37, 0x38, 0x31, 0x30, 0x36, 0x36, 0x38, 0x39, 0x34, 0x35, 0x33, //0x000075dc QUAD $0x3138373736343634; QUAD $0x3335343938363630  // .asciz 16, '4646778106689453'
	0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075ec QUAD $0x0000000000353231; QUAD $0x0000000000000000  // .asciz 16, '125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000075fc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000760c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000761c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000762c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007630 .long 16
	0x38, 0x38, 0x38, 0x31, 0x37, 0x38, 0x34, 0x31, 0x39, 0x37, 0x30, 0x30, 0x31, 0x32, 0x35, 0x32, //0x00007634 QUAD $0x3134383731383838; QUAD $0x3235323130303739  // .asciz 16, '8881784197001252'
	0x33, 0x32, 0x33, 0x33, 0x38, 0x39, 0x30, 0x35, 0x33, 0x33, 0x34, 0x34, 0x37, 0x32, 0x36, 0x35, //0x00007644 QUAD $0x3530393833333233; QUAD $0x3536323734343333  // .asciz 16, '3233890533447265'
	0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007654 QUAD $0x0000000000353236; QUAD $0x0000000000000000  // .asciz 16, '625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007664 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007674 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007684 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007694 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007698 .long 16
	0x34, 0x34, 0x34, 0x30, 0x38, 0x39, 0x32, 0x30, 0x39, 0x38, 0x35, 0x30, 0x30, 0x36, 0x32, 0x36, //0x0000769c QUAD $0x3032393830343434; QUAD $0x3632363030353839  // .asciz 16, '4440892098500626'
	0x31, 0x36, 0x31, 0x36, 0x39, 0x34, 0x35, 0x32, 0x36, 0x36, 0x37, 0x32, 0x33, 0x36, 0x33, 0x32, //0x000076ac QUAD $0x3235343936313631; QUAD $0x3233363332373636  // .asciz 16, '1616945266723632'
	0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076bc QUAD $0x0000000035323138; QUAD $0x0000000000000000  // .asciz 16, '8125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076cc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076dc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000076ec QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000076fc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007700 .long 16
	0x32, 0x32, 0x32, 0x30, 0x34, 0x34, 0x36, 0x30, 0x34, 0x39, 0x32, 0x35, 0x30, 0x33, 0x31, 0x33, //0x00007704 QUAD $0x3036343430323232; QUAD $0x3331333035323934  // .asciz 16, '2220446049250313'
	0x30, 0x38, 0x30, 0x38, 0x34, 0x37, 0x32, 0x36, 0x33, 0x33, 0x33, 0x36, 0x31, 0x38, 0x31, 0x36, //0x00007714 QUAD $0x3632373438303830; QUAD $0x3631383136333333  // .asciz 16, '0808472633361816'
	0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007724 QUAD $0x0000003532363034; QUAD $0x0000000000000000  // .asciz 16, '40625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007734 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007744 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007754 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007764 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x10, 0x00, 0x00, 0x00, //0x00007768 .long 16
	0x31, 0x31, 0x31, 0x30, 0x32, 0x32, 0x33, 0x30, 0x32, 0x34, 0x36, 0x32, 0x35, 0x31, 0x35, 0x36, //0x0000776c QUAD $0x3033323230313131; QUAD $0x3635313532363432  // .asciz 16, '1110223024625156'
	0x35, 0x34, 0x30, 0x34, 0x32, 0x33, 0x36, 0x33, 0x31, 0x36, 0x36, 0x38, 0x30, 0x39, 0x30, 0x38, //0x0000777c QUAD $0x3336333234303435; QUAD $0x3830393038363631  // .asciz 16, '5404236316680908'
	0x32, 0x30, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000778c QUAD $0x0000353231333032; QUAD $0x0000000000000000  // .asciz 16, '203125\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000779c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077ac QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077bc QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000077cc LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x000077d0 .long 17
	0x35, 0x35, 0x35, 0x31, 0x31, 0x31, 0x35, 0x31, 0x32, 0x33, 0x31, 0x32, 0x35, 0x37, 0x38, 0x32, //0x000077d4 QUAD $0x3135313131353535; QUAD $0x3238373532313332  // .asciz 16, '5551115123125782'
	0x37, 0x30, 0x32, 0x31, 0x31, 0x38, 0x31, 0x35, 0x38, 0x33, 0x34, 0x30, 0x34, 0x35, 0x34, 0x31, //0x000077e4 QUAD $0x3531383131323037; QUAD $0x3134353430343338  // .asciz 16, '7021181583404541'
	0x30, 0x31, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000077f4 QUAD $0x0000353236353130; QUAD $0x0000000000000000  // .asciz 16, '015625\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007804 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007814 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007824 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007834 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x00007838 .long 17
	0x32, 0x37, 0x37, 0x35, 0x35, 0x35, 0x37, 0x35, 0x36, 0x31, 0x35, 0x36, 0x32, 0x38, 0x39, 0x31, //0x0000783c QUAD $0x3537353535373732; QUAD $0x3139383236353136  // .asciz 16, '2775557561562891'
	0x33, 0x35, 0x31, 0x30, 0x35, 0x39, 0x30, 0x37, 0x39, 0x31, 0x37, 0x30, 0x32, 0x32, 0x37, 0x30, //0x0000784c QUAD $0x3730393530313533; QUAD $0x3037323230373139  // .asciz 16, '3510590791702270'
	0x35, 0x30, 0x37, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000785c QUAD $0x0035323138373035; QUAD $0x0000000000000000  // .asciz 16, '5078125\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000786c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000787c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000788c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000789c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x11, 0x00, 0x00, 0x00, //0x000078a0 .long 17
	0x31, 0x33, 0x38, 0x37, 0x37, 0x37, 0x38, 0x37, 0x38, 0x30, 0x37, 0x38, 0x31, 0x34, 0x34, 0x35, //0x000078a4 QUAD $0x3738373737383331; QUAD $0x3534343138373038  // .asciz 16, '1387778780781445'
	0x36, 0x37, 0x35, 0x35, 0x32, 0x39, 0x35, 0x33, 0x39, 0x35, 0x38, 0x35, 0x31, 0x31, 0x33, 0x35, //0x000078b4 QUAD $0x3335393235353736; QUAD $0x3533313135383539  // .asciz 16, '6755295395851135'
	0x32, 0x35, 0x33, 0x39, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078c4 QUAD $0x3532363039333532; QUAD $0x0000000000000000  // .asciz 16, '25390625\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078d4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078e4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000078f4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007904 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007908 .long 18
	0x36, 0x39, 0x33, 0x38, 0x38, 0x39, 0x33, 0x39, 0x30, 0x33, 0x39, 0x30, 0x37, 0x32, 0x32, 0x38, //0x0000790c QUAD $0x3933393838333936; QUAD $0x3832323730393330  // .asciz 16, '6938893903907228'
	0x33, 0x37, 0x37, 0x36, 0x34, 0x37, 0x36, 0x39, 0x37, 0x39, 0x32, 0x35, 0x35, 0x36, 0x37, 0x36, //0x0000791c QUAD $0x3936373436373733; QUAD $0x3637363535323937  // .asciz 16, '3776476979255676'
	0x32, 0x36, 0x39, 0x35, 0x33, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000792c QUAD $0x3532313335393632; QUAD $0x0000000000000000  // .asciz 16, '26953125\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000793c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000794c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x0000795c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x0000796c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x00007970 .long 18
	0x33, 0x34, 0x36, 0x39, 0x34, 0x34, 0x36, 0x39, 0x35, 0x31, 0x39, 0x35, 0x33, 0x36, 0x31, 0x34, //0x00007974 QUAD $0x3936343439363433; QUAD $0x3431363335393135  // .asciz 16, '3469446951953614'
	0x31, 0x38, 0x38, 0x38, 0x32, 0x33, 0x38, 0x34, 0x38, 0x39, 0x36, 0x32, 0x37, 0x38, 0x33, 0x38, //0x00007984 QUAD $0x3438333238383831; QUAD $0x3833383732363938  // .asciz 16, '1888238489627838'
	0x31, 0x33, 0x34, 0x37, 0x36, 0x35, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007994 QUAD $0x3236353637343331; QUAD $0x0000000000000035  // .asciz 16, '134765625\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079a4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079b4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079c4 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x000079d4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x12, 0x00, 0x00, 0x00, //0x000079d8 .long 18
	0x31, 0x37, 0x33, 0x34, 0x37, 0x32, 0x33, 0x34, 0x37, 0x35, 0x39, 0x37, 0x36, 0x38, 0x30, 0x37, //0x000079dc QUAD $0x3433323734333731; QUAD $0x3730383637393537  // .asciz 16, '1734723475976807'
	0x30, 0x39, 0x34, 0x34, 0x31, 0x31, 0x39, 0x32, 0x34, 0x34, 0x38, 0x31, 0x33, 0x39, 0x31, 0x39, //0x000079ec QUAD $0x3239313134343930; QUAD $0x3931393331383434  // .asciz 16, '0944119244813919'
	0x30, 0x36, 0x37, 0x33, 0x38, 0x32, 0x38, 0x31, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x000079fc QUAD $0x3138323833373630; QUAD $0x0000000000003532  // .asciz 16, '0673828125\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a0c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a1c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a2c QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007a3c LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
	0x13, 0x00, 0x00, 0x00, //0x00007a40 .long 19
	0x38, 0x36, 0x37, 0x33, 0x36, 0x31, 0x37, 0x33, 0x37, 0x39, 0x38, 0x38, 0x34, 0x30, 0x33, 0x35, //0x00007a44 QUAD $0x3337313633373638; QUAD $0x3533303438383937  // .asciz 16, '8673617379884035'
	0x34, 0x37, 0x32, 0x30, 0x35, 0x39, 0x36, 0x32, 0x32, 0x34, 0x30, 0x36, 0x39, 0x35, 0x39, 0x35, //0x00007a54 QUAD $0x3236393530323734; QUAD $0x3539353936303432  // .asciz 16, '4720596224069595'
	0x33, 0x33, 0x36, 0x39, 0x31, 0x34, 0x30, 0x36, 0x32, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a64 QUAD $0x3630343139363333; QUAD $0x0000000000003532  // .asciz 16, '3369140625\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a74 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a84 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //0x00007a94 QUAD $0x0000000000000000; QUAD $0x0000000000000000  // .asciz 16, '\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
	0x00, 0x00, 0x00, 0x00, //0x00007aa4 LONG $0x00000000  // .asciz 4, '\x00\x00\x00\x00'
}
 
