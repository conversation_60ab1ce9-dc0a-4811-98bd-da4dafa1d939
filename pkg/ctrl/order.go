package ctrl

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"

	"github.com/abiosoft/ishell/v2"
	"github.com/go-resty/resty/v2"
	"github.com/olekukonko/tablewriter"
	"github.com/skip2/go-qrcode"

	"git.sho/ComputeHub/ComputeHub/pkg/models"
	cu "git.sho/ComputeHub/ComputeHub/utils"
	"git.sho/ComputeHub/SuanLink"
	"git.sho/ComputeHub/SuanLink/pkg/consts"
	"git.sho/ComputeHub/SuanLink/pkg/utils"

	"devgitlab.lianoid.com/jasonni/eaas-common/pagination"
)

var (
	OrderListFields = []string{
		"ID",
		"订单号",
		"状态",
		"金额(元)",
		"描述",
		"商品标签",
		"创建时间",
		"更新时间",
	}

	RefundOrderListFields = []string{
		"ID",
		"退款单号",
		"原订单号",
		"状态",
		"退款金额(元)",
		"退款原因",
		"创建时间",
		"退款时间",
	}

	OrderCompletionListFields = []string{
		"ID",
		"订单号",
		"状态",
		"金额",
	}
)

func AddOrderCmd(shell *ishell.Shell, ctl *Ctrl) {
	shell.AddCmd(&ishell.Cmd{
		Name: "order-list",
		Help: "order-list - 查看订单列表",
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.orderList)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "order-detail",
		Help:    "order-detail <id> - 获取指定ID的订单详情，待支付订单自动显示支付二维码",
		Aliases: []string{"order-get"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.orderDetail)
		},
		Completer: func(args []string) []string {
			return ctl.getOrderIDsForCompletion()
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name: "order-cancel",
		Help: "order-cancel <id> - 取消指定ID的订单",
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.orderCancel)
		},
		Completer: func(args []string) []string {
			return ctl.getOrderIDsForCompletion()
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name: "refund-list",
		Help: "refund-list - 查看退款订单列表",
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.refundOrderList)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "refund-detail",
		Help:    "refund-detail <id> - 获取指定ID的退款订单详情",
		Aliases: []string{"refund-get"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.refundOrderDetail)
		},
	})
}

func (ctl *Ctrl) orderList(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	var (
		params = &pagination.QueryParams{
			Page:  1,
			Limit: 25,
		}
		total uint
	)

	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	for {
		table := tablewriter.NewWriter(os.Stdout)
		table.Header(OrderListFields)

		rc := resty.New().
			SetHeaders(
				map[string]string{
					"Accept":        "application/json",
					"Content-Type":  "application/json",
					"User-Agent":    "suanlink/cli",
					"Authorization": ctl.GetAccessToken(),
				},
			)

		if ctl.config.Debug {
			rc.Debug = true
		}

		if params.Page == 0 {
			params.Page = 1
		}

		if total > 0 && params.Page > (int(total)/params.Limit)+1 {
			break
		}

		resp, err := rc.R().
			SetResult(&ApiResponse[cu.QueryResult[*models.Order]]{}).
			SetBody(params).
			Post(ctl.config.EndPoint(consts.ApiUrlOrderList))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}

		switch code := resp.StatusCode(); code {
		case http.StatusOK:
		case http.StatusUnauthorized:
			if se := ctl.refresh(); se != nil {
				return nil, se.With("function", "refresh").LogError()
			}
			continue
		default:
			return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
		}

		result := resp.Result().(*ApiResponse[cu.QueryResult[*models.Order]])
		if code := result.Code; code != suanlink.Success.Code() {
			return nil, suanlink.ErrApiRequest.With("order list resp data code: %d", code).LogError()
		}
		total = result.Data.Total

		// 处理返回的订单数据
		for _, order := range result.Data.Records {
			table.Append(ctl.formatOrderRowFromModel(order))
		}

		table.Render()

		// 如果总数为0或者当前页已经是最后一页且数据不足一页，直接结束
		if total == 0 || (total <= uint(params.Limit) && params.Page == 1) {
			break
		}

		c.Printf("Total(%d), Page(%d), Limit(%d), (N)ext, (P)revious, [Q]uit: ", result.Data.Total, params.Page, params.Limit)
		switch input := strings.TrimSpace(c.ReadLine()); input {
		case "q", "Q":
			return nil, nil
		case "n", "N":
			params.Page += 1
			continue
		case "p", "P":
			params.Page -= 1
			continue
		default:
			params.Page += 1
			continue
		}
	}

	return nil, nil
}

func (ctl *Ctrl) refundOrderList(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	var (
		params = &pagination.QueryParams{
			Page:  1,
			Limit: 25,
		}
		total uint
	)

	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	for {
		table := tablewriter.NewWriter(os.Stdout)
		table.Header(RefundOrderListFields)

		if params.Page == 0 {
			params.Page = 1
		}

		if total > 0 && params.Page > (int(total)/params.Limit)+1 {
			break
		}

		resp, err := ctl.rc().R().
			SetResult(&ApiResponse[cu.QueryResult[*models.RefundOrder]]{}).
			SetBody(params).
			Post(ctl.config.EndPoint(consts.ApiUrlRefundList))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}

		switch code := resp.StatusCode(); code {
		case http.StatusOK:
		case http.StatusUnauthorized:
			if se := ctl.refresh(); se != nil {
				return nil, se.With("function", "refresh").LogError()
			}
			continue
		default:
			return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
		}

		result := resp.Result().(*ApiResponse[cu.QueryResult[*models.RefundOrder]])
		if code := result.Code; code != suanlink.Success.Code() {
			return nil, suanlink.ErrApiRequest.With("refund order list resp data code: %d", code).LogError()
		}
		total = result.Data.Total

		// 处理返回的退款订单数据
		for _, refundOrder := range result.Data.Records {
			table.Append(ctl.formatRefundOrderRowFromModel(refundOrder))
		}

		table.Render()

		// 如果总数为0或者当前页已经是最后一页且数据不足一页，直接结束
		if total == 0 || (total <= uint(params.Limit) && params.Page == 1) {
			break
		}

		c.Printf("Total(%d), Page(%d), Limit(%d), (N)ext, (P)revious, [Q]uit: ", result.Data.Total, params.Page, params.Limit)
		switch input := strings.TrimSpace(c.ReadLine()); input {
		case "q", "Q":
			return nil, nil
		case "n", "N":
			params.Page += 1
			continue
		case "p", "P":
			params.Page -= 1
			continue
		default:
			params.Page += 1
			continue
		}
	}

	return nil, nil
}

func (ctl *Ctrl) formatOrderRowFromModel(order *models.Order) []string {
	return []string{
		fmt.Sprintf("%d", order.ID),
		order.No,
		utils.FormatOrderStatus(order.Status),
		utils.FormatPriceCentsIntoYuan(order.Amount),
		order.Description,
		order.GoodsTag,
		utils.FormatCommonTime(order.CreatedAt),
		utils.FormatCommonTime(order.UpdatedAt),
	}
}

func (ctl *Ctrl) formatRefundOrderRowFromModel(refundOrder *models.RefundOrder) []string {
	return []string{
		fmt.Sprintf("%d", refundOrder.ID),
		refundOrder.No,
		refundOrder.OrderNo,
		utils.FormatRefundStatus(refundOrder.Status),
		utils.FormatPriceCentsIntoYuan(refundOrder.RefundAmount),
		refundOrder.Reason,
		utils.FormatCommonTime(refundOrder.CreatedAt),
		utils.FormatCommonTime(refundOrder.RefundedAt),
	}
}

func (ctl *Ctrl) orderDetail(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查参数
	if len(c.Args) == 0 {
		c.Println("错误: 请提供订单ID")
		c.Println("用法: order-detail <id>")
		return nil, nil
	}

	// 解析ID参数
	idStr := c.Args[0]
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Printf("错误: 无效的ID格式 '%s', 请提供数字ID\n", idStr)
		return nil, nil
	}

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// 发送请求获取订单详情
	url := fmt.Sprintf(consts.ApiUrlOrderDetail, id)
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.Order]{}).
		Get(ctl.config.EndPoint(url))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.Order]{}).
			Get(ctl.config.EndPoint(url))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.Order])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("order detail resp data code: %d", code).LogError()
	}

	// 显示订单详情
	order := result.Data
	c.Println()
	c.Printf("订单详情 (ID: %d)\n", order.ID)
	c.Println("=" + strings.Repeat("=", 50))
	c.Printf("订单号: %s\n", order.No)
	c.Printf("状态: %s\n", utils.FormatOrderStatus(order.Status))
	c.Printf("金额: %s 元\n", utils.FormatPriceCentsIntoYuan(order.Amount))
	c.Printf("描述: %s\n", order.Description)
	c.Printf("商品标签: %s\n", order.GoodsTag)
	c.Printf("创建时间: %s\n", utils.FormatCommonTime(order.CreatedAt))
	c.Printf("更新时间: %s\n", utils.FormatCommonTime(order.UpdatedAt))

	// 如果订单状态为待支付，自动显示支付二维码
	if order.Status == "pending" {
		c.Println()
		c.Println("支付信息:")
		c.Println("-" + strings.Repeat("-", 40))

		if codeURL := ctl.getOrderPaymentCode(id); codeURL != "" {
			ctl.displayQRCodeForOrder(codeURL, c)
		} else {
			c.Println("暂时无法获取支付二维码，请稍后重试")
		}
	}

	c.Println()

	return nil, nil
}

func (ctl *Ctrl) orderCancel(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查参数
	if len(c.Args) == 0 {
		c.Println("错误: 请提供订单ID")
		c.Println("用法: order-cancel <id>")
		return nil, nil
	}

	// 解析ID参数
	idStr := c.Args[0]
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Printf("错误: 无效的ID格式 '%s', 请提供数字ID\n", idStr)
		return nil, nil
	}

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	// 确认取消操作
	c.Printf("确认要取消订单 %d 吗? (y/N): ", id)
	confirm := strings.ToLower(strings.TrimSpace(c.ReadLine()))
	if confirm != "y" && confirm != "yes" {
		c.Println("操作已取消")
		return nil, nil
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// 发送取消请求
	url := fmt.Sprintf(consts.ApiUrlOrderCancel, id)
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.Order]{}).
		Post(ctl.config.EndPoint(url))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.Order]{}).
			Post(ctl.config.EndPoint(url))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.Order])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("order cancel resp data code: %d", code).LogError()
	}
	c.Printf("订单 %d 取消成功\n", id)

	return nil, nil
}

func (ctl *Ctrl) refundOrderDetail(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查参数
	if len(c.Args) == 0 {
		c.Println("错误: 请提供退款订单ID")
		c.Println("用法: refund-detail <id>")
		return nil, nil
	}

	// 解析ID参数
	idStr := c.Args[0]
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Printf("错误: 无效的ID格式 '%s', 请提供数字ID\n", idStr)
		return nil, nil
	}

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// 发送请求获取退款订单详情
	url := fmt.Sprintf(consts.ApiUrlRefundDetail, id)
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.RefundOrder]{}).
		Get(ctl.config.EndPoint(url))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.RefundOrder]{}).
			Get(ctl.config.EndPoint(url))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.RefundOrder])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("refund order detail resp data code: %d", code).LogError()
	}

	// 显示退款订单详情
	refundOrder := result.Data
	c.Println()
	c.Printf("退款订单详情 (ID: %d)\n", refundOrder.ID)
	c.Println("=" + strings.Repeat("=", 50))
	c.Printf("退款单号: %s\n", refundOrder.No)
	c.Printf("原订单号: %s\n", refundOrder.OrderNo)
	c.Printf("状态: %s\n", utils.FormatRefundStatus(refundOrder.Status))
	c.Printf("退款金额: %s 元\n", utils.FormatPriceCentsIntoYuan(refundOrder.RefundAmount))
	c.Printf("退款原因: %s\n", refundOrder.Reason)
	c.Printf("创建时间: %s\n", utils.FormatCommonTime(refundOrder.CreatedAt))
	c.Printf("退款时间: %s\n", utils.FormatCommonTime(refundOrder.RefundedAt))
	c.Println()

	return nil, nil
}

// getOrderPaymentCode 获取订单的支付二维码URL
func (ctl *Ctrl) getOrderPaymentCode(orderID int) string {
	// 发送请求获取微信支付码
	url := fmt.Sprintf(consts.ApiUrlOrderWcpayCode, orderID)
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[interface{}]{}).
		Get(ctl.config.EndPoint(url))
	if err != nil {
		return ""
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return ""
		}
		// 重试一次
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[interface{}]{}).
			Get(ctl.config.EndPoint(url))
		if err != nil {
			return ""
		}
	default:
		return ""
	}

	result := resp.Result().(*ApiResponse[interface{}])
	if code := result.Code; code != suanlink.Success.Code() {
		return ""
	}

	// 提取支付码URL
	if dataMap, ok := result.Data.(map[string]interface{}); ok {
		if codeUrl, ok := dataMap["codeUrl"].(string); ok {
			return codeUrl
		}
	}

	return ""
}

// getOrderIDsForCompletion 获取用户订单ID列表用于自动补全
func (ctl *Ctrl) getOrderIDsForCompletion() []string {
	// 检查用户是否已登录
	if ctl.AccessToken == "" {
		return []string{}
	}

	params := &pagination.QueryParams{
		Page:  1,
		Limit: 20, // 限制显示数量，避免输出过多
	}

	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[cu.QueryResult[*models.Order]]{}).
		SetBody(params).
		Post(ctl.config.EndPoint(consts.ApiUrlOrderList))
	if err != nil {
		return []string{}
	}

	if resp.StatusCode() != http.StatusOK {
		return []string{}
	}

	result := resp.Result().(*ApiResponse[cu.QueryResult[*models.Order]])
	if result.Code != suanlink.Success.Code() {
		return []string{}
	}

	// 先打印订单列表信息，帮助用户选择
	if len(result.Data.Records) > 0 {
		fmt.Println("\n可用的订单列表:")

		table := tablewriter.NewWriter(os.Stdout)
		table.Header(OrderCompletionListFields)

		for _, order := range result.Data.Records {
			table.Append([]string{
				fmt.Sprintf("%d", order.ID),
				order.No,
				utils.FormatOrderStatus(order.Status),
				fmt.Sprintf("%.2f元", float64(order.Amount)/100),
			})
		}

		table.Render()
		fmt.Println()
	}

	// 返回纯净的订单ID列表用于补全
	var completions []string
	for _, order := range result.Data.Records {
		orderID := fmt.Sprintf("%d", order.ID)
		completions = append(completions, orderID)
	}

	return completions
}

// displayQRCodeForOrder 在终端中显示订单支付二维码
func (ctl *Ctrl) displayQRCodeForOrder(url string, c *ishell.Context) {
	// 生成二维码
	qr, err := qrcode.New(url, qrcode.Medium)
	if err != nil {
		c.Printf("生成二维码失败: %v\n", err)
		c.Printf("支付链接: %s\n", url)
		return
	}

	// 将二维码转换为字符串显示
	qrString := qr.ToSmallString(false)

	c.Println("请使用微信扫描以下二维码进行支付:")
	c.Println(qrString)
	c.Printf("或复制链接到微信: %s\n", url)
}
