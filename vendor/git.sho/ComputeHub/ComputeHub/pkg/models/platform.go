package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
)

const (
	// user request from eaas platform
	PlatformTypeEaas = "eaas"
	// user request from the simplified edition of eaas
	PlatformTypeEaasMini = "eaas_mini"
	// user request from standalone portal but authenticated by 3rd CAS service
	PlatformTypeHub = "hub"
	// user request from device of hardware agent
	PlatformTypePersonal = "personal"
	// user request from cli tool
	PlatformTypeCli = "cli"
)

type StringArray []string

func (a *StringArray) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal StringArray value: %v", value)
	}
	return json.Unmarshal(bytes, a)
}

func (a StringArray) Value() (driver.Value, error) {
	return json.Marshal(a)
}

type Platform struct {
	Id uint `json:"id" gorm:"column:id; PRIMARY_KEY; AUTO_INCREMENT"`

	// platform identifier
	Key         string `json:"key" gorm:"unique_index; not null"`
	DisplayName string `json:"displayName" gorm:"unique_index; not null"`
	Type        string `json:"type" gorm:"not null"`
	// only for type "personal"
	DeviceSn string `json:"deviceSn"`

	Token       string `json:"token" gorm:"unique_index; not null"`
	Description string `json:"description"`

	OnlinePayment         *bool       `json:"onlinePayment" gorm:"default:true"`
	PaymentMethods        StringArray `json:"paymentMethods" gorm:"type:jsonb"`
	OverdraftLimitInCents int64       `json:"overdraftLimitInCents"`

	CreatedAt time.Time `json:"createdAt" gorm:"not null"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"not null"`
}

func (t *Platform) Validate(forCreate bool, db *gorm.DB) error {
	t.Key = strings.TrimSpace(t.Key)
	t.DisplayName = strings.TrimSpace(t.DisplayName)
	t.Type = strings.TrimSpace(t.Type)
	t.DeviceSn = strings.TrimSpace(t.DeviceSn)

	if t.Key == "" || t.DisplayName == "" || t.Type == "" {
		return fmt.Errorf("key / displayName / type are required")
	}

	if forCreate && t.OverdraftLimitInCents == 0 {
		if sysCfg, err := GetSystemConfigs(db); err != nil {
			return fmt.Errorf("failed to get system configs, %v", err)
		} else {
			if overdraftLimit, ok := sysCfg[SysCfgGlobalOverdraftLimitInCents]; ok {
				t.OverdraftLimitInCents = int64(overdraftLimit.(uint))
			}
		}
	}

	switch t.Type {
	case PlatformTypeEaas:
	case PlatformTypeEaasMini:
	case PlatformTypeHub:
	case PlatformTypeCli:
	case PlatformTypePersonal:
		if t.DeviceSn == "" {
			return fmt.Errorf("deviceSn is required when type is (%s)", t.Type)
		}
	default:
		return fmt.Errorf("unknown platform token type (%s)", t.Type)
	}

	return nil
}
