## Suan Link

## 使用帮助

```bash
>>> help

Commands:
  app-create             app-create - 创建新应用
  app-detail             app-detail <id> - 获取指定ID的应用详情
  app-list               app-list - 查看应用实例列表
  app-operate            app-operate <id> <action> - 操作应用 (start/stop/restart)
  app-spec-detail        app-spec-detail <id> - 获取指定ID的应用规格详情
  app-spec-list          app-spec-list - 列表显示应用规格
  bill-detail            bill-detail <id> - 获取指定ID的账单详情
  bill-list              bill-list - 查看账单列表
  clear                  clear the screen
  debug                  debug - 显示debug信息
  development            development - 启用开发测试环境
  help                   display help
  login                  login
  logout                 logout
  options                options <type> - 获取指定类型的选项配置
  order-cancel           order-cancel <id> - 取消指定ID的订单
  order-detail           order-detail <id> - 获取指定ID的订单详情，待支付订单自动显示支付二维码
  order-list             order-list - 查看订单列表
  quit                   quit the program, alias as exit
  refund-detail          refund-detail <id> - 获取指定ID的退款订单详情
  refund-list            refund-list - 查看退款订单列表
  show-local-config      show-local-config - 显示本地配置信息
  show-user-config       show-user-config - 显示用户配置信息
  user-deposit           user-deposit - 用户账户充值
  user-info              user-info - 获取用户信息
  version                version


```

# 编译工具版本

```bash
kim@m2 ~/WorkSpace/GolandProjects/suanlink (dev*) $ goreleaser -v    
  ____       ____      _
 / ___| ___ |  _ \ ___| | ___  __ _ ___  ___ _ __
| |  _ / _ \| |_) / _ \ |/ _ \/ _` / __|/ _ \ '__|
| |_| | (_) |  _ <  __/ |  __/ (_| \__ \  __/ |
 \____|\___/|_| \_\___|_|\___|\__,_|___/\___|_|
goreleaser: Release engineering, simplified.
https://goreleaser.com

GitVersion:    v2.11.0
GitCommit:     unknown
GitTreeState:  unknown
BuildDate:     unknown
BuiltBy:       unknown
GoVersion:     go1.24.4
Compiler:      gc
ModuleSum:     h1:QXXdGjmzdgHnsI2vkijEAskHyYLHOYTQilA6NaGMwQo=
Platform:      darwin/arm64
```
