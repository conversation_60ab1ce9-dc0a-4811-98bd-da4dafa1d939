package ctrl

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/abiosoft/ishell/v2"
	"github.com/gaoxing520/errors"
	"github.com/rs/zerolog"
	"github.com/skip2/go-qrcode"

	"git.sho/ComputeHub/ComputeHub/pkg/models"
	"git.sho/ComputeHub/ComputeHub/pkg/service"
	"git.sho/ComputeHub/SuanLink"
	"git.sho/ComputeHub/SuanLink/pkg/config"
	"git.sho/ComputeHub/SuanLink/pkg/consts"
	"git.sho/ComputeHub/SuanLink/pkg/utils"
)

func AddUserCmd(shell *ishell.Shell, ctl *Ctrl) {
	shell.AddCmd(&ishell.Cmd{
		Name:    "login",
		Help:    "用户登录",
		Aliases: []string{"user-login", "1-user-login"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.login)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "logout",
		Help:    "用户退出",
		Aliases: []string{"user-logout", "2-user-logout"},
		Func: func(c *ishell.Context) {
			shell.SetHistoryPath("")
			ctl.runShellCmd(c, ctl.logout)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "user-info",
		Help:    "获取当前用户信息",
		Aliases: []string{"3-user-info"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.userInfo)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "user-deposit",
		Help:    "当前用户为自己充值",
		Aliases: []string{"4-user-deposit"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.userDeposit)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name:    "user-options",
		Help:    "获取指定类型的选项配置",
		Aliases: []string{"5-user-options"},
		Func: func(c *ishell.Context) {
			ctl.runShellCmd(c, ctl.getOptions)
		},
	})
}

func (ctl *Ctrl) login(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// choice := c.MultiChoice([]string{
	// 	"短信登录",
	// 	"密码登录",
	// }, "选择登录方式")
	// if choice == 0 {}

	c.Println("通过手机号验证")
	c.Printf("请输入手机号码: ")

	cellphone := c.ReadLine()
	if cellphone == "" {
		return nil, suanlink.ErrRequest.With("手机号检查未通过: 空手机号").LogDebug()
	}

	if !utils.VerifyMobileFormat(cellphone) {
		return nil, suanlink.ErrRequest.With("手机号检查未通过: 格式错误: %s", cellphone).LogDebug()
	}

	var (
		ae     errors.AppError
		secret = ctl.config.Secret
	)

	if secret == "" {
		secret, ae = config.GenSecret(cellphone)
		if ae != nil {
			return nil, ae
		}
	}

	rc := ctl.rcWithSecret(secret)
	resp, err := rc.R().
		SetResult(&ApiResponse[service.CaptchaResp]{}).
		SetBody(service.GetCaptchaReq{
			Cellphone: cellphone,
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthCaptcha))
	if err != nil {
		return nil, suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	captchaResp := resp.Result().(*ApiResponse[service.CaptchaResp])
	if code := captchaResp.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("captcha resp data code: %d", code).LogError()
	}

	c.Println(captchaResp.Data.Content)
	c.Printf("请输入验证码: ")
	captcha := strings.TrimSpace(c.ReadLine())

	resp, err = rc.R().
		SetResult(&ApiResponse[service.SendCodeResp]{}).
		SetBody(service.SendCodeReq{
			Cellphone: cellphone,
			Token:     captchaResp.Data.Token,
			Captcha:   captcha,
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthSmsCode))
	if err != nil {
		return nil, suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	smsCodeResp := resp.Result().(*ApiResponse[service.SendCodeResp])
	if code := smsCodeResp.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("sms code resp data code: %d", code).LogError()
	}

	if !smsCodeResp.Data.Sent {
		return nil, suanlink.ErrApiRequest.With("send sms code failed").LogError()
	}

	c.Printf("请输入短信验证码: ")
	sms := strings.TrimSpace(c.ReadLine())

	resp, err = rc.R().
		SetResult(&ApiResponse[service.LoginByCodeResp]{}).
		SetBody(service.LoginByCodeReq{
			Token:     captchaResp.Data.Token,
			Code:      sms,
			Cellphone: cellphone,
			MachineID: utils.MachineId(),
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthSmsLogin))
	if err != nil {
		return nil, suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	smsLoginResp := resp.Result().(*ApiResponse[service.LoginByCodeResp])
	if code := smsLoginResp.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("sms login resp data code: %d", code).LogError()
	}

	if smsLoginResp.Data.RefreshToken != "" && smsLoginResp.Data.AccessToken != "" {
		ctl.config.CellPhone = cellphone
		ctl.AccessToken = smsLoginResp.Data.AccessToken
		ctl.config.RefreshToken = smsLoginResp.Data.RefreshToken
	}

	ctl.saveConfigToFile()

	return ctl.showUserInfo(c)
}

func (ctl *Ctrl) logout(c *ishell.Context) (any, error) {
	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	ctl.config = &config.Config{
		Debug:    false,
		LogLevel: zerolog.LevelInfoValue,
	}

	ctl.saveConfigToFile()
	c.Println("当前用户已退出登录")

	return nil, nil
}

func (ctl *Ctrl) refresh() errors.AppError {
	if ctl.config.RefreshToken == "" {
		return suanlink.ErrParameter.With("refresh token is empty").LogError()
	}

	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[service.RefreshTokenResp]{}).
		SetBody(service.RefreshTokenReq{
			RefreshToken: ctl.config.RefreshToken,
			MachineID:    utils.MachineId(),
		}).
		Post(ctl.config.EndPoint(consts.ApiUrlAuthRefreshToken))
	if err != nil {
		return suanlink.ErrApiRequest.WithCause(err).LogError()
	}

	if code := resp.StatusCode(); code != http.StatusOK {
		return suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[service.RefreshTokenResp])
	if code := result.Code; code != suanlink.Success.Code() {
		return suanlink.ErrApiRequest.With("refresh access token resp data code: %d", code).LogError()
	}
	ctl.AccessToken = result.Data.AccessToken

	return nil
}

func (ctl *Ctrl) userInfo(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	return ctl.showUserInfo(c)
}

func (ctl *Ctrl) showUserInfo(c *ishell.Context) (any, error) {
	// 发送请求获取用户信息
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[models.User]{}).
		Get(ctl.config.EndPoint(consts.ApiUrlUserInfo))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[models.User]{}).
			Get(ctl.config.EndPoint(consts.ApiUrlUserInfo))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[models.User])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("user info resp data code: %d", code).LogError()
	}

	// 显示用户信息
	user := result.Data
	c.Println()
	c.Printf("用户信息\n")
	c.Println("=" + strings.Repeat("=", 40))
	c.Printf("ID: %d\n", user.Id)
	c.Printf("用户名: %s\n", user.Name)
	c.Printf("昵称: %s\n", user.Nickname)
	c.Printf("手机号: %s\n", user.Cellphone)
	c.Printf("邮箱: %s\n", user.Email)
	c.Printf("部门: %s\n", user.Department)
	c.Printf("账户余额: %s 元\n", utils.FormatPriceCentsIntoYuan(user.BalanceInCents))
	c.Printf("透支额度: %s 元\n", utils.FormatPriceCentsIntoYuan(user.OverdraftLimitInCents))
	c.Printf("角色类型: %s\n", user.RoleType)
	c.Printf("平台角色: %s\n", utils.FormatPlatformRole(user.PlatformRole))
	c.Printf("创建时间: %s\n", utils.FormatCommonTime(user.CreatedAt))
	c.Printf("更新时间: %s\n", utils.FormatCommonTime(user.UpdatedAt))

	return nil, nil
}

func (ctl *Ctrl) userDeposit(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	c.Println("用户账户充值")
	c.Println("=" + strings.Repeat("=", 30))

	// 获取充值金额
	c.Printf("充值金额(元): ")
	amountStr := strings.TrimSpace(c.ReadLine())
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil || amount <= 0 {
		c.Printf("错误: 无效的金额格式 '%s', 请输入正数\n", amountStr)
		return nil, nil
	}

	// 使用微信支付（目前唯一支持的支付方式）
	c.Println("支付方式: 微信支付")

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// 构建请求数据
	depositData := &service.DepositUserReq{
		Uid:           0,                   // 服务端会从认证信息中获取
		AmountInCents: int64(amount * 100), // 转换为分
	}

	// 发送充值请求
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[service.DepositUserResp]{}).
		SetBody(depositData).
		Post(ctl.config.EndPoint(consts.ApiUrlUserDeposit))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[service.DepositUserResp]{}).
			SetBody(depositData).
			Post(ctl.config.EndPoint(consts.ApiUrlUserDeposit))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "post").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[service.DepositUserResp])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("user deposit resp data code: %d", code).LogError()
	}

	// 显示充值结果
	data := result.Data
	c.Println()
	c.Printf("充值请求提交成功!\n")
	c.Printf("充值金额: %.2f 元\n", amount)
	if data.CreatedOrder != nil {
		c.Printf("订单号: %s\n", data.CreatedOrder.No)
		c.Printf("订单ID: %d\n", data.CreatedOrder.ID)
	}
	if data.CodeURL != "" {
		// 检查是否是微信支付链接
		if strings.Contains(data.CodeURL, "weixin://") || strings.Contains(data.CodeURL, "wxpay") {
			ctl.displayQRCode(data.CodeURL, c)
		} else {
			c.Printf("支付链接: %s\n", data.CodeURL)
		}
	}
	c.Println()

	return nil, nil
}

func (ctl *Ctrl) getOptions(c *ishell.Context) (any, error) {
	c.ShowPrompt(false)
	defer c.ShowPrompt(true)

	// 检查参数
	if len(c.Args) == 0 {
		c.Println("错误: 请提供选项类型")
		c.Println("用法: user-options <type>")
		c.Println("支持的类型: app-spec")
		return nil, nil
	}

	optionType := c.Args[0]

	// 检查用户登录状态
	if _, err := ctl.isUserLogin(c); err != nil {
		return nil, err
	}

	ctl.lock.Lock()
	defer ctl.lock.Unlock()

	// 发送请求获取选项配置
	url := fmt.Sprintf(consts.ApiUrlOptions, optionType)
	resp, err := ctl.rc().R().
		SetResult(&ApiResponse[map[string][]string]{}).
		Get(ctl.config.EndPoint(url))
	if err != nil {
		return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
	}

	switch code := resp.StatusCode(); code {
	case http.StatusOK:
		// 成功，继续处理
	case http.StatusUnauthorized:
		if se := ctl.refresh(); se != nil {
			return nil, se.With("function", "refresh").LogError()
		}
		// 重新发送请求
		resp, err = ctl.rc().R().
			SetResult(&ApiResponse[map[string][]string]{}).
			Get(ctl.config.EndPoint(url))
		if err != nil {
			return nil, suanlink.ErrApiRequest.With("method: %s", "get").WithCause(err).LogError()
		}
		if resp.StatusCode() != http.StatusOK {
			return nil, suanlink.ErrApiRequest.With("http status code: %d", resp.StatusCode()).LogError()
		}
	default:
		return nil, suanlink.ErrApiRequest.With("http status code: %d", code).LogError()
	}

	result := resp.Result().(*ApiResponse[map[string][]string])
	if code := result.Code; code != suanlink.Success.Code() {
		return nil, suanlink.ErrApiRequest.With("options resp data code: %d", code).LogError()
	}

	// 显示选项配置
	data := result.Data
	c.Println()
	c.Printf("选项配置 - %s\n", optionType)
	c.Println("=" + strings.Repeat("=", 50))

	// 格式化显示选项数据
	for key, values := range data {
		c.Printf("%s:\n", key)
		for i, item := range values {
			c.Printf("  %d. %s\n", i+1, item)
		}
	}
	c.Println()

	return nil, nil
}

func (ctl *Ctrl) isUserLogin(c *ishell.Context) (any, error) {
	if ctl.config.RefreshToken == "" || ctl.config.CellPhone == "" {
		c.Println("尚未登录, 开始进行用户验证.")
		return ctl.login(c)
	}
	return nil, nil
}

// displayQRCode 在终端中显示二维码
func (ctl *Ctrl) displayQRCode(url string, c *ishell.Context) {
	// 生成二维码
	qr, err := qrcode.New(url, qrcode.Medium)
	if err != nil {
		c.Printf("生成二维码失败: %v\n", err)
		c.Printf("支付链接: %s\n", url)
		return
	}

	// 将二维码转换为字符串显示
	qrString := qr.ToSmallString(false)

	c.Println("请使用微信扫描以下二维码进行支付:")
	c.Println(qrString)
	c.Printf("或复制链接到微信: %s\n", url)
}
