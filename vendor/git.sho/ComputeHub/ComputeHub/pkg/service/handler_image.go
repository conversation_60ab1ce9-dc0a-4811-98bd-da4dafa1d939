package service

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"

	"devgitlab.lianoid.com/jasonni/eaas-common/log"
	"devgitlab.lianoid.com/jasonni/eaas-common/pagination"
	hub "git.sho/ComputeHub/ComputeHub"
	"git.sho/ComputeHub/ComputeHub/pkg/models"
	"git.sho/ComputeHub/ComputeHub/utils"
)

type ImageQueryResultResp struct {
	Records       []*models.Image `json:"records"`
	Total         uint            `json:"total"`
	FilteredTotal uint            `json:"filteredTotal"`
}

func (s *ComputeHubService) HttpHandleGetImages(c *gin.Context) {
	log.Infof("ready to get images")
	// user := c.MustGet(utils.AuthKeyUser).(*models.User)

	pageParams := &pagination.QueryParams{}
	if err := c.ShouldBindJSON(pageParams); err != nil {
		err = fmt.Errorf("bind json data from post request error: %+v", err)
		log.Error(err)
		utils.AbortReqOnErr(c, hub.ParamsInvalidErr.FromError(err))
		return
	}
	log.Debugf("pageParams: %+v", pageParams)

	model := &pagination.Model{
		DefaultOrderBy: "id",
		ObjModel:       &models.Image{},
		TimeFieldName:  "CreatedAt",
	}
	pageParams.SetModel(model)

	alteredColumns := []*pagination.Column{}
	var tagsColumn *pagination.Column
	for _, col := range pageParams.Columns {
		if col.Name == "tags" {
			colValues := []string{}
			for tag := range strings.SplitSeq(col.Value, ",") {
				if len(strings.Split(tag, ":")) == 1 {
					colValues = append(colValues, fmt.Sprintf("%s:%s", models.TagNameCustom, tag))
				} else {
					colValues = append(colValues, tag)
				}
			}
			col.Value = strings.Join(colValues, ",")
			tagsColumn = col
		} else {
			alteredColumns = append(alteredColumns, col)
		}
	}
	pageParams.Columns = alteredColumns

	// db := s.db
	db := s.db.Where("available = ?", true)
	if tagsColumn != nil {
		tags := models.RemoveDups(
			strings.Split(
				strings.ReplaceAll(tagsColumn.Value, " ", ""),
				","),
		)
		tagsStr := "["
		for _, tag := range tags {
			tagsStr += fmt.Sprintf("\"%s\",", tag)
		}
		tagsStr = strings.TrimSuffix(tagsStr, ",")
		tagsStr += "]"

		db = db.Where("tags @> ?",
			tagsStr,
		)
	}

	images := []*models.Image{}
	if err := pageParams.LoadRecords(db, &images); err != nil {
		err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("load records failed, %s", err))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}
	for _, image := range images {
		image.Readme = ""
	}

	total := uint(0)
	// if err := s.db.Model(&models.Image{}).Count(&total).Error; err != nil {
	if err := s.db.Model(&models.Image{}).Where("available = ?", true).Count(&total).Error; err != nil {
		err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("load record count failed, %s", err))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}

	filteredTotal := uint(0)
	query := "count(id)"
	if err := pageParams.Select(db, query, &filteredTotal); err != nil {
		err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("load filtered record count failed, %s", err))
		log.Error(err)
		utils.AbortReqOnErr(c, err)
		return
	}

	utils.RespondDataToHttpClient(c, &utils.QueryResult[*models.Image]{
		Records:       images,
		Total:         total,
		FilteredTotal: filteredTotal,
	})
}

func (s *ComputeHubService) HttpHandleGetImage(c *gin.Context) {
	log.Infof("ready to get image")
	// user := c.MustGet(utils.AuthKeyUser).(*models.User)

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		log.Error(err)
		utils.AbortReqOnErr(c, hub.ParamsInvalidErr)
		return
	}

	image := &models.Image{}
	if err := s.db.Model(&models.Image{}).Where("id = ?", id).First(image).Error; err != nil {
		if !gorm.IsRecordNotFoundError(err) {
			err := hub.QueryDatabaseErr.FromStr(fmt.Sprintf("query record by id (%d) failed, %s", id, err))
			log.Error(err)
			utils.AbortReqOnErr(c, err)
			return
		} else {
			log.Errorf("record not found by id (%d)", id)
			utils.AbortReqOnErr(c, hub.ParamsInvalidErr)
			return
		}
	}

	utils.RespondDataToHttpClient(c, image)
}
