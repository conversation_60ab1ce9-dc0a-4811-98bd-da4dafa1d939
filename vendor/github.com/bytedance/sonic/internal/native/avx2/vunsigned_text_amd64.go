// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package avx2

var _text_vunsigned = []byte{
	// .p2align 4, 0x90
	// _vunsigned
	0x55, // pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000001 movq         %rsp, %rbp
	0x41, 0x56, //0x00000004 pushq        %r14
	0x53, //0x00000006 pushq        %rbx
	0x49, 0x89, 0xd0, //0x00000007 movq         %rdx, %r8
	0x48, 0x8b, 0x0e, //0x0000000a movq         (%rsi), %rcx
	0x4c, 0x8b, 0x0f, //0x0000000d movq         (%rdi), %r9
	0x4c, 0x8b, 0x77, 0x08, //0x00000010 movq         $8(%rdi), %r14
	0x48, 0xc7, 0x02, 0x09, 0x00, 0x00, 0x00, //0x00000014 movq         $9, (%rdx)
	0xc5, 0xf8, 0x57, 0xc0, //0x0000001b vxorps       %xmm0, %xmm0, %xmm0
	0xc5, 0xf8, 0x11, 0x42, 0x08, //0x0000001f vmovups      %xmm0, $8(%rdx)
	0x48, 0x8b, 0x06, //0x00000024 movq         (%rsi), %rax
	0x48, 0x89, 0x42, 0x18, //0x00000027 movq         %rax, $24(%rdx)
	0x4c, 0x39, 0xf1, //0x0000002b cmpq         %r14, %rcx
	0x0f, 0x83, 0x1b, 0x00, 0x00, 0x00, //0x0000002e jae          LBB0_1
	0x41, 0x8a, 0x04, 0x09, //0x00000034 movb         (%r9,%rcx), %al
	0x3c, 0x2d, //0x00000038 cmpb         $45, %al
	0x0f, 0x85, 0x1e, 0x00, 0x00, 0x00, //0x0000003a jne          LBB0_4
	//0x00000040 LBB0_3
	0x48, 0x89, 0x0e, //0x00000040 movq         %rcx, (%rsi)
	0x49, 0xc7, 0x00, 0xfa, 0xff, 0xff, 0xff, //0x00000043 movq         $-6, (%r8)
	0x5b, //0x0000004a popq         %rbx
	0x41, 0x5e, //0x0000004b popq         %r14
	0x5d, //0x0000004d popq         %rbp
	0xc3, //0x0000004e retq         
	//0x0000004f LBB0_1
	0x4c, 0x89, 0x36, //0x0000004f movq         %r14, (%rsi)
	0x49, 0xc7, 0x00, 0xff, 0xff, 0xff, 0xff, //0x00000052 movq         $-1, (%r8)
	0x5b, //0x00000059 popq         %rbx
	0x41, 0x5e, //0x0000005a popq         %r14
	0x5d, //0x0000005c popq         %rbp
	0xc3, //0x0000005d retq         
	//0x0000005e LBB0_4
	0x8d, 0x50, 0xd0, //0x0000005e leal         $-48(%rax), %edx
	0x80, 0xfa, 0x0a, //0x00000061 cmpb         $10, %dl
	0x0f, 0x82, 0x0f, 0x00, 0x00, 0x00, //0x00000064 jb           LBB0_6
	0x48, 0x89, 0x0e, //0x0000006a movq         %rcx, (%rsi)
	0x49, 0xc7, 0x00, 0xfe, 0xff, 0xff, 0xff, //0x0000006d movq         $-2, (%r8)
	0x5b, //0x00000074 popq         %rbx
	0x41, 0x5e, //0x00000075 popq         %r14
	0x5d, //0x00000077 popq         %rbp
	0xc3, //0x00000078 retq         
	//0x00000079 LBB0_6
	0x3c, 0x30, //0x00000079 cmpb         $48, %al
	0x0f, 0x85, 0x26, 0x00, 0x00, 0x00, //0x0000007b jne          LBB0_10
	0x41, 0x8a, 0x44, 0x09, 0x01, //0x00000081 movb         $1(%r9,%rcx), %al
	0x04, 0xd2, //0x00000086 addb         $-46, %al
	0x3c, 0x37, //0x00000088 cmpb         $55, %al
	0x0f, 0x87, 0xc5, 0x00, 0x00, 0x00, //0x0000008a ja           LBB0_9
	0x0f, 0xb6, 0xc0, //0x00000090 movzbl       %al, %eax
	0x48, 0xba, 0x01, 0x00, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, //0x00000093 movabsq      $36028797027352577, %rdx
	0x48, 0x0f, 0xa3, 0xc2, //0x0000009d btq          %rax, %rdx
	0x0f, 0x83, 0xae, 0x00, 0x00, 0x00, //0x000000a1 jae          LBB0_9
	//0x000000a7 LBB0_10
	0x49, 0x39, 0xce, //0x000000a7 cmpq         %rcx, %r14
	0x49, 0x89, 0xca, //0x000000aa movq         %rcx, %r10
	0x4d, 0x0f, 0x47, 0xd6, //0x000000ad cmovaq       %r14, %r10
	0x31, 0xc0, //0x000000b1 xorl         %eax, %eax
	0x41, 0xbb, 0x0a, 0x00, 0x00, 0x00, //0x000000b3 movl         $10, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000000b9 .p2align 4, 0x90
	//0x000000c0 LBB0_11
	0x49, 0x39, 0xca, //0x000000c0 cmpq         %rcx, %r10
	0x0f, 0x84, 0x80, 0x00, 0x00, 0x00, //0x000000c3 je           LBB0_22
	0x41, 0x0f, 0xbe, 0x1c, 0x09, //0x000000c9 movsbl       (%r9,%rcx), %ebx
	0x8d, 0x53, 0xd0, //0x000000ce leal         $-48(%rbx), %edx
	0x80, 0xfa, 0x09, //0x000000d1 cmpb         $9, %dl
	0x0f, 0x87, 0x44, 0x00, 0x00, 0x00, //0x000000d4 ja           LBB0_17
	0x49, 0xf7, 0xe3, //0x000000da mulq         %r11
	0x0f, 0x80, 0x28, 0x00, 0x00, 0x00, //0x000000dd jo           LBB0_16
	0x48, 0x83, 0xc1, 0x01, //0x000000e3 addq         $1, %rcx
	0x83, 0xc3, 0xd0, //0x000000e7 addl         $-48, %ebx
	0x31, 0xff, //0x000000ea xorl         %edi, %edi
	0x48, 0x01, 0xd8, //0x000000ec addq         %rbx, %rax
	0x40, 0x0f, 0x92, 0xc7, //0x000000ef setb         %dil
	0x48, 0x89, 0xfa, //0x000000f3 movq         %rdi, %rdx
	0x48, 0xf7, 0xda, //0x000000f6 negq         %rdx
	0x48, 0x31, 0xd7, //0x000000f9 xorq         %rdx, %rdi
	0x0f, 0x85, 0x09, 0x00, 0x00, 0x00, //0x000000fc jne          LBB0_16
	0x48, 0x85, 0xd2, //0x00000102 testq        %rdx, %rdx
	0x0f, 0x89, 0xb5, 0xff, 0xff, 0xff, //0x00000105 jns          LBB0_11
	//0x0000010b LBB0_16
	0x48, 0x83, 0xc1, 0xff, //0x0000010b addq         $-1, %rcx
	0x48, 0x89, 0x0e, //0x0000010f movq         %rcx, (%rsi)
	0x49, 0xc7, 0x00, 0xfb, 0xff, 0xff, 0xff, //0x00000112 movq         $-5, (%r8)
	0x5b, //0x00000119 popq         %rbx
	0x41, 0x5e, //0x0000011a popq         %r14
	0x5d, //0x0000011c popq         %rbp
	0xc3, //0x0000011d retq         
	//0x0000011e LBB0_17
	0x4c, 0x39, 0xf1, //0x0000011e cmpq         %r14, %rcx
	0x0f, 0x83, 0x1f, 0x00, 0x00, 0x00, //0x00000121 jae          LBB0_21
	0x41, 0x8a, 0x14, 0x09, //0x00000127 movb         (%r9,%rcx), %dl
	0x80, 0xfa, 0x2e, //0x0000012b cmpb         $46, %dl
	0x0f, 0x84, 0x0c, 0xff, 0xff, 0xff, //0x0000012e je           LBB0_3
	0x80, 0xfa, 0x45, //0x00000134 cmpb         $69, %dl
	0x0f, 0x84, 0x03, 0xff, 0xff, 0xff, //0x00000137 je           LBB0_3
	0x80, 0xfa, 0x65, //0x0000013d cmpb         $101, %dl
	0x0f, 0x84, 0xfa, 0xfe, 0xff, 0xff, //0x00000140 je           LBB0_3
	//0x00000146 LBB0_21
	0x49, 0x89, 0xca, //0x00000146 movq         %rcx, %r10
	//0x00000149 LBB0_22
	0x4c, 0x89, 0x16, //0x00000149 movq         %r10, (%rsi)
	0x49, 0x89, 0x40, 0x10, //0x0000014c movq         %rax, $16(%r8)
	0x5b, //0x00000150 popq         %rbx
	0x41, 0x5e, //0x00000151 popq         %r14
	0x5d, //0x00000153 popq         %rbp
	0xc3, //0x00000154 retq         
	//0x00000155 LBB0_9
	0x48, 0x83, 0xc1, 0x01, //0x00000155 addq         $1, %rcx
	0x48, 0x89, 0x0e, //0x00000159 movq         %rcx, (%rsi)
	0x5b, //0x0000015c popq         %rbx
	0x41, 0x5e, //0x0000015d popq         %r14
	0x5d, //0x0000015f popq         %rbp
	0xc3, //0x00000160 retq         
	0x00, 0x00, 0x00, //0x00000161 .p2align 2, 0x00
	//0x00000164 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000164 .long 2
}
 
