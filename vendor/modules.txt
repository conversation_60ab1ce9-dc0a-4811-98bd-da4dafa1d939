# git.sho/ComputeHub/ComputeHub v0.0.0-20250807090818-e79876f98642
## explicit; go 1.24.4
git.sho/ComputeHub/ComputeHub
git.sho/ComputeHub/ComputeHub/pkg/auth
git.sho/ComputeHub/ComputeHub/pkg/models
git.sho/ComputeHub/ComputeHub/pkg/payments
git.sho/ComputeHub/ComputeHub/pkg/providers
git.sho/ComputeHub/ComputeHub/pkg/service
git.sho/ComputeHub/ComputeHub/pkg/sms
git.sho/ComputeHub/ComputeHub/utils
# git.sho/ComputeHub/HubCommon v0.0.0-20250807053825-a07be99e78cf
## explicit; go 1.24.4
git.sho/ComputeHub/HubCommon/db
git.sho/ComputeHub/HubCommon/pagination
git.sho/ComputeHub/HubCommon/slice
git.sho/ComputeHub/HubCommon/version
# git.sho/ComputeHub/vmm v0.0.0-20250807034002-0abd374bd32c
## explicit; go 1.24.0
git.sho/ComputeHub/vmm
git.sho/ComputeHub/vmm/pkg/api
# github.com/abiosoft/ishell v2.0.0+incompatible
## explicit
# github.com/abiosoft/ishell/v2 v2.0.2
## explicit; go 1.16
github.com/abiosoft/ishell/v2
# github.com/abiosoft/readline v0.0.0-20180607040430-155bce2042db
## explicit
github.com/abiosoft/readline
# github.com/bytedance/sonic v1.13.3
## explicit; go 1.17
github.com/bytedance/sonic
github.com/bytedance/sonic/ast
github.com/bytedance/sonic/decoder
github.com/bytedance/sonic/encoder
github.com/bytedance/sonic/internal/caching
github.com/bytedance/sonic/internal/compat
github.com/bytedance/sonic/internal/cpu
github.com/bytedance/sonic/internal/decoder/api
github.com/bytedance/sonic/internal/decoder/consts
github.com/bytedance/sonic/internal/decoder/errors
github.com/bytedance/sonic/internal/decoder/jitdec
github.com/bytedance/sonic/internal/decoder/optdec
github.com/bytedance/sonic/internal/encoder
github.com/bytedance/sonic/internal/encoder/alg
github.com/bytedance/sonic/internal/encoder/ir
github.com/bytedance/sonic/internal/encoder/vars
github.com/bytedance/sonic/internal/encoder/vm
github.com/bytedance/sonic/internal/encoder/x86
github.com/bytedance/sonic/internal/envs
github.com/bytedance/sonic/internal/jit
github.com/bytedance/sonic/internal/native
github.com/bytedance/sonic/internal/native/avx2
github.com/bytedance/sonic/internal/native/neon
github.com/bytedance/sonic/internal/native/sse
github.com/bytedance/sonic/internal/native/types
github.com/bytedance/sonic/internal/optcaching
github.com/bytedance/sonic/internal/resolver
github.com/bytedance/sonic/internal/rt
github.com/bytedance/sonic/internal/utils
github.com/bytedance/sonic/option
github.com/bytedance/sonic/unquote
github.com/bytedance/sonic/utf8
# github.com/bytedance/sonic/loader v0.2.4
## explicit; go 1.16
github.com/bytedance/sonic/loader
github.com/bytedance/sonic/loader/internal/abi
github.com/bytedance/sonic/loader/internal/iasm/expr
github.com/bytedance/sonic/loader/internal/iasm/x86_64
github.com/bytedance/sonic/loader/internal/rt
# github.com/cloudwego/base64x v0.1.5
## explicit; go 1.16
github.com/cloudwego/base64x
github.com/cloudwego/base64x/internal/native
github.com/cloudwego/base64x/internal/native/avx2
github.com/cloudwego/base64x/internal/native/sse
github.com/cloudwego/base64x/internal/rt
# github.com/common-nighthawk/go-figure v0.0.0-20210622060536-734e95fb86be
## explicit
github.com/common-nighthawk/go-figure
# github.com/cpuguy83/go-md2man/v2 v2.0.7
## explicit; go 1.12
github.com/cpuguy83/go-md2man/v2/md2man
# github.com/denisbrodbeck/machineid v1.0.1
## explicit
github.com/denisbrodbeck/machineid
# github.com/dustin/go-humanize v1.0.1
## explicit; go 1.16
github.com/dustin/go-humanize
# github.com/fatih/color v1.18.0
## explicit; go 1.17
github.com/fatih/color
# github.com/flynn-archive/go-shlex v0.0.0-20150515145356-3f9db97f8568
## explicit
github.com/flynn-archive/go-shlex
# github.com/fsnotify/fsnotify v1.9.0
## explicit; go 1.17
github.com/fsnotify/fsnotify
github.com/fsnotify/fsnotify/internal
# github.com/gabriel-vasile/mimetype v1.4.9
## explicit; go 1.23.0
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/gaoxing520/errors v0.0.0-20250801054007-14c32b7a32dc
## explicit; go 1.23.0
github.com/gaoxing520/errors
# github.com/gin-contrib/gzip v1.2.0
## explicit; go 1.21.0
github.com/gin-contrib/gzip
# github.com/gin-contrib/sse v1.1.0
## explicit; go 1.23
github.com/gin-contrib/sse
# github.com/gin-gonic/gin v1.10.1
## explicit; go 1.20
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.27.0
## explicit; go 1.20
github.com/go-playground/validator/v10
# github.com/go-resty/resty/v2 v2.16.5
## explicit; go 1.20
github.com/go-resty/resty/v2
github.com/go-resty/resty/v2/shellescape
# github.com/go-viper/mapstructure/v2 v2.3.0
## explicit; go 1.18
github.com/go-viper/mapstructure/v2
github.com/go-viper/mapstructure/v2/internal/errors
# github.com/goccy/go-json v0.10.5
## explicit; go 1.19
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/golang-jwt/jwt/v5 v5.2.2
## explicit; go 1.18
github.com/golang-jwt/jwt/v5
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/jinzhu/copier v0.4.0
## explicit; go 1.13
github.com/jinzhu/copier
# github.com/jinzhu/gorm v1.9.16
## explicit; go 1.12
github.com/jinzhu/gorm
github.com/jinzhu/gorm/dialects/postgres
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/klauspost/cpuid/v2 v2.2.11
## explicit; go 1.22
github.com/klauspost/cpuid/v2
# github.com/leodido/go-urn v1.4.0
## explicit; go 1.18
github.com/leodido/go-urn
github.com/leodido/go-urn/scim/schema
# github.com/lib/pq v1.10.9
## explicit; go 1.13
github.com/lib/pq
github.com/lib/pq/hstore
github.com/lib/pq/oid
github.com/lib/pq/scram
# github.com/mattn/go-colorable v0.1.14
## explicit; go 1.18
github.com/mattn/go-colorable
# github.com/mattn/go-isatty v0.0.20
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/mattn/go-runewidth v0.0.16
## explicit; go 1.9
github.com/mattn/go-runewidth
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/olekukonko/errors v1.1.0
## explicit; go 1.21
github.com/olekukonko/errors
# github.com/olekukonko/ll v0.0.9
## explicit; go 1.21
github.com/olekukonko/ll
github.com/olekukonko/ll/lh
github.com/olekukonko/ll/lx
# github.com/olekukonko/tablewriter v1.0.8
## explicit; go 1.21
github.com/olekukonko/tablewriter
github.com/olekukonko/tablewriter/pkg/twwarp
github.com/olekukonko/tablewriter/pkg/twwidth
github.com/olekukonko/tablewriter/renderer
github.com/olekukonko/tablewriter/tw
# github.com/pelletier/go-toml/v2 v2.2.4
## explicit; go 1.21.0
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/rivo/uniseg v0.4.7
## explicit; go 1.18
github.com/rivo/uniseg
# github.com/rs/zerolog v1.34.0
## explicit; go 1.15
github.com/rs/zerolog
github.com/rs/zerolog/internal/cbor
github.com/rs/zerolog/internal/json
github.com/rs/zerolog/log
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
github.com/russross/blackfriday/v2
# github.com/sagikazarmark/locafero v0.9.0
## explicit; go 1.23.0
github.com/sagikazarmark/locafero
# github.com/satori/go.uuid v1.2.0
## explicit
github.com/satori/go.uuid
# github.com/shopspring/decimal v1.4.0
## explicit; go 1.10
github.com/shopspring/decimal
# github.com/skip2/go-qrcode v0.0.0-20200617195104-da1b6568686e
## explicit; go 1.13
github.com/skip2/go-qrcode
github.com/skip2/go-qrcode/bitset
github.com/skip2/go-qrcode/reedsolomon
# github.com/sourcegraph/conc v0.3.0
## explicit; go 1.19
github.com/sourcegraph/conc
github.com/sourcegraph/conc/internal/multierror
github.com/sourcegraph/conc/iter
github.com/sourcegraph/conc/panics
# github.com/spf13/afero v1.14.0
## explicit; go 1.23.0
github.com/spf13/afero
github.com/spf13/afero/internal/common
github.com/spf13/afero/mem
# github.com/spf13/cast v1.9.2
## explicit; go 1.21.0
github.com/spf13/cast
github.com/spf13/cast/internal
# github.com/spf13/pflag v1.0.6
## explicit; go 1.12
github.com/spf13/pflag
# github.com/spf13/viper v1.20.1
## explicit; go 1.21.0
github.com/spf13/viper
github.com/spf13/viper/internal/encoding/dotenv
github.com/spf13/viper/internal/encoding/json
github.com/spf13/viper/internal/encoding/toml
github.com/spf13/viper/internal/encoding/yaml
github.com/spf13/viper/internal/features
# github.com/subosito/gotenv v1.6.0
## explicit; go 1.18
github.com/subosito/gotenv
# github.com/twitchyliquid64/golang-asm v0.15.1
## explicit; go 1.13
github.com/twitchyliquid64/golang-asm/asm/arch
github.com/twitchyliquid64/golang-asm/bio
github.com/twitchyliquid64/golang-asm/dwarf
github.com/twitchyliquid64/golang-asm/goobj
github.com/twitchyliquid64/golang-asm/obj
github.com/twitchyliquid64/golang-asm/obj/arm
github.com/twitchyliquid64/golang-asm/obj/arm64
github.com/twitchyliquid64/golang-asm/obj/mips
github.com/twitchyliquid64/golang-asm/obj/ppc64
github.com/twitchyliquid64/golang-asm/obj/riscv
github.com/twitchyliquid64/golang-asm/obj/s390x
github.com/twitchyliquid64/golang-asm/obj/wasm
github.com/twitchyliquid64/golang-asm/obj/x86
github.com/twitchyliquid64/golang-asm/objabi
github.com/twitchyliquid64/golang-asm/src
github.com/twitchyliquid64/golang-asm/sys
github.com/twitchyliquid64/golang-asm/unsafeheader
# github.com/ugorji/go/codec v1.3.0
## explicit; go 1.21
github.com/ugorji/go/codec
# github.com/urfave/cli/v2 v2.27.7
## explicit; go 1.18
github.com/urfave/cli/v2
# github.com/wechatpay-apiv3/wechatpay-go v0.2.20
## explicit; go 1.16
github.com/wechatpay-apiv3/wechatpay-go/core
github.com/wechatpay-apiv3/wechatpay-go/core/auth
github.com/wechatpay-apiv3/wechatpay-go/core/auth/credentials
github.com/wechatpay-apiv3/wechatpay-go/core/auth/signers
github.com/wechatpay-apiv3/wechatpay-go/core/auth/validators
github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers
github.com/wechatpay-apiv3/wechatpay-go/core/cipher
github.com/wechatpay-apiv3/wechatpay-go/core/cipher/ciphers
github.com/wechatpay-apiv3/wechatpay-go/core/cipher/decryptors
github.com/wechatpay-apiv3/wechatpay-go/core/cipher/encryptors
github.com/wechatpay-apiv3/wechatpay-go/core/consts
github.com/wechatpay-apiv3/wechatpay-go/core/downloader
github.com/wechatpay-apiv3/wechatpay-go/core/notify
github.com/wechatpay-apiv3/wechatpay-go/core/option
github.com/wechatpay-apiv3/wechatpay-go/services
github.com/wechatpay-apiv3/wechatpay-go/services/payments
github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi
github.com/wechatpay-apiv3/wechatpay-go/utils
github.com/wechatpay-apiv3/wechatpay-go/utils/task
# github.com/xrash/smetrics v0.0.0-20250705151800-55b8f293f342
## explicit; go 1.15
github.com/xrash/smetrics
# go.uber.org/multierr v1.11.0
## explicit; go 1.19
go.uber.org/multierr
# golang.org/x/arch v0.19.0
## explicit; go 1.23.0
golang.org/x/arch/x86/x86asm
# golang.org/x/crypto v0.40.0
## explicit; go 1.23.0
golang.org/x/crypto/bcrypt
golang.org/x/crypto/blowfish
golang.org/x/crypto/sha3
# golang.org/x/net v0.42.0
## explicit; go 1.23.0
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/httpcommon
golang.org/x/net/publicsuffix
# golang.org/x/sys v0.34.0
## explicit; go 1.23.0
golang.org/x/sys/cpu
golang.org/x/sys/unix
golang.org/x/sys/windows
golang.org/x/sys/windows/registry
# golang.org/x/text v0.27.0
## explicit; go 1.23.0
golang.org/x/text/cases
golang.org/x/text/encoding
golang.org/x/text/encoding/internal
golang.org/x/text/encoding/internal/identifier
golang.org/x/text/encoding/unicode
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/internal/utf8internal
golang.org/x/text/language
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# google.golang.org/protobuf v1.36.6
## explicit; go 1.22
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
# gopkg.in/resty.v1 v1.12.0
## explicit
gopkg.in/resty.v1
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/gorm v1.30.1
## explicit; go 1.18
gorm.io/gorm
gorm.io/gorm/clause
gorm.io/gorm/internal/lru
gorm.io/gorm/internal/stmt_store
gorm.io/gorm/logger
gorm.io/gorm/schema
gorm.io/gorm/utils
