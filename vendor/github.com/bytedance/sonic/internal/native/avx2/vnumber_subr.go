// +build !noasm !appengine
// Code generated by asm2asm, DO NOT EDIT.

package avx2

import (
	`github.com/bytedance/sonic/loader`
)

const (
    _entry__vnumber = 128
)

const (
    _stack__vnumber = 136
)

const (
    _size__vnumber = 8496
)

var (
    _pcsp__vnumber = [][2]uint32{
        {0x1, 0},
        {0x6, 8},
        {0x8, 16},
        {0xa, 24},
        {0xc, 32},
        {0xd, 40},
        {0x11, 48},
        {0x211f, 136},
        {0x2120, 48},
        {0x2122, 40},
        {0x2124, 32},
        {0x2126, 24},
        {0x2128, 16},
        {0x2129, 8},
        {0x2130, 0},
    }
)

var _cfunc_vnumber = []loader.CFunc{
    {"_vnumber_entry", 0,  _entry__vnumber, 0, nil},
    {"_vnumber", _entry__vnumber, _size__vnumber, _stack__vnumber, _pcsp__vnumber},
}
