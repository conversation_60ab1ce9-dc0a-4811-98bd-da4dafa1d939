package service

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"devgitlab.lianoid.com/jasonni/eaas-common/log"
	"git.sho/ComputeHub/ComputeHub/pkg/models"
	"git.sho/ComputeHub/ComputeHub/utils"
)

const (
	OptionsTypeAppSpec = "app-spec"
)

func (s *ComputeHubService) HttpHandleGetOptions(c *gin.Context) {
	typ := c.Param("type")
	var options map[string][]string
	switch typ {
	case OptionsTypeAppSpec:
		if appSpecOptions, err := models.GetOptionsForAppSpec(s.db); err != nil {
			utils.RespondErrorToHttpClient(c, err)
			return
		} else {
			options = appSpecOptions
		}
	default:
		log.Errorf("unknown options type (%s)", typ)
		utils.RespondToHttpClient[any](c, http.StatusNotFound, nil, nil, true)
		return
	}

	utils.RespondDataToHttpClient(c, options)
}
