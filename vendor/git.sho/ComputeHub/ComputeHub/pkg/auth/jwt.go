package auth

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

type JWTManager struct {
	accessSecret  []byte
	refreshSecret []byte

	accessExpSec  int64
	refreshExpSec int64
}

func NewJWTManager(accessSecret string, accessExp int64, refreshSecret string, refreshExp int64) *JWTManager {
	return &JWTManager{
		accessSecret:  []byte(accessSecret),
		refreshSecret: []byte(refreshSecret),
		accessExpSec:  accessExp,
		refreshExpSec: refreshExp,
	}
}

type Claims struct {
	PlatformKey string `json:"platformKey"`
	UserID      uint   `json:"userId"`
	MachineID   string `json:"machineId"`
	jwt.RegisteredClaims
}

// IssueAccess issues a short‐lived access token
func (j *JWTManager) IssueAccess(pk string, uid uint, mid string) (string, error) {
	if len(j.accessSecret) == 0 {
		return "", errors.New("jwt: access secret not configured")
	}
	now := time.Now()
	c := Claims{
		PlatformKey: pk, UserID: uid, MachineID: mid,
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(j.accessExpSec) * time.Second)),
		},
	}
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, c)
	return t.SignedString(j.accessSecret)
}

// IssueRefresh issues a long‐lived refresh token
func (j *JWTManager) IssueRefresh(pk string, uid uint, mid string) (string, error) {
	if len(j.refreshSecret) == 0 {
		return "", errors.New("jwt: refresh secret not configured")
	}
	now := time.Now()
	c := Claims{
		PlatformKey: pk, UserID: uid, MachineID: mid,
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(j.refreshExpSec) * time.Second)),
		},
	}
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, c)
	return t.SignedString(j.refreshSecret)
}

// ParseAccess parses an access token
func (j *JWTManager) ParseAccess(tok string) (*Claims, error) {
	return j.parseWith(tok, j.accessSecret)
}

// ParseRefresh parses a refresh token
func (j *JWTManager) ParseRefresh(tok string) (*Claims, error) {
	return j.parseWith(tok, j.refreshSecret)
}

func (j *JWTManager) parseWith(tokStr string, secret []byte) (*Claims, error) {
	if len(secret) == 0 {
		return nil, errors.New("jwt: secret not configured")
	}
	token, err := jwt.ParseWithClaims(tokStr, &Claims{}, func(_ *jwt.Token) (interface{}, error) {
		return secret, nil
	})
	if err != nil {
		return nil, err
	}
	if c, ok := token.Claims.(*Claims); ok && token.Valid {
		return c, nil
	}
	return nil, errors.New("jwt: invalid token")
}
