// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

//go:build go1.10 && !go1.13

package cases

// UnicodeVersion is the Unicode version from which the tables in this package are derived.
const UnicodeVersion = "10.0.0"

var xorData string = "" + // Size: 185 bytes
	"\x00\x06\x07\x00\x01?\x00\x0f\x03\x00\x0f\x12\x00\x0f\x1f\x00\x0f\x1d" +
	"\x00\x01\x13\x00\x0f\x16\x00\x0f\x0b\x00\x0f3\x00\x0f7\x00\x01#\x00\x0f?" +
	"\x00\x0e'\x00\x0f/\x00\x0e>\x00\x0f*\x00\x0c&\x00\x0c*\x00\x0c;\x00\x0c9" +
	"\x00\x0c%\x00\x01\x08\x00\x03\x0d\x00\x03\x09\x00\x02\x06\x00\x02\x02" +
	"\x00\x02\x0c\x00\x01\x00\x00\x01\x03\x00\x01\x01\x00\x01 \x00\x01\x0c" +
	"\x00\x01\x10\x00\x03\x10\x00\x036 \x00\x037 \x00\x0b#\x10\x00\x0b 0\x00" +
	"\x0b!\x10\x00\x0b!0\x00\x0b(\x04\x00\x03\x04\x1e\x00\x03\x0a\x00\x02:" +
	"\x00\x02>\x00\x02,\x00\x02\x00\x00\x02\x10\x00\x01<\x00\x01&\x00\x01*" +
	"\x00\x01.\x00\x010\x003 \x00\x01\x18\x00\x01(\x00\x01\x1e\x00\x01\x22"

var exceptions string = "" + // Size: 2068 bytes
	"\x00\x12\x12μΜΜ\x12\x12ssSSSs\x13\x18i̇i̇\x10\x09II\x13\x1bʼnʼNʼN\x11" +
	"\x09sSS\x12\x12ǆǆǅ\x12\x12ǆǆǄ\x10\x12Ǆǅ\x12\x12ǉǉǈ\x12\x12ǉǉǇ\x10\x12Ǉǈ" +
	"\x12\x12ǌǌǋ\x12\x12ǌǌǊ\x10\x12Ǌǋ\x13\x1bǰJ̌J̌\x12\x12ǳǳǲ\x12\x12ǳǳǱ\x10" +
	"\x12Ǳǲ\x13\x18ⱥⱥ\x13\x18ⱦⱦ\x10\x1bⱾⱾ\x10\x1bⱿⱿ\x10\x1bⱯⱯ\x10\x1bⱭⱭ\x10" +
	"\x1bⱰⱰ\x10\x1bꞫꞫ\x10\x1bꞬꞬ\x10\x1bꞍꞍ\x10\x1bꞪꞪ\x10\x1bꞮꞮ\x10\x1bⱢⱢ\x10" +
	"\x1bꞭꞭ\x10\x1bⱮⱮ\x10\x1bⱤⱤ\x10\x1bꞱꞱ\x10\x1bꞲꞲ\x10\x1bꞰꞰ2\x12ιΙΙ\x166ΐ" +
	"Ϊ́Ϊ́\x166ΰΫ́Ϋ́\x12\x12σΣΣ\x12\x12βΒΒ\x12\x12θΘΘ\x12\x12φΦΦ\x12" +
	"\x12πΠΠ\x12\x12κΚΚ\x12\x12ρΡΡ\x12\x12εΕΕ\x14$եւԵՒԵւ\x12\x12вВВ\x12\x12дД" +
	"Д\x12\x12оОО\x12\x12сСС\x12\x12тТТ\x12\x12тТТ\x12\x12ъЪЪ\x12\x12ѣѢѢ\x13" +
	"\x1bꙋꙊꙊ\x13\x1bẖH̱H̱\x13\x1bẗT̈T̈\x13\x1bẘW̊W̊\x13\x1bẙY̊Y̊\x13\x1ba" +
	"ʾAʾAʾ\x13\x1bṡṠṠ\x12\x10ssß\x14$ὐΥ̓Υ̓\x166ὒΥ̓̀Υ̓̀\x166ὔΥ̓́Υ̓́\x166" +
	"ὖΥ̓͂Υ̓͂\x15+ἀιἈΙᾈ\x15+ἁιἉΙᾉ\x15+ἂιἊΙᾊ\x15+ἃιἋΙᾋ\x15+ἄιἌΙᾌ\x15+ἅιἍΙᾍ" +
	"\x15+ἆιἎΙᾎ\x15+ἇιἏΙᾏ\x15\x1dἀιᾀἈΙ\x15\x1dἁιᾁἉΙ\x15\x1dἂιᾂἊΙ\x15\x1dἃιᾃἋΙ" +
	"\x15\x1dἄιᾄἌΙ\x15\x1dἅιᾅἍΙ\x15\x1dἆιᾆἎΙ\x15\x1dἇιᾇἏΙ\x15+ἠιἨΙᾘ\x15+ἡιἩΙᾙ" +
	"\x15+ἢιἪΙᾚ\x15+ἣιἫΙᾛ\x15+ἤιἬΙᾜ\x15+ἥιἭΙᾝ\x15+ἦιἮΙᾞ\x15+ἧιἯΙᾟ\x15\x1dἠιᾐἨ" +
	"Ι\x15\x1dἡιᾑἩΙ\x15\x1dἢιᾒἪΙ\x15\x1dἣιᾓἫΙ\x15\x1dἤιᾔἬΙ\x15\x1dἥιᾕἭΙ\x15" +
	"\x1dἦιᾖἮΙ\x15\x1dἧιᾗἯΙ\x15+ὠιὨΙᾨ\x15+ὡιὩΙᾩ\x15+ὢιὪΙᾪ\x15+ὣιὫΙᾫ\x15+ὤιὬΙᾬ" +
	"\x15+ὥιὭΙᾭ\x15+ὦιὮΙᾮ\x15+ὧιὯΙᾯ\x15\x1dὠιᾠὨΙ\x15\x1dὡιᾡὩΙ\x15\x1dὢιᾢὪΙ" +
	"\x15\x1dὣιᾣὫΙ\x15\x1dὤιᾤὬΙ\x15\x1dὥιᾥὭΙ\x15\x1dὦιᾦὮΙ\x15\x1dὧιᾧὯΙ\x15-ὰι" +
	"ᾺΙᾺͅ\x14#αιΑΙᾼ\x14$άιΆΙΆͅ\x14$ᾶΑ͂Α͂\x166ᾶιΑ͂Ιᾼ͂\x14\x1cαιᾳΑΙ\x12" +
	"\x12ιΙΙ\x15-ὴιῊΙῊͅ\x14#ηιΗΙῌ\x14$ήιΉΙΉͅ\x14$ῆΗ͂Η͂\x166ῆιΗ͂Ιῌ͂\x14\x1c" +
	"ηιῃΗΙ\x166ῒΪ̀Ϊ̀\x166ΐΪ́Ϊ́\x14$ῖΙ͂Ι͂\x166ῗΪ͂Ϊ͂\x166ῢΫ̀Ϋ" +
	"̀\x166ΰΫ́Ϋ́\x14$ῤΡ̓Ρ̓\x14$ῦΥ͂Υ͂\x166ῧΫ͂Ϋ͂\x15-ὼιῺΙῺͅ\x14#ωιΩΙ" +
	"ῼ\x14$ώιΏΙΏͅ\x14$ῶΩ͂Ω͂\x166ῶιΩ͂Ιῼ͂\x14\x1cωιῳΩΙ\x12\x10ωω\x11\x08kk" +
	"\x12\x10åå\x12\x10ɫɫ\x12\x10ɽɽ\x10\x12ȺȺ\x10\x12ȾȾ\x12\x10ɑɑ\x12\x10ɱɱ" +
	"\x12\x10ɐɐ\x12\x10ɒɒ\x12\x10ȿȿ\x12\x10ɀɀ\x12\x10ɥɥ\x12\x10ɦɦ\x12\x10ɜɜ" +
	"\x12\x10ɡɡ\x12\x10ɬɬ\x12\x10ɪɪ\x12\x10ʞʞ\x12\x10ʇʇ\x12\x10ʝʝ\x12\x12ffFF" +
	"Ff\x12\x12fiFIFi\x12\x12flFLFl\x13\x1bffiFFIFfi\x13\x1bfflFFLFfl\x12\x12" +
	"stSTSt\x12\x12stSTSt\x14$մնՄՆՄն\x14$մեՄԵՄե\x14$միՄԻՄի\x14$վնՎՆՎն\x14$մխՄ" +
	"ԽՄխ"

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *caseTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return caseValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = caseIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *caseTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return caseValues[c0]
	}
	i := caseIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *caseTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return caseValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := caseIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = caseIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = caseIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *caseTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return caseValues[c0]
	}
	i := caseIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = caseIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// caseTrie. Total size: 11892 bytes (11.61 KiB). Checksum: c6f15484b7653775.
type caseTrie struct{}

func newCaseTrie(i int) *caseTrie {
	return &caseTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *caseTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	case n < 18:
		return uint16(caseValues[n<<6+uint32(b)])
	default:
		n -= 18
		return uint16(sparse.lookup(n, b))
	}
}

// caseValues: 20 blocks, 1280 entries, 2560 bytes
// The third block is the zero block.
var caseValues = [1280]uint16{
	// Block 0x0, offset 0x0
	0x27: 0x0054,
	0x2e: 0x0054,
	0x30: 0x0010, 0x31: 0x0010, 0x32: 0x0010, 0x33: 0x0010, 0x34: 0x0010, 0x35: 0x0010,
	0x36: 0x0010, 0x37: 0x0010, 0x38: 0x0010, 0x39: 0x0010, 0x3a: 0x0054,
	// Block 0x1, offset 0x40
	0x41: 0x2013, 0x42: 0x2013, 0x43: 0x2013, 0x44: 0x2013, 0x45: 0x2013,
	0x46: 0x2013, 0x47: 0x2013, 0x48: 0x2013, 0x49: 0x2013, 0x4a: 0x2013, 0x4b: 0x2013,
	0x4c: 0x2013, 0x4d: 0x2013, 0x4e: 0x2013, 0x4f: 0x2013, 0x50: 0x2013, 0x51: 0x2013,
	0x52: 0x2013, 0x53: 0x2013, 0x54: 0x2013, 0x55: 0x2013, 0x56: 0x2013, 0x57: 0x2013,
	0x58: 0x2013, 0x59: 0x2013, 0x5a: 0x2013,
	0x5e: 0x0004, 0x5f: 0x0010, 0x60: 0x0004, 0x61: 0x2012, 0x62: 0x2012, 0x63: 0x2012,
	0x64: 0x2012, 0x65: 0x2012, 0x66: 0x2012, 0x67: 0x2012, 0x68: 0x2012, 0x69: 0x2012,
	0x6a: 0x2012, 0x6b: 0x2012, 0x6c: 0x2012, 0x6d: 0x2012, 0x6e: 0x2012, 0x6f: 0x2012,
	0x70: 0x2012, 0x71: 0x2012, 0x72: 0x2012, 0x73: 0x2012, 0x74: 0x2012, 0x75: 0x2012,
	0x76: 0x2012, 0x77: 0x2012, 0x78: 0x2012, 0x79: 0x2012, 0x7a: 0x2012,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x0852, 0xc1: 0x0b53, 0xc2: 0x0113, 0xc3: 0x0112, 0xc4: 0x0113, 0xc5: 0x0112,
	0xc6: 0x0b53, 0xc7: 0x0f13, 0xc8: 0x0f12, 0xc9: 0x0e53, 0xca: 0x1153, 0xcb: 0x0713,
	0xcc: 0x0712, 0xcd: 0x0012, 0xce: 0x1453, 0xcf: 0x1753, 0xd0: 0x1a53, 0xd1: 0x0313,
	0xd2: 0x0312, 0xd3: 0x1d53, 0xd4: 0x2053, 0xd5: 0x2352, 0xd6: 0x2653, 0xd7: 0x2653,
	0xd8: 0x0113, 0xd9: 0x0112, 0xda: 0x2952, 0xdb: 0x0012, 0xdc: 0x1d53, 0xdd: 0x2c53,
	0xde: 0x2f52, 0xdf: 0x3253, 0xe0: 0x0113, 0xe1: 0x0112, 0xe2: 0x0113, 0xe3: 0x0112,
	0xe4: 0x0113, 0xe5: 0x0112, 0xe6: 0x3553, 0xe7: 0x0f13, 0xe8: 0x0f12, 0xe9: 0x3853,
	0xea: 0x0012, 0xeb: 0x0012, 0xec: 0x0113, 0xed: 0x0112, 0xee: 0x3553, 0xef: 0x1f13,
	0xf0: 0x1f12, 0xf1: 0x3b53, 0xf2: 0x3e53, 0xf3: 0x0713, 0xf4: 0x0712, 0xf5: 0x0313,
	0xf6: 0x0312, 0xf7: 0x4153, 0xf8: 0x0113, 0xf9: 0x0112, 0xfa: 0x0012, 0xfb: 0x0010,
	0xfc: 0x0113, 0xfd: 0x0112, 0xfe: 0x0012, 0xff: 0x4452,
	// Block 0x4, offset 0x100
	0x100: 0x0010, 0x101: 0x0010, 0x102: 0x0010, 0x103: 0x0010, 0x104: 0x02db, 0x105: 0x0359,
	0x106: 0x03da, 0x107: 0x043b, 0x108: 0x04b9, 0x109: 0x053a, 0x10a: 0x059b, 0x10b: 0x0619,
	0x10c: 0x069a, 0x10d: 0x0313, 0x10e: 0x0312, 0x10f: 0x1f13, 0x110: 0x1f12, 0x111: 0x0313,
	0x112: 0x0312, 0x113: 0x0713, 0x114: 0x0712, 0x115: 0x0313, 0x116: 0x0312, 0x117: 0x0f13,
	0x118: 0x0f12, 0x119: 0x0313, 0x11a: 0x0312, 0x11b: 0x0713, 0x11c: 0x0712, 0x11d: 0x1452,
	0x11e: 0x0113, 0x11f: 0x0112, 0x120: 0x0113, 0x121: 0x0112, 0x122: 0x0113, 0x123: 0x0112,
	0x124: 0x0113, 0x125: 0x0112, 0x126: 0x0113, 0x127: 0x0112, 0x128: 0x0113, 0x129: 0x0112,
	0x12a: 0x0113, 0x12b: 0x0112, 0x12c: 0x0113, 0x12d: 0x0112, 0x12e: 0x0113, 0x12f: 0x0112,
	0x130: 0x06fa, 0x131: 0x07ab, 0x132: 0x0829, 0x133: 0x08aa, 0x134: 0x0113, 0x135: 0x0112,
	0x136: 0x2353, 0x137: 0x4453, 0x138: 0x0113, 0x139: 0x0112, 0x13a: 0x0113, 0x13b: 0x0112,
	0x13c: 0x0113, 0x13d: 0x0112, 0x13e: 0x0113, 0x13f: 0x0112,
	// Block 0x5, offset 0x140
	0x140: 0x0a8a, 0x141: 0x0313, 0x142: 0x0312, 0x143: 0x0853, 0x144: 0x4753, 0x145: 0x4a53,
	0x146: 0x0113, 0x147: 0x0112, 0x148: 0x0113, 0x149: 0x0112, 0x14a: 0x0113, 0x14b: 0x0112,
	0x14c: 0x0113, 0x14d: 0x0112, 0x14e: 0x0113, 0x14f: 0x0112, 0x150: 0x0b0a, 0x151: 0x0b8a,
	0x152: 0x0c0a, 0x153: 0x0b52, 0x154: 0x0b52, 0x155: 0x0012, 0x156: 0x0e52, 0x157: 0x1152,
	0x158: 0x0012, 0x159: 0x1752, 0x15a: 0x0012, 0x15b: 0x1a52, 0x15c: 0x0c8a, 0x15d: 0x0012,
	0x15e: 0x0012, 0x15f: 0x0012, 0x160: 0x1d52, 0x161: 0x0d0a, 0x162: 0x0012, 0x163: 0x2052,
	0x164: 0x0012, 0x165: 0x0d8a, 0x166: 0x0e0a, 0x167: 0x0012, 0x168: 0x2652, 0x169: 0x2652,
	0x16a: 0x0e8a, 0x16b: 0x0f0a, 0x16c: 0x0f8a, 0x16d: 0x0012, 0x16e: 0x0012, 0x16f: 0x1d52,
	0x170: 0x0012, 0x171: 0x100a, 0x172: 0x2c52, 0x173: 0x0012, 0x174: 0x0012, 0x175: 0x3252,
	0x176: 0x0012, 0x177: 0x0012, 0x178: 0x0012, 0x179: 0x0012, 0x17a: 0x0012, 0x17b: 0x0012,
	0x17c: 0x0012, 0x17d: 0x108a, 0x17e: 0x0012, 0x17f: 0x0012,
	// Block 0x6, offset 0x180
	0x180: 0x3552, 0x181: 0x0012, 0x182: 0x0012, 0x183: 0x3852, 0x184: 0x0012, 0x185: 0x0012,
	0x186: 0x0012, 0x187: 0x110a, 0x188: 0x3552, 0x189: 0x4752, 0x18a: 0x3b52, 0x18b: 0x3e52,
	0x18c: 0x4a52, 0x18d: 0x0012, 0x18e: 0x0012, 0x18f: 0x0012, 0x190: 0x0012, 0x191: 0x0012,
	0x192: 0x4152, 0x193: 0x0012, 0x194: 0x0010, 0x195: 0x0012, 0x196: 0x0012, 0x197: 0x0012,
	0x198: 0x0012, 0x199: 0x0012, 0x19a: 0x0012, 0x19b: 0x0012, 0x19c: 0x0012, 0x19d: 0x118a,
	0x19e: 0x120a, 0x19f: 0x0012, 0x1a0: 0x0012, 0x1a1: 0x0012, 0x1a2: 0x0012, 0x1a3: 0x0012,
	0x1a4: 0x0012, 0x1a5: 0x0012, 0x1a6: 0x0012, 0x1a7: 0x0012, 0x1a8: 0x0012, 0x1a9: 0x0012,
	0x1aa: 0x0012, 0x1ab: 0x0012, 0x1ac: 0x0012, 0x1ad: 0x0012, 0x1ae: 0x0012, 0x1af: 0x0012,
	0x1b0: 0x0015, 0x1b1: 0x0015, 0x1b2: 0x0015, 0x1b3: 0x0015, 0x1b4: 0x0015, 0x1b5: 0x0015,
	0x1b6: 0x0015, 0x1b7: 0x0015, 0x1b8: 0x0015, 0x1b9: 0x0014, 0x1ba: 0x0014, 0x1bb: 0x0014,
	0x1bc: 0x0014, 0x1bd: 0x0014, 0x1be: 0x0014, 0x1bf: 0x0014,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x0024, 0x1c1: 0x0024, 0x1c2: 0x0024, 0x1c3: 0x0024, 0x1c4: 0x0024, 0x1c5: 0x128d,
	0x1c6: 0x0024, 0x1c7: 0x0034, 0x1c8: 0x0034, 0x1c9: 0x0034, 0x1ca: 0x0024, 0x1cb: 0x0024,
	0x1cc: 0x0024, 0x1cd: 0x0034, 0x1ce: 0x0034, 0x1cf: 0x0014, 0x1d0: 0x0024, 0x1d1: 0x0024,
	0x1d2: 0x0024, 0x1d3: 0x0034, 0x1d4: 0x0034, 0x1d5: 0x0034, 0x1d6: 0x0034, 0x1d7: 0x0024,
	0x1d8: 0x0034, 0x1d9: 0x0034, 0x1da: 0x0034, 0x1db: 0x0024, 0x1dc: 0x0034, 0x1dd: 0x0034,
	0x1de: 0x0034, 0x1df: 0x0034, 0x1e0: 0x0034, 0x1e1: 0x0034, 0x1e2: 0x0034, 0x1e3: 0x0024,
	0x1e4: 0x0024, 0x1e5: 0x0024, 0x1e6: 0x0024, 0x1e7: 0x0024, 0x1e8: 0x0024, 0x1e9: 0x0024,
	0x1ea: 0x0024, 0x1eb: 0x0024, 0x1ec: 0x0024, 0x1ed: 0x0024, 0x1ee: 0x0024, 0x1ef: 0x0024,
	0x1f0: 0x0113, 0x1f1: 0x0112, 0x1f2: 0x0113, 0x1f3: 0x0112, 0x1f4: 0x0014, 0x1f5: 0x0004,
	0x1f6: 0x0113, 0x1f7: 0x0112, 0x1fa: 0x0015, 0x1fb: 0x4d52,
	0x1fc: 0x5052, 0x1fd: 0x5052, 0x1ff: 0x5353,
	// Block 0x8, offset 0x200
	0x204: 0x0004, 0x205: 0x0004,
	0x206: 0x2a13, 0x207: 0x0054, 0x208: 0x2513, 0x209: 0x2713, 0x20a: 0x2513,
	0x20c: 0x5653, 0x20e: 0x5953, 0x20f: 0x5c53, 0x210: 0x130a, 0x211: 0x2013,
	0x212: 0x2013, 0x213: 0x2013, 0x214: 0x2013, 0x215: 0x2013, 0x216: 0x2013, 0x217: 0x2013,
	0x218: 0x2013, 0x219: 0x2013, 0x21a: 0x2013, 0x21b: 0x2013, 0x21c: 0x2013, 0x21d: 0x2013,
	0x21e: 0x2013, 0x21f: 0x2013, 0x220: 0x5f53, 0x221: 0x5f53, 0x223: 0x5f53,
	0x224: 0x5f53, 0x225: 0x5f53, 0x226: 0x5f53, 0x227: 0x5f53, 0x228: 0x5f53, 0x229: 0x5f53,
	0x22a: 0x5f53, 0x22b: 0x5f53, 0x22c: 0x2a12, 0x22d: 0x2512, 0x22e: 0x2712, 0x22f: 0x2512,
	0x230: 0x144a, 0x231: 0x2012, 0x232: 0x2012, 0x233: 0x2012, 0x234: 0x2012, 0x235: 0x2012,
	0x236: 0x2012, 0x237: 0x2012, 0x238: 0x2012, 0x239: 0x2012, 0x23a: 0x2012, 0x23b: 0x2012,
	0x23c: 0x2012, 0x23d: 0x2012, 0x23e: 0x2012, 0x23f: 0x2012,
	// Block 0x9, offset 0x240
	0x240: 0x5f52, 0x241: 0x5f52, 0x242: 0x158a, 0x243: 0x5f52, 0x244: 0x5f52, 0x245: 0x5f52,
	0x246: 0x5f52, 0x247: 0x5f52, 0x248: 0x5f52, 0x249: 0x5f52, 0x24a: 0x5f52, 0x24b: 0x5f52,
	0x24c: 0x5652, 0x24d: 0x5952, 0x24e: 0x5c52, 0x24f: 0x1813, 0x250: 0x160a, 0x251: 0x168a,
	0x252: 0x0013, 0x253: 0x0013, 0x254: 0x0013, 0x255: 0x170a, 0x256: 0x178a, 0x257: 0x1812,
	0x258: 0x0113, 0x259: 0x0112, 0x25a: 0x0113, 0x25b: 0x0112, 0x25c: 0x0113, 0x25d: 0x0112,
	0x25e: 0x0113, 0x25f: 0x0112, 0x260: 0x0113, 0x261: 0x0112, 0x262: 0x0113, 0x263: 0x0112,
	0x264: 0x0113, 0x265: 0x0112, 0x266: 0x0113, 0x267: 0x0112, 0x268: 0x0113, 0x269: 0x0112,
	0x26a: 0x0113, 0x26b: 0x0112, 0x26c: 0x0113, 0x26d: 0x0112, 0x26e: 0x0113, 0x26f: 0x0112,
	0x270: 0x180a, 0x271: 0x188a, 0x272: 0x0b12, 0x273: 0x5352, 0x274: 0x6253, 0x275: 0x190a,
	0x277: 0x0f13, 0x278: 0x0f12, 0x279: 0x0b13, 0x27a: 0x0113, 0x27b: 0x0112,
	0x27c: 0x0012, 0x27d: 0x4d53, 0x27e: 0x5053, 0x27f: 0x5053,
	// Block 0xa, offset 0x280
	0x280: 0x0812, 0x281: 0x0812, 0x282: 0x0812, 0x283: 0x0812, 0x284: 0x0812, 0x285: 0x0812,
	0x288: 0x0813, 0x289: 0x0813, 0x28a: 0x0813, 0x28b: 0x0813,
	0x28c: 0x0813, 0x28d: 0x0813, 0x290: 0x239a, 0x291: 0x0812,
	0x292: 0x247a, 0x293: 0x0812, 0x294: 0x25ba, 0x295: 0x0812, 0x296: 0x26fa, 0x297: 0x0812,
	0x299: 0x0813, 0x29b: 0x0813, 0x29d: 0x0813,
	0x29f: 0x0813, 0x2a0: 0x0812, 0x2a1: 0x0812, 0x2a2: 0x0812, 0x2a3: 0x0812,
	0x2a4: 0x0812, 0x2a5: 0x0812, 0x2a6: 0x0812, 0x2a7: 0x0812, 0x2a8: 0x0813, 0x2a9: 0x0813,
	0x2aa: 0x0813, 0x2ab: 0x0813, 0x2ac: 0x0813, 0x2ad: 0x0813, 0x2ae: 0x0813, 0x2af: 0x0813,
	0x2b0: 0x8b52, 0x2b1: 0x8b52, 0x2b2: 0x8e52, 0x2b3: 0x8e52, 0x2b4: 0x9152, 0x2b5: 0x9152,
	0x2b6: 0x9452, 0x2b7: 0x9452, 0x2b8: 0x9752, 0x2b9: 0x9752, 0x2ba: 0x9a52, 0x2bb: 0x9a52,
	0x2bc: 0x4d52, 0x2bd: 0x4d52,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x283a, 0x2c1: 0x292a, 0x2c2: 0x2a1a, 0x2c3: 0x2b0a, 0x2c4: 0x2bfa, 0x2c5: 0x2cea,
	0x2c6: 0x2dda, 0x2c7: 0x2eca, 0x2c8: 0x2fb9, 0x2c9: 0x30a9, 0x2ca: 0x3199, 0x2cb: 0x3289,
	0x2cc: 0x3379, 0x2cd: 0x3469, 0x2ce: 0x3559, 0x2cf: 0x3649, 0x2d0: 0x373a, 0x2d1: 0x382a,
	0x2d2: 0x391a, 0x2d3: 0x3a0a, 0x2d4: 0x3afa, 0x2d5: 0x3bea, 0x2d6: 0x3cda, 0x2d7: 0x3dca,
	0x2d8: 0x3eb9, 0x2d9: 0x3fa9, 0x2da: 0x4099, 0x2db: 0x4189, 0x2dc: 0x4279, 0x2dd: 0x4369,
	0x2de: 0x4459, 0x2df: 0x4549, 0x2e0: 0x463a, 0x2e1: 0x472a, 0x2e2: 0x481a, 0x2e3: 0x490a,
	0x2e4: 0x49fa, 0x2e5: 0x4aea, 0x2e6: 0x4bda, 0x2e7: 0x4cca, 0x2e8: 0x4db9, 0x2e9: 0x4ea9,
	0x2ea: 0x4f99, 0x2eb: 0x5089, 0x2ec: 0x5179, 0x2ed: 0x5269, 0x2ee: 0x5359, 0x2ef: 0x5449,
	0x2f0: 0x0812, 0x2f1: 0x0812, 0x2f2: 0x553a, 0x2f3: 0x564a, 0x2f4: 0x571a,
	0x2f6: 0x57fa, 0x2f7: 0x58da, 0x2f8: 0x0813, 0x2f9: 0x0813, 0x2fa: 0x8b53, 0x2fb: 0x8b53,
	0x2fc: 0x5a19, 0x2fd: 0x0004, 0x2fe: 0x5aea, 0x2ff: 0x0004,
	// Block 0xc, offset 0x300
	0x300: 0x0004, 0x301: 0x0004, 0x302: 0x5b6a, 0x303: 0x5c7a, 0x304: 0x5d4a,
	0x306: 0x5e2a, 0x307: 0x5f0a, 0x308: 0x8e53, 0x309: 0x8e53, 0x30a: 0x9153, 0x30b: 0x9153,
	0x30c: 0x6049, 0x30d: 0x0004, 0x30e: 0x0004, 0x30f: 0x0004, 0x310: 0x0812, 0x311: 0x0812,
	0x312: 0x611a, 0x313: 0x625a, 0x316: 0x639a, 0x317: 0x647a,
	0x318: 0x0813, 0x319: 0x0813, 0x31a: 0x9453, 0x31b: 0x9453, 0x31d: 0x0004,
	0x31e: 0x0004, 0x31f: 0x0004, 0x320: 0x0812, 0x321: 0x0812, 0x322: 0x65ba, 0x323: 0x66fa,
	0x324: 0x683a, 0x325: 0x0912, 0x326: 0x691a, 0x327: 0x69fa, 0x328: 0x0813, 0x329: 0x0813,
	0x32a: 0x9a53, 0x32b: 0x9a53, 0x32c: 0x0913, 0x32d: 0x0004, 0x32e: 0x0004, 0x32f: 0x0004,
	0x332: 0x6b3a, 0x333: 0x6c4a, 0x334: 0x6d1a,
	0x336: 0x6dfa, 0x337: 0x6eda, 0x338: 0x9753, 0x339: 0x9753, 0x33a: 0x4d53, 0x33b: 0x4d53,
	0x33c: 0x7019, 0x33d: 0x0004, 0x33e: 0x0004,
	// Block 0xd, offset 0x340
	0x342: 0x0013,
	0x347: 0x0013, 0x34a: 0x0012, 0x34b: 0x0013,
	0x34c: 0x0013, 0x34d: 0x0013, 0x34e: 0x0012, 0x34f: 0x0012, 0x350: 0x0013, 0x351: 0x0013,
	0x352: 0x0013, 0x353: 0x0012, 0x355: 0x0013,
	0x359: 0x0013, 0x35a: 0x0013, 0x35b: 0x0013, 0x35c: 0x0013, 0x35d: 0x0013,
	0x364: 0x0013, 0x366: 0x70eb, 0x368: 0x0013,
	0x36a: 0x714b, 0x36b: 0x718b, 0x36c: 0x0013, 0x36d: 0x0013, 0x36f: 0x0012,
	0x370: 0x0013, 0x371: 0x0013, 0x372: 0x9d53, 0x373: 0x0013, 0x374: 0x0012, 0x375: 0x0010,
	0x376: 0x0010, 0x377: 0x0010, 0x378: 0x0010, 0x379: 0x0012,
	0x37c: 0x0012, 0x37d: 0x0012, 0x37e: 0x0013, 0x37f: 0x0013,
	// Block 0xe, offset 0x380
	0x380: 0x1a13, 0x381: 0x1a13, 0x382: 0x1e13, 0x383: 0x1e13, 0x384: 0x1a13, 0x385: 0x1a13,
	0x386: 0x2613, 0x387: 0x2613, 0x388: 0x2a13, 0x389: 0x2a13, 0x38a: 0x2e13, 0x38b: 0x2e13,
	0x38c: 0x2a13, 0x38d: 0x2a13, 0x38e: 0x2613, 0x38f: 0x2613, 0x390: 0xa052, 0x391: 0xa052,
	0x392: 0xa352, 0x393: 0xa352, 0x394: 0xa652, 0x395: 0xa652, 0x396: 0xa352, 0x397: 0xa352,
	0x398: 0xa052, 0x399: 0xa052, 0x39a: 0x1a12, 0x39b: 0x1a12, 0x39c: 0x1e12, 0x39d: 0x1e12,
	0x39e: 0x1a12, 0x39f: 0x1a12, 0x3a0: 0x2612, 0x3a1: 0x2612, 0x3a2: 0x2a12, 0x3a3: 0x2a12,
	0x3a4: 0x2e12, 0x3a5: 0x2e12, 0x3a6: 0x2a12, 0x3a7: 0x2a12, 0x3a8: 0x2612, 0x3a9: 0x2612,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x6552, 0x3c1: 0x6552, 0x3c2: 0x6552, 0x3c3: 0x6552, 0x3c4: 0x6552, 0x3c5: 0x6552,
	0x3c6: 0x6552, 0x3c7: 0x6552, 0x3c8: 0x6552, 0x3c9: 0x6552, 0x3ca: 0x6552, 0x3cb: 0x6552,
	0x3cc: 0x6552, 0x3cd: 0x6552, 0x3ce: 0x6552, 0x3cf: 0x6552, 0x3d0: 0xa952, 0x3d1: 0xa952,
	0x3d2: 0xa952, 0x3d3: 0xa952, 0x3d4: 0xa952, 0x3d5: 0xa952, 0x3d6: 0xa952, 0x3d7: 0xa952,
	0x3d8: 0xa952, 0x3d9: 0xa952, 0x3da: 0xa952, 0x3db: 0xa952, 0x3dc: 0xa952, 0x3dd: 0xa952,
	0x3de: 0xa952, 0x3e0: 0x0113, 0x3e1: 0x0112, 0x3e2: 0x71eb, 0x3e3: 0x8853,
	0x3e4: 0x724b, 0x3e5: 0x72aa, 0x3e6: 0x730a, 0x3e7: 0x0f13, 0x3e8: 0x0f12, 0x3e9: 0x0313,
	0x3ea: 0x0312, 0x3eb: 0x0713, 0x3ec: 0x0712, 0x3ed: 0x736b, 0x3ee: 0x73cb, 0x3ef: 0x742b,
	0x3f0: 0x748b, 0x3f1: 0x0012, 0x3f2: 0x0113, 0x3f3: 0x0112, 0x3f4: 0x0012, 0x3f5: 0x0313,
	0x3f6: 0x0312, 0x3f7: 0x0012, 0x3f8: 0x0012, 0x3f9: 0x0012, 0x3fa: 0x0012, 0x3fb: 0x0012,
	0x3fc: 0x0015, 0x3fd: 0x0015, 0x3fe: 0x74eb, 0x3ff: 0x754b,
	// Block 0x10, offset 0x400
	0x400: 0x0113, 0x401: 0x0112, 0x402: 0x0113, 0x403: 0x0112, 0x404: 0x0113, 0x405: 0x0112,
	0x406: 0x0113, 0x407: 0x0112, 0x408: 0x0014, 0x409: 0x0014, 0x40a: 0x0014, 0x40b: 0x0713,
	0x40c: 0x0712, 0x40d: 0x75ab, 0x40e: 0x0012, 0x40f: 0x0010, 0x410: 0x0113, 0x411: 0x0112,
	0x412: 0x0113, 0x413: 0x0112, 0x414: 0x0012, 0x415: 0x0012, 0x416: 0x0113, 0x417: 0x0112,
	0x418: 0x0113, 0x419: 0x0112, 0x41a: 0x0113, 0x41b: 0x0112, 0x41c: 0x0113, 0x41d: 0x0112,
	0x41e: 0x0113, 0x41f: 0x0112, 0x420: 0x0113, 0x421: 0x0112, 0x422: 0x0113, 0x423: 0x0112,
	0x424: 0x0113, 0x425: 0x0112, 0x426: 0x0113, 0x427: 0x0112, 0x428: 0x0113, 0x429: 0x0112,
	0x42a: 0x760b, 0x42b: 0x766b, 0x42c: 0x76cb, 0x42d: 0x772b, 0x42e: 0x778b,
	0x430: 0x77eb, 0x431: 0x784b, 0x432: 0x78ab, 0x433: 0xac53, 0x434: 0x0113, 0x435: 0x0112,
	0x436: 0x0113, 0x437: 0x0112,
	// Block 0x11, offset 0x440
	0x440: 0x790a, 0x441: 0x798a, 0x442: 0x7a0a, 0x443: 0x7a8a, 0x444: 0x7b3a, 0x445: 0x7bea,
	0x446: 0x7c6a,
	0x453: 0x7cea, 0x454: 0x7dca, 0x455: 0x7eaa, 0x456: 0x7f8a, 0x457: 0x806a,
	0x45d: 0x0010,
	0x45e: 0x0034, 0x45f: 0x0010, 0x460: 0x0010, 0x461: 0x0010, 0x462: 0x0010, 0x463: 0x0010,
	0x464: 0x0010, 0x465: 0x0010, 0x466: 0x0010, 0x467: 0x0010, 0x468: 0x0010,
	0x46a: 0x0010, 0x46b: 0x0010, 0x46c: 0x0010, 0x46d: 0x0010, 0x46e: 0x0010, 0x46f: 0x0010,
	0x470: 0x0010, 0x471: 0x0010, 0x472: 0x0010, 0x473: 0x0010, 0x474: 0x0010, 0x475: 0x0010,
	0x476: 0x0010, 0x478: 0x0010, 0x479: 0x0010, 0x47a: 0x0010, 0x47b: 0x0010,
	0x47c: 0x0010, 0x47e: 0x0010,
	// Block 0x12, offset 0x480
	0x480: 0x2213, 0x481: 0x2213, 0x482: 0x2613, 0x483: 0x2613, 0x484: 0x2213, 0x485: 0x2213,
	0x486: 0x2e13, 0x487: 0x2e13, 0x488: 0x2213, 0x489: 0x2213, 0x48a: 0x2613, 0x48b: 0x2613,
	0x48c: 0x2213, 0x48d: 0x2213, 0x48e: 0x3e13, 0x48f: 0x3e13, 0x490: 0x2213, 0x491: 0x2213,
	0x492: 0x2613, 0x493: 0x2613, 0x494: 0x2213, 0x495: 0x2213, 0x496: 0x2e13, 0x497: 0x2e13,
	0x498: 0x2213, 0x499: 0x2213, 0x49a: 0x2613, 0x49b: 0x2613, 0x49c: 0x2213, 0x49d: 0x2213,
	0x49e: 0xb553, 0x49f: 0xb553, 0x4a0: 0xb853, 0x4a1: 0xb853, 0x4a2: 0x2212, 0x4a3: 0x2212,
	0x4a4: 0x2612, 0x4a5: 0x2612, 0x4a6: 0x2212, 0x4a7: 0x2212, 0x4a8: 0x2e12, 0x4a9: 0x2e12,
	0x4aa: 0x2212, 0x4ab: 0x2212, 0x4ac: 0x2612, 0x4ad: 0x2612, 0x4ae: 0x2212, 0x4af: 0x2212,
	0x4b0: 0x3e12, 0x4b1: 0x3e12, 0x4b2: 0x2212, 0x4b3: 0x2212, 0x4b4: 0x2612, 0x4b5: 0x2612,
	0x4b6: 0x2212, 0x4b7: 0x2212, 0x4b8: 0x2e12, 0x4b9: 0x2e12, 0x4ba: 0x2212, 0x4bb: 0x2212,
	0x4bc: 0x2612, 0x4bd: 0x2612, 0x4be: 0x2212, 0x4bf: 0x2212,
	// Block 0x13, offset 0x4c0
	0x4c2: 0x0010,
	0x4c7: 0x0010, 0x4c9: 0x0010, 0x4cb: 0x0010,
	0x4cd: 0x0010, 0x4ce: 0x0010, 0x4cf: 0x0010, 0x4d1: 0x0010,
	0x4d2: 0x0010, 0x4d4: 0x0010, 0x4d7: 0x0010,
	0x4d9: 0x0010, 0x4db: 0x0010, 0x4dd: 0x0010,
	0x4df: 0x0010, 0x4e1: 0x0010, 0x4e2: 0x0010,
	0x4e4: 0x0010, 0x4e7: 0x0010, 0x4e8: 0x0010, 0x4e9: 0x0010,
	0x4ea: 0x0010, 0x4ec: 0x0010, 0x4ed: 0x0010, 0x4ee: 0x0010, 0x4ef: 0x0010,
	0x4f0: 0x0010, 0x4f1: 0x0010, 0x4f2: 0x0010, 0x4f4: 0x0010, 0x4f5: 0x0010,
	0x4f6: 0x0010, 0x4f7: 0x0010, 0x4f9: 0x0010, 0x4fa: 0x0010, 0x4fb: 0x0010,
	0x4fc: 0x0010, 0x4fe: 0x0010,
}

// caseIndex: 25 blocks, 1600 entries, 3200 bytes
// Block 0 is the zero block.
var caseIndex = [1600]uint16{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x12, 0xc3: 0x13, 0xc4: 0x14, 0xc5: 0x15, 0xc6: 0x01, 0xc7: 0x02,
	0xc8: 0x16, 0xc9: 0x03, 0xca: 0x04, 0xcb: 0x17, 0xcc: 0x18, 0xcd: 0x05, 0xce: 0x06, 0xcf: 0x07,
	0xd0: 0x19, 0xd1: 0x1a, 0xd2: 0x1b, 0xd3: 0x1c, 0xd4: 0x1d, 0xd5: 0x1e, 0xd6: 0x1f, 0xd7: 0x20,
	0xd8: 0x21, 0xd9: 0x22, 0xda: 0x23, 0xdb: 0x24, 0xdc: 0x25, 0xdd: 0x26, 0xde: 0x27, 0xdf: 0x28,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05,
	0xea: 0x06, 0xeb: 0x07, 0xec: 0x07, 0xed: 0x08, 0xef: 0x09,
	0xf0: 0x14, 0xf3: 0x16,
	// Block 0x4, offset 0x100
	0x120: 0x29, 0x121: 0x2a, 0x122: 0x2b, 0x123: 0x2c, 0x124: 0x2d, 0x125: 0x2e, 0x126: 0x2f, 0x127: 0x30,
	0x128: 0x31, 0x129: 0x32, 0x12a: 0x33, 0x12b: 0x34, 0x12c: 0x35, 0x12d: 0x36, 0x12e: 0x37, 0x12f: 0x38,
	0x130: 0x39, 0x131: 0x3a, 0x132: 0x3b, 0x133: 0x3c, 0x134: 0x3d, 0x135: 0x3e, 0x136: 0x3f, 0x137: 0x40,
	0x138: 0x41, 0x139: 0x42, 0x13a: 0x43, 0x13b: 0x44, 0x13c: 0x45, 0x13d: 0x46, 0x13e: 0x47, 0x13f: 0x48,
	// Block 0x5, offset 0x140
	0x140: 0x49, 0x141: 0x4a, 0x142: 0x4b, 0x143: 0x4c, 0x144: 0x23, 0x145: 0x23, 0x146: 0x23, 0x147: 0x23,
	0x148: 0x23, 0x149: 0x4d, 0x14a: 0x4e, 0x14b: 0x4f, 0x14c: 0x50, 0x14d: 0x51, 0x14e: 0x52, 0x14f: 0x53,
	0x150: 0x54, 0x151: 0x23, 0x152: 0x23, 0x153: 0x23, 0x154: 0x23, 0x155: 0x23, 0x156: 0x23, 0x157: 0x23,
	0x158: 0x23, 0x159: 0x55, 0x15a: 0x56, 0x15b: 0x57, 0x15c: 0x58, 0x15d: 0x59, 0x15e: 0x5a, 0x15f: 0x5b,
	0x160: 0x5c, 0x161: 0x5d, 0x162: 0x5e, 0x163: 0x5f, 0x164: 0x60, 0x165: 0x61, 0x167: 0x62,
	0x168: 0x63, 0x169: 0x64, 0x16a: 0x65, 0x16c: 0x66, 0x16d: 0x67, 0x16e: 0x68, 0x16f: 0x69,
	0x170: 0x6a, 0x171: 0x6b, 0x172: 0x6c, 0x173: 0x6d, 0x174: 0x6e, 0x175: 0x6f, 0x176: 0x70, 0x177: 0x71,
	0x178: 0x72, 0x179: 0x72, 0x17a: 0x73, 0x17b: 0x72, 0x17c: 0x74, 0x17d: 0x08, 0x17e: 0x09, 0x17f: 0x0a,
	// Block 0x6, offset 0x180
	0x180: 0x75, 0x181: 0x76, 0x182: 0x77, 0x183: 0x78, 0x184: 0x0b, 0x185: 0x79, 0x186: 0x7a,
	0x192: 0x7b, 0x193: 0x0c,
	0x1b0: 0x7c, 0x1b1: 0x0d, 0x1b2: 0x72, 0x1b3: 0x7d, 0x1b4: 0x7e, 0x1b5: 0x7f, 0x1b6: 0x80, 0x1b7: 0x81,
	0x1b8: 0x82,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x83, 0x1c2: 0x84, 0x1c3: 0x85, 0x1c4: 0x86, 0x1c5: 0x23, 0x1c6: 0x87,
	// Block 0x8, offset 0x200
	0x200: 0x88, 0x201: 0x23, 0x202: 0x23, 0x203: 0x23, 0x204: 0x23, 0x205: 0x23, 0x206: 0x23, 0x207: 0x23,
	0x208: 0x23, 0x209: 0x23, 0x20a: 0x23, 0x20b: 0x23, 0x20c: 0x23, 0x20d: 0x23, 0x20e: 0x23, 0x20f: 0x23,
	0x210: 0x23, 0x211: 0x23, 0x212: 0x89, 0x213: 0x8a, 0x214: 0x23, 0x215: 0x23, 0x216: 0x23, 0x217: 0x23,
	0x218: 0x8b, 0x219: 0x8c, 0x21a: 0x8d, 0x21b: 0x8e, 0x21c: 0x8f, 0x21d: 0x90, 0x21e: 0x0e, 0x21f: 0x91,
	0x220: 0x92, 0x221: 0x93, 0x222: 0x23, 0x223: 0x94, 0x224: 0x95, 0x225: 0x96, 0x226: 0x97, 0x227: 0x98,
	0x228: 0x99, 0x229: 0x9a, 0x22a: 0x9b, 0x22b: 0x9c, 0x22c: 0x9d, 0x22d: 0x9e, 0x22e: 0x9f, 0x22f: 0xa0,
	0x230: 0x23, 0x231: 0x23, 0x232: 0x23, 0x233: 0x23, 0x234: 0x23, 0x235: 0x23, 0x236: 0x23, 0x237: 0x23,
	0x238: 0x23, 0x239: 0x23, 0x23a: 0x23, 0x23b: 0x23, 0x23c: 0x23, 0x23d: 0x23, 0x23e: 0x23, 0x23f: 0x23,
	// Block 0x9, offset 0x240
	0x240: 0x23, 0x241: 0x23, 0x242: 0x23, 0x243: 0x23, 0x244: 0x23, 0x245: 0x23, 0x246: 0x23, 0x247: 0x23,
	0x248: 0x23, 0x249: 0x23, 0x24a: 0x23, 0x24b: 0x23, 0x24c: 0x23, 0x24d: 0x23, 0x24e: 0x23, 0x24f: 0x23,
	0x250: 0x23, 0x251: 0x23, 0x252: 0x23, 0x253: 0x23, 0x254: 0x23, 0x255: 0x23, 0x256: 0x23, 0x257: 0x23,
	0x258: 0x23, 0x259: 0x23, 0x25a: 0x23, 0x25b: 0x23, 0x25c: 0x23, 0x25d: 0x23, 0x25e: 0x23, 0x25f: 0x23,
	0x260: 0x23, 0x261: 0x23, 0x262: 0x23, 0x263: 0x23, 0x264: 0x23, 0x265: 0x23, 0x266: 0x23, 0x267: 0x23,
	0x268: 0x23, 0x269: 0x23, 0x26a: 0x23, 0x26b: 0x23, 0x26c: 0x23, 0x26d: 0x23, 0x26e: 0x23, 0x26f: 0x23,
	0x270: 0x23, 0x271: 0x23, 0x272: 0x23, 0x273: 0x23, 0x274: 0x23, 0x275: 0x23, 0x276: 0x23, 0x277: 0x23,
	0x278: 0x23, 0x279: 0x23, 0x27a: 0x23, 0x27b: 0x23, 0x27c: 0x23, 0x27d: 0x23, 0x27e: 0x23, 0x27f: 0x23,
	// Block 0xa, offset 0x280
	0x280: 0x23, 0x281: 0x23, 0x282: 0x23, 0x283: 0x23, 0x284: 0x23, 0x285: 0x23, 0x286: 0x23, 0x287: 0x23,
	0x288: 0x23, 0x289: 0x23, 0x28a: 0x23, 0x28b: 0x23, 0x28c: 0x23, 0x28d: 0x23, 0x28e: 0x23, 0x28f: 0x23,
	0x290: 0x23, 0x291: 0x23, 0x292: 0x23, 0x293: 0x23, 0x294: 0x23, 0x295: 0x23, 0x296: 0x23, 0x297: 0x23,
	0x298: 0x23, 0x299: 0x23, 0x29a: 0x23, 0x29b: 0x23, 0x29c: 0x23, 0x29d: 0x23, 0x29e: 0xa1, 0x29f: 0xa2,
	// Block 0xb, offset 0x2c0
	0x2ec: 0x0f, 0x2ed: 0xa3, 0x2ee: 0xa4, 0x2ef: 0xa5,
	0x2f0: 0x23, 0x2f1: 0x23, 0x2f2: 0x23, 0x2f3: 0x23, 0x2f4: 0xa6, 0x2f5: 0xa7, 0x2f6: 0xa8, 0x2f7: 0xa9,
	0x2f8: 0xaa, 0x2f9: 0xab, 0x2fa: 0x23, 0x2fb: 0xac, 0x2fc: 0xad, 0x2fd: 0xae, 0x2fe: 0xaf, 0x2ff: 0xb0,
	// Block 0xc, offset 0x300
	0x300: 0xb1, 0x301: 0xb2, 0x302: 0x23, 0x303: 0xb3, 0x305: 0xb4, 0x307: 0xb5,
	0x30a: 0xb6, 0x30b: 0xb7, 0x30c: 0xb8, 0x30d: 0xb9, 0x30e: 0xba, 0x30f: 0xbb,
	0x310: 0xbc, 0x311: 0xbd, 0x312: 0xbe, 0x313: 0xbf, 0x314: 0xc0, 0x315: 0xc1,
	0x318: 0x23, 0x319: 0x23, 0x31a: 0x23, 0x31b: 0x23, 0x31c: 0xc2, 0x31d: 0xc3,
	0x320: 0xc4, 0x321: 0xc5, 0x322: 0xc6, 0x323: 0xc7, 0x324: 0xc8, 0x326: 0xc9,
	0x328: 0xca, 0x329: 0xcb, 0x32a: 0xcc, 0x32b: 0xcd, 0x32c: 0x5f, 0x32d: 0xce, 0x32e: 0xcf,
	0x330: 0x23, 0x331: 0xd0, 0x332: 0xd1, 0x333: 0xd2,
	// Block 0xd, offset 0x340
	0x340: 0xd3, 0x341: 0xd4, 0x342: 0xd5, 0x343: 0xd6, 0x344: 0xd7, 0x345: 0xd8, 0x346: 0xd9, 0x347: 0xda,
	0x348: 0xdb, 0x34a: 0xdc, 0x34b: 0xdd, 0x34c: 0xde, 0x34d: 0xdf,
	0x350: 0xe0, 0x351: 0xe1, 0x352: 0xe2, 0x353: 0xe3, 0x356: 0xe4, 0x357: 0xe5,
	0x358: 0xe6, 0x359: 0xe7, 0x35a: 0xe8, 0x35b: 0xe9, 0x35c: 0xea,
	0x362: 0xeb, 0x363: 0xec,
	0x368: 0xed, 0x369: 0xee, 0x36a: 0xef, 0x36b: 0xf0,
	0x370: 0xf1, 0x371: 0xf2, 0x372: 0xf3, 0x374: 0xf4, 0x375: 0xf5,
	// Block 0xe, offset 0x380
	0x380: 0x23, 0x381: 0x23, 0x382: 0x23, 0x383: 0x23, 0x384: 0x23, 0x385: 0x23, 0x386: 0x23, 0x387: 0x23,
	0x388: 0x23, 0x389: 0x23, 0x38a: 0x23, 0x38b: 0x23, 0x38c: 0x23, 0x38d: 0x23, 0x38e: 0xf6,
	0x390: 0x23, 0x391: 0xf7, 0x392: 0x23, 0x393: 0x23, 0x394: 0x23, 0x395: 0xf8,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x23, 0x3c1: 0x23, 0x3c2: 0x23, 0x3c3: 0x23, 0x3c4: 0x23, 0x3c5: 0x23, 0x3c6: 0x23, 0x3c7: 0x23,
	0x3c8: 0x23, 0x3c9: 0x23, 0x3ca: 0x23, 0x3cb: 0x23, 0x3cc: 0x23, 0x3cd: 0x23, 0x3ce: 0x23, 0x3cf: 0x23,
	0x3d0: 0xf7,
	// Block 0x10, offset 0x400
	0x410: 0x23, 0x411: 0x23, 0x412: 0x23, 0x413: 0x23, 0x414: 0x23, 0x415: 0x23, 0x416: 0x23, 0x417: 0x23,
	0x418: 0x23, 0x419: 0xf9,
	// Block 0x11, offset 0x440
	0x460: 0x23, 0x461: 0x23, 0x462: 0x23, 0x463: 0x23, 0x464: 0x23, 0x465: 0x23, 0x466: 0x23, 0x467: 0x23,
	0x468: 0xf0, 0x469: 0xfa, 0x46b: 0xfb, 0x46c: 0xfc, 0x46d: 0xfd, 0x46e: 0xfe,
	0x47c: 0x23, 0x47d: 0xff, 0x47e: 0x100, 0x47f: 0x101,
	// Block 0x12, offset 0x480
	0x4b0: 0x23, 0x4b1: 0x102, 0x4b2: 0x103,
	// Block 0x13, offset 0x4c0
	0x4c5: 0x104, 0x4c6: 0x105,
	0x4c9: 0x106,
	0x4d0: 0x107, 0x4d1: 0x108, 0x4d2: 0x109, 0x4d3: 0x10a, 0x4d4: 0x10b, 0x4d5: 0x10c, 0x4d6: 0x10d, 0x4d7: 0x10e,
	0x4d8: 0x10f, 0x4d9: 0x110, 0x4da: 0x111, 0x4db: 0x112, 0x4dc: 0x113, 0x4dd: 0x114, 0x4de: 0x115, 0x4df: 0x116,
	0x4e8: 0x117, 0x4e9: 0x118, 0x4ea: 0x119,
	// Block 0x14, offset 0x500
	0x500: 0x11a,
	0x520: 0x23, 0x521: 0x23, 0x522: 0x23, 0x523: 0x11b, 0x524: 0x10, 0x525: 0x11c,
	0x538: 0x11d, 0x539: 0x11, 0x53a: 0x11e,
	// Block 0x15, offset 0x540
	0x544: 0x11f, 0x545: 0x120, 0x546: 0x121,
	0x54f: 0x122,
	// Block 0x16, offset 0x580
	0x590: 0x0a, 0x591: 0x0b, 0x592: 0x0c, 0x593: 0x0d, 0x594: 0x0e, 0x596: 0x0f,
	0x59b: 0x10, 0x59d: 0x11, 0x59e: 0x12, 0x59f: 0x13,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x123, 0x5c1: 0x124, 0x5c4: 0x124, 0x5c5: 0x124, 0x5c6: 0x124, 0x5c7: 0x125,
	// Block 0x18, offset 0x600
	0x620: 0x15,
}

// sparseOffsets: 277 entries, 554 bytes
var sparseOffsets = []uint16{0x0, 0x9, 0xf, 0x18, 0x24, 0x2e, 0x35, 0x38, 0x3c, 0x3f, 0x43, 0x4d, 0x4f, 0x54, 0x64, 0x6b, 0x70, 0x7e, 0x7f, 0x8d, 0x9c, 0xa6, 0xa9, 0xaf, 0xb7, 0xba, 0xbc, 0xca, 0xd0, 0xde, 0xe9, 0xf5, 0x100, 0x10c, 0x116, 0x122, 0x12d, 0x139, 0x145, 0x14d, 0x155, 0x15f, 0x16a, 0x176, 0x17d, 0x188, 0x18d, 0x195, 0x198, 0x19d, 0x1a1, 0x1a5, 0x1ac, 0x1b5, 0x1bd, 0x1be, 0x1c7, 0x1ce, 0x1d6, 0x1dc, 0x1e2, 0x1e7, 0x1eb, 0x1ee, 0x1f0, 0x1f3, 0x1f8, 0x1f9, 0x1fb, 0x1fd, 0x1ff, 0x206, 0x20b, 0x20f, 0x218, 0x21b, 0x21e, 0x224, 0x225, 0x230, 0x231, 0x232, 0x237, 0x244, 0x24c, 0x254, 0x25d, 0x266, 0x26f, 0x274, 0x277, 0x280, 0x28d, 0x28f, 0x296, 0x298, 0x2a4, 0x2a5, 0x2b0, 0x2b8, 0x2c0, 0x2c6, 0x2c7, 0x2d5, 0x2da, 0x2dd, 0x2e2, 0x2e6, 0x2ec, 0x2f1, 0x2f4, 0x2f9, 0x2fe, 0x2ff, 0x305, 0x307, 0x308, 0x30a, 0x30c, 0x30f, 0x310, 0x312, 0x315, 0x31b, 0x31f, 0x321, 0x326, 0x32d, 0x331, 0x33a, 0x33b, 0x343, 0x347, 0x34c, 0x354, 0x35a, 0x360, 0x36a, 0x36f, 0x378, 0x37e, 0x385, 0x389, 0x391, 0x393, 0x395, 0x398, 0x39a, 0x39c, 0x39d, 0x39e, 0x3a0, 0x3a2, 0x3a8, 0x3ad, 0x3af, 0x3b5, 0x3b8, 0x3ba, 0x3c0, 0x3c5, 0x3c7, 0x3c8, 0x3c9, 0x3ca, 0x3cc, 0x3ce, 0x3d0, 0x3d3, 0x3d5, 0x3d8, 0x3e0, 0x3e3, 0x3e7, 0x3ef, 0x3f1, 0x3f2, 0x3f3, 0x3f5, 0x3fb, 0x3fd, 0x3fe, 0x400, 0x402, 0x404, 0x411, 0x412, 0x413, 0x417, 0x419, 0x41a, 0x41b, 0x41c, 0x41d, 0x421, 0x425, 0x42b, 0x42d, 0x434, 0x437, 0x43b, 0x441, 0x44a, 0x450, 0x456, 0x460, 0x46a, 0x46c, 0x473, 0x479, 0x47f, 0x485, 0x488, 0x48e, 0x491, 0x499, 0x49a, 0x4a1, 0x4a2, 0x4a5, 0x4af, 0x4b5, 0x4bb, 0x4bc, 0x4c2, 0x4c5, 0x4cd, 0x4d4, 0x4db, 0x4dc, 0x4dd, 0x4de, 0x4df, 0x4e1, 0x4e3, 0x4e5, 0x4e9, 0x4ea, 0x4ec, 0x4ed, 0x4ee, 0x4f0, 0x4f5, 0x4fa, 0x4fe, 0x4ff, 0x502, 0x506, 0x511, 0x515, 0x51d, 0x522, 0x526, 0x529, 0x52d, 0x530, 0x533, 0x538, 0x53c, 0x540, 0x544, 0x548, 0x54a, 0x54c, 0x54f, 0x554, 0x556, 0x55b, 0x564, 0x569, 0x56a, 0x56d, 0x56e, 0x56f, 0x571, 0x572, 0x573}

// sparseValues: 1395 entries, 5580 bytes
var sparseValues = [1395]valueRange{
	// Block 0x0, offset 0x0
	{value: 0x0004, lo: 0xa8, hi: 0xa8},
	{value: 0x0012, lo: 0xaa, hi: 0xaa},
	{value: 0x0014, lo: 0xad, hi: 0xad},
	{value: 0x0004, lo: 0xaf, hi: 0xaf},
	{value: 0x0004, lo: 0xb4, hi: 0xb4},
	{value: 0x001a, lo: 0xb5, hi: 0xb5},
	{value: 0x0054, lo: 0xb7, hi: 0xb7},
	{value: 0x0004, lo: 0xb8, hi: 0xb8},
	{value: 0x0012, lo: 0xba, hi: 0xba},
	// Block 0x1, offset 0x9
	{value: 0x2013, lo: 0x80, hi: 0x96},
	{value: 0x2013, lo: 0x98, hi: 0x9e},
	{value: 0x009a, lo: 0x9f, hi: 0x9f},
	{value: 0x2012, lo: 0xa0, hi: 0xb6},
	{value: 0x2012, lo: 0xb8, hi: 0xbe},
	{value: 0x0252, lo: 0xbf, hi: 0xbf},
	// Block 0x2, offset 0xf
	{value: 0x0117, lo: 0x80, hi: 0xaf},
	{value: 0x011b, lo: 0xb0, hi: 0xb0},
	{value: 0x019a, lo: 0xb1, hi: 0xb1},
	{value: 0x0117, lo: 0xb2, hi: 0xb7},
	{value: 0x0012, lo: 0xb8, hi: 0xb8},
	{value: 0x0316, lo: 0xb9, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x0316, lo: 0xbd, hi: 0xbe},
	{value: 0x0553, lo: 0xbf, hi: 0xbf},
	// Block 0x3, offset 0x18
	{value: 0x0552, lo: 0x80, hi: 0x80},
	{value: 0x0316, lo: 0x81, hi: 0x82},
	{value: 0x0716, lo: 0x83, hi: 0x84},
	{value: 0x0316, lo: 0x85, hi: 0x86},
	{value: 0x0f16, lo: 0x87, hi: 0x88},
	{value: 0x01da, lo: 0x89, hi: 0x89},
	{value: 0x0117, lo: 0x8a, hi: 0xb7},
	{value: 0x0253, lo: 0xb8, hi: 0xb8},
	{value: 0x0316, lo: 0xb9, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x0316, lo: 0xbd, hi: 0xbe},
	{value: 0x028a, lo: 0xbf, hi: 0xbf},
	// Block 0x4, offset 0x24
	{value: 0x0117, lo: 0x80, hi: 0x9f},
	{value: 0x2f53, lo: 0xa0, hi: 0xa0},
	{value: 0x0012, lo: 0xa1, hi: 0xa1},
	{value: 0x0117, lo: 0xa2, hi: 0xb3},
	{value: 0x0012, lo: 0xb4, hi: 0xb9},
	{value: 0x090b, lo: 0xba, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x2953, lo: 0xbd, hi: 0xbd},
	{value: 0x098b, lo: 0xbe, hi: 0xbe},
	{value: 0x0a0a, lo: 0xbf, hi: 0xbf},
	// Block 0x5, offset 0x2e
	{value: 0x0015, lo: 0x80, hi: 0x81},
	{value: 0x0014, lo: 0x82, hi: 0x97},
	{value: 0x0004, lo: 0x98, hi: 0x9d},
	{value: 0x0014, lo: 0x9e, hi: 0x9f},
	{value: 0x0015, lo: 0xa0, hi: 0xa4},
	{value: 0x0004, lo: 0xa5, hi: 0xab},
	{value: 0x0014, lo: 0xac, hi: 0xbf},
	// Block 0x6, offset 0x35
	{value: 0x0024, lo: 0x80, hi: 0x94},
	{value: 0x0034, lo: 0x95, hi: 0xbc},
	{value: 0x0024, lo: 0xbd, hi: 0xbf},
	// Block 0x7, offset 0x38
	{value: 0x6553, lo: 0x80, hi: 0x8f},
	{value: 0x2013, lo: 0x90, hi: 0x9f},
	{value: 0x5f53, lo: 0xa0, hi: 0xaf},
	{value: 0x2012, lo: 0xb0, hi: 0xbf},
	// Block 0x8, offset 0x3c
	{value: 0x5f52, lo: 0x80, hi: 0x8f},
	{value: 0x6552, lo: 0x90, hi: 0x9f},
	{value: 0x0117, lo: 0xa0, hi: 0xbf},
	// Block 0x9, offset 0x3f
	{value: 0x0117, lo: 0x80, hi: 0x81},
	{value: 0x0024, lo: 0x83, hi: 0x87},
	{value: 0x0014, lo: 0x88, hi: 0x89},
	{value: 0x0117, lo: 0x8a, hi: 0xbf},
	// Block 0xa, offset 0x43
	{value: 0x0f13, lo: 0x80, hi: 0x80},
	{value: 0x0316, lo: 0x81, hi: 0x82},
	{value: 0x0716, lo: 0x83, hi: 0x84},
	{value: 0x0316, lo: 0x85, hi: 0x86},
	{value: 0x0f16, lo: 0x87, hi: 0x88},
	{value: 0x0316, lo: 0x89, hi: 0x8a},
	{value: 0x0716, lo: 0x8b, hi: 0x8c},
	{value: 0x0316, lo: 0x8d, hi: 0x8e},
	{value: 0x0f12, lo: 0x8f, hi: 0x8f},
	{value: 0x0117, lo: 0x90, hi: 0xbf},
	// Block 0xb, offset 0x4d
	{value: 0x0117, lo: 0x80, hi: 0xaf},
	{value: 0x6553, lo: 0xb1, hi: 0xbf},
	// Block 0xc, offset 0x4f
	{value: 0x3013, lo: 0x80, hi: 0x8f},
	{value: 0x6853, lo: 0x90, hi: 0x96},
	{value: 0x0014, lo: 0x99, hi: 0x99},
	{value: 0x6552, lo: 0xa1, hi: 0xaf},
	{value: 0x3012, lo: 0xb0, hi: 0xbf},
	// Block 0xd, offset 0x54
	{value: 0x6852, lo: 0x80, hi: 0x86},
	{value: 0x198a, lo: 0x87, hi: 0x87},
	{value: 0x0034, lo: 0x91, hi: 0x91},
	{value: 0x0024, lo: 0x92, hi: 0x95},
	{value: 0x0034, lo: 0x96, hi: 0x96},
	{value: 0x0024, lo: 0x97, hi: 0x99},
	{value: 0x0034, lo: 0x9a, hi: 0x9b},
	{value: 0x0024, lo: 0x9c, hi: 0xa1},
	{value: 0x0034, lo: 0xa2, hi: 0xa7},
	{value: 0x0024, lo: 0xa8, hi: 0xa9},
	{value: 0x0034, lo: 0xaa, hi: 0xaa},
	{value: 0x0024, lo: 0xab, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xae},
	{value: 0x0024, lo: 0xaf, hi: 0xaf},
	{value: 0x0034, lo: 0xb0, hi: 0xbd},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xe, offset 0x64
	{value: 0x0034, lo: 0x81, hi: 0x82},
	{value: 0x0024, lo: 0x84, hi: 0x84},
	{value: 0x0034, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xb3},
	{value: 0x0054, lo: 0xb4, hi: 0xb4},
	// Block 0xf, offset 0x6b
	{value: 0x0014, lo: 0x80, hi: 0x85},
	{value: 0x0024, lo: 0x90, hi: 0x97},
	{value: 0x0034, lo: 0x98, hi: 0x9a},
	{value: 0x0014, lo: 0x9c, hi: 0x9c},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x10, offset 0x70
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x8a},
	{value: 0x0034, lo: 0x8b, hi: 0x92},
	{value: 0x0024, lo: 0x93, hi: 0x94},
	{value: 0x0034, lo: 0x95, hi: 0x96},
	{value: 0x0024, lo: 0x97, hi: 0x9b},
	{value: 0x0034, lo: 0x9c, hi: 0x9c},
	{value: 0x0024, lo: 0x9d, hi: 0x9e},
	{value: 0x0034, lo: 0x9f, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0x0010, lo: 0xab, hi: 0xab},
	{value: 0x0010, lo: 0xae, hi: 0xaf},
	{value: 0x0034, lo: 0xb0, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xbf},
	// Block 0x11, offset 0x7e
	{value: 0x0010, lo: 0x80, hi: 0xbf},
	// Block 0x12, offset 0x7f
	{value: 0x0010, lo: 0x80, hi: 0x93},
	{value: 0x0010, lo: 0x95, hi: 0x95},
	{value: 0x0024, lo: 0x96, hi: 0x9c},
	{value: 0x0014, lo: 0x9d, hi: 0x9d},
	{value: 0x0024, lo: 0x9f, hi: 0xa2},
	{value: 0x0034, lo: 0xa3, hi: 0xa3},
	{value: 0x0024, lo: 0xa4, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xa6},
	{value: 0x0024, lo: 0xa7, hi: 0xa8},
	{value: 0x0034, lo: 0xaa, hi: 0xaa},
	{value: 0x0024, lo: 0xab, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xbc},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x13, offset 0x8d
	{value: 0x0014, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0034, lo: 0x91, hi: 0x91},
	{value: 0x0010, lo: 0x92, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb0},
	{value: 0x0034, lo: 0xb1, hi: 0xb1},
	{value: 0x0024, lo: 0xb2, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0024, lo: 0xb5, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb9},
	{value: 0x0024, lo: 0xba, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbc},
	{value: 0x0024, lo: 0xbd, hi: 0xbd},
	{value: 0x0034, lo: 0xbe, hi: 0xbe},
	{value: 0x0024, lo: 0xbf, hi: 0xbf},
	// Block 0x14, offset 0x9c
	{value: 0x0024, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0024, lo: 0x83, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x84},
	{value: 0x0024, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0024, lo: 0x87, hi: 0x87},
	{value: 0x0034, lo: 0x88, hi: 0x88},
	{value: 0x0024, lo: 0x89, hi: 0x8a},
	{value: 0x0010, lo: 0x8d, hi: 0xbf},
	// Block 0x15, offset 0xa6
	{value: 0x0010, lo: 0x80, hi: 0xa5},
	{value: 0x0014, lo: 0xa6, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	// Block 0x16, offset 0xa9
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0024, lo: 0xab, hi: 0xb1},
	{value: 0x0034, lo: 0xb2, hi: 0xb2},
	{value: 0x0024, lo: 0xb3, hi: 0xb3},
	{value: 0x0014, lo: 0xb4, hi: 0xb5},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	// Block 0x17, offset 0xaf
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0024, lo: 0x96, hi: 0x99},
	{value: 0x0014, lo: 0x9a, hi: 0x9a},
	{value: 0x0024, lo: 0x9b, hi: 0xa3},
	{value: 0x0014, lo: 0xa4, hi: 0xa4},
	{value: 0x0024, lo: 0xa5, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa8},
	{value: 0x0024, lo: 0xa9, hi: 0xad},
	// Block 0x18, offset 0xb7
	{value: 0x0010, lo: 0x80, hi: 0x98},
	{value: 0x0034, lo: 0x99, hi: 0x9b},
	{value: 0x0010, lo: 0xa0, hi: 0xaa},
	// Block 0x19, offset 0xba
	{value: 0x0010, lo: 0xa0, hi: 0xb4},
	{value: 0x0010, lo: 0xb6, hi: 0xbd},
	// Block 0x1a, offset 0xbc
	{value: 0x0024, lo: 0x94, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa2},
	{value: 0x0034, lo: 0xa3, hi: 0xa3},
	{value: 0x0024, lo: 0xa4, hi: 0xa5},
	{value: 0x0034, lo: 0xa6, hi: 0xa6},
	{value: 0x0024, lo: 0xa7, hi: 0xa8},
	{value: 0x0034, lo: 0xa9, hi: 0xa9},
	{value: 0x0024, lo: 0xaa, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xb2},
	{value: 0x0024, lo: 0xb3, hi: 0xb5},
	{value: 0x0034, lo: 0xb6, hi: 0xb6},
	{value: 0x0024, lo: 0xb7, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	{value: 0x0024, lo: 0xbb, hi: 0xbf},
	// Block 0x1b, offset 0xca
	{value: 0x0014, lo: 0x80, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0xb9},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x1c, offset 0xd0
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x88},
	{value: 0x0010, lo: 0x89, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0024, lo: 0x91, hi: 0x91},
	{value: 0x0034, lo: 0x92, hi: 0x92},
	{value: 0x0024, lo: 0x93, hi: 0x94},
	{value: 0x0014, lo: 0x95, hi: 0x97},
	{value: 0x0010, lo: 0x98, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0014, lo: 0xb1, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xbf},
	// Block 0x1d, offset 0xde
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb2},
	{value: 0x0010, lo: 0xb6, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x1e, offset 0xe9
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8e, hi: 0x8e},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0x9c, hi: 0x9d},
	{value: 0x0010, lo: 0x9f, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xb1},
	{value: 0x0010, lo: 0xbc, hi: 0xbc},
	// Block 0x1f, offset 0xf5
	{value: 0x0014, lo: 0x81, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8a},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb6},
	{value: 0x0010, lo: 0xb8, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x20, offset 0x100
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x82},
	{value: 0x0014, lo: 0x87, hi: 0x88},
	{value: 0x0014, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0014, lo: 0x91, hi: 0x91},
	{value: 0x0010, lo: 0x99, hi: 0x9c},
	{value: 0x0010, lo: 0x9e, hi: 0x9e},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb5},
	// Block 0x21, offset 0x10c
	{value: 0x0014, lo: 0x81, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8d},
	{value: 0x0010, lo: 0x8f, hi: 0x91},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x22, offset 0x116
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x85},
	{value: 0x0014, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x89, hi: 0x89},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb9, hi: 0xb9},
	{value: 0x0014, lo: 0xba, hi: 0xbf},
	// Block 0x23, offset 0x122
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbe},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x24, offset 0x12d
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0014, lo: 0x96, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0x9c, hi: 0x9d},
	{value: 0x0010, lo: 0x9f, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	// Block 0x25, offset 0x139
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8a},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0x95},
	{value: 0x0010, lo: 0x99, hi: 0x9a},
	{value: 0x0010, lo: 0x9c, hi: 0x9c},
	{value: 0x0010, lo: 0x9e, hi: 0x9f},
	{value: 0x0010, lo: 0xa3, hi: 0xa4},
	{value: 0x0010, lo: 0xa8, hi: 0xaa},
	{value: 0x0010, lo: 0xae, hi: 0xb9},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x26, offset 0x145
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x82},
	{value: 0x0010, lo: 0x86, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	// Block 0x27, offset 0x14d
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb9},
	{value: 0x0010, lo: 0xbd, hi: 0xbd},
	{value: 0x0014, lo: 0xbe, hi: 0xbf},
	// Block 0x28, offset 0x155
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x84},
	{value: 0x0014, lo: 0x86, hi: 0x88},
	{value: 0x0014, lo: 0x8a, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0034, lo: 0x95, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x9a},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	// Block 0x29, offset 0x15f
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbe},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x2a, offset 0x16a
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0014, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x95, hi: 0x96},
	{value: 0x0010, lo: 0x9e, hi: 0x9e},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb1, hi: 0xb2},
	// Block 0x2b, offset 0x176
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x2c, offset 0x17d
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x86, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8e, hi: 0x8e},
	{value: 0x0010, lo: 0x94, hi: 0x97},
	{value: 0x0010, lo: 0x9f, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa3},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xba, hi: 0xbf},
	// Block 0x2d, offset 0x188
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x96},
	{value: 0x0010, lo: 0x9a, hi: 0xb1},
	{value: 0x0010, lo: 0xb3, hi: 0xbb},
	{value: 0x0010, lo: 0xbd, hi: 0xbd},
	// Block 0x2e, offset 0x18d
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0010, lo: 0x8f, hi: 0x91},
	{value: 0x0014, lo: 0x92, hi: 0x94},
	{value: 0x0014, lo: 0x96, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x9f},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	// Block 0x2f, offset 0x195
	{value: 0x0014, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb4, hi: 0xb7},
	{value: 0x0034, lo: 0xb8, hi: 0xba},
	// Block 0x30, offset 0x198
	{value: 0x0004, lo: 0x86, hi: 0x86},
	{value: 0x0014, lo: 0x87, hi: 0x87},
	{value: 0x0034, lo: 0x88, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8e},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x31, offset 0x19d
	{value: 0x0014, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb4, hi: 0xb7},
	{value: 0x0034, lo: 0xb8, hi: 0xb9},
	{value: 0x0014, lo: 0xbb, hi: 0xbc},
	// Block 0x32, offset 0x1a1
	{value: 0x0004, lo: 0x86, hi: 0x86},
	{value: 0x0034, lo: 0x88, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x33, offset 0x1a5
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0034, lo: 0x98, hi: 0x99},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0x0034, lo: 0xb5, hi: 0xb5},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	{value: 0x0034, lo: 0xb9, hi: 0xb9},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x34, offset 0x1ac
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0010, lo: 0x89, hi: 0xac},
	{value: 0x0034, lo: 0xb1, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xba, hi: 0xbd},
	{value: 0x0014, lo: 0xbe, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x35, offset 0x1b5
	{value: 0x0034, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0024, lo: 0x82, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x84},
	{value: 0x0024, lo: 0x86, hi: 0x87},
	{value: 0x0010, lo: 0x88, hi: 0x8c},
	{value: 0x0014, lo: 0x8d, hi: 0x97},
	{value: 0x0014, lo: 0x99, hi: 0xbc},
	// Block 0x36, offset 0x1bd
	{value: 0x0034, lo: 0x86, hi: 0x86},
	// Block 0x37, offset 0x1be
	{value: 0x0010, lo: 0xab, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	{value: 0x0010, lo: 0xb8, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbc},
	{value: 0x0014, lo: 0xbd, hi: 0xbe},
	// Block 0x38, offset 0x1c7
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x96, hi: 0x97},
	{value: 0x0014, lo: 0x98, hi: 0x99},
	{value: 0x0014, lo: 0x9e, hi: 0xa0},
	{value: 0x0010, lo: 0xa2, hi: 0xa4},
	{value: 0x0010, lo: 0xa7, hi: 0xad},
	{value: 0x0014, lo: 0xb1, hi: 0xb4},
	// Block 0x39, offset 0x1ce
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x84},
	{value: 0x0014, lo: 0x85, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x8f, hi: 0x9c},
	{value: 0x0014, lo: 0x9d, hi: 0x9d},
	{value: 0x6c53, lo: 0xa0, hi: 0xbf},
	// Block 0x3a, offset 0x1d6
	{value: 0x7053, lo: 0x80, hi: 0x85},
	{value: 0x7053, lo: 0x87, hi: 0x87},
	{value: 0x7053, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0xba},
	{value: 0x0014, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x3b, offset 0x1dc
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x98},
	{value: 0x0010, lo: 0x9a, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x3c, offset 0x1e2
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb5},
	{value: 0x0010, lo: 0xb8, hi: 0xbe},
	// Block 0x3d, offset 0x1e7
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x82, hi: 0x85},
	{value: 0x0010, lo: 0x88, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0xbf},
	// Block 0x3e, offset 0x1eb
	{value: 0x0010, lo: 0x80, hi: 0x90},
	{value: 0x0010, lo: 0x92, hi: 0x95},
	{value: 0x0010, lo: 0x98, hi: 0xbf},
	// Block 0x3f, offset 0x1ee
	{value: 0x0010, lo: 0x80, hi: 0x9a},
	{value: 0x0024, lo: 0x9d, hi: 0x9f},
	// Block 0x40, offset 0x1f0
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	{value: 0x7453, lo: 0xa0, hi: 0xaf},
	{value: 0x7853, lo: 0xb0, hi: 0xbf},
	// Block 0x41, offset 0x1f3
	{value: 0x7c53, lo: 0x80, hi: 0x8f},
	{value: 0x8053, lo: 0x90, hi: 0x9f},
	{value: 0x7c53, lo: 0xa0, hi: 0xaf},
	{value: 0x0813, lo: 0xb0, hi: 0xb5},
	{value: 0x0892, lo: 0xb8, hi: 0xbd},
	// Block 0x42, offset 0x1f8
	{value: 0x0010, lo: 0x81, hi: 0xbf},
	// Block 0x43, offset 0x1f9
	{value: 0x0010, lo: 0x80, hi: 0xac},
	{value: 0x0010, lo: 0xaf, hi: 0xbf},
	// Block 0x44, offset 0x1fb
	{value: 0x0010, lo: 0x81, hi: 0x9a},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x45, offset 0x1fd
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0010, lo: 0xae, hi: 0xb8},
	// Block 0x46, offset 0x1ff
	{value: 0x0010, lo: 0x80, hi: 0x8c},
	{value: 0x0010, lo: 0x8e, hi: 0x91},
	{value: 0x0014, lo: 0x92, hi: 0x93},
	{value: 0x0034, lo: 0x94, hi: 0x94},
	{value: 0x0010, lo: 0xa0, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	// Block 0x47, offset 0x206
	{value: 0x0010, lo: 0x80, hi: 0x91},
	{value: 0x0014, lo: 0x92, hi: 0x93},
	{value: 0x0010, lo: 0xa0, hi: 0xac},
	{value: 0x0010, lo: 0xae, hi: 0xb0},
	{value: 0x0014, lo: 0xb2, hi: 0xb3},
	// Block 0x48, offset 0x20b
	{value: 0x0014, lo: 0xb4, hi: 0xb5},
	{value: 0x0010, lo: 0xb6, hi: 0xb6},
	{value: 0x0014, lo: 0xb7, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0x49, offset 0x20f
	{value: 0x0010, lo: 0x80, hi: 0x85},
	{value: 0x0014, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0014, lo: 0x89, hi: 0x91},
	{value: 0x0034, lo: 0x92, hi: 0x92},
	{value: 0x0014, lo: 0x93, hi: 0x93},
	{value: 0x0004, lo: 0x97, hi: 0x97},
	{value: 0x0024, lo: 0x9d, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	// Block 0x4a, offset 0x218
	{value: 0x0014, lo: 0x8b, hi: 0x8e},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x4b, offset 0x21b
	{value: 0x0010, lo: 0x80, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0xb7},
	// Block 0x4c, offset 0x21e
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0014, lo: 0x85, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0xa8},
	{value: 0x0034, lo: 0xa9, hi: 0xa9},
	{value: 0x0010, lo: 0xaa, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x4d, offset 0x224
	{value: 0x0010, lo: 0x80, hi: 0xb5},
	// Block 0x4e, offset 0x225
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	{value: 0x0014, lo: 0xa0, hi: 0xa2},
	{value: 0x0010, lo: 0xa3, hi: 0xa6},
	{value: 0x0014, lo: 0xa7, hi: 0xa8},
	{value: 0x0010, lo: 0xa9, hi: 0xab},
	{value: 0x0010, lo: 0xb0, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb2},
	{value: 0x0010, lo: 0xb3, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xb9},
	{value: 0x0024, lo: 0xba, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbb},
	// Block 0x4f, offset 0x230
	{value: 0x0010, lo: 0x86, hi: 0x8f},
	// Block 0x50, offset 0x231
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x51, offset 0x232
	{value: 0x0010, lo: 0x80, hi: 0x96},
	{value: 0x0024, lo: 0x97, hi: 0x97},
	{value: 0x0034, lo: 0x98, hi: 0x98},
	{value: 0x0010, lo: 0x99, hi: 0x9a},
	{value: 0x0014, lo: 0x9b, hi: 0x9b},
	// Block 0x52, offset 0x237
	{value: 0x0010, lo: 0x95, hi: 0x95},
	{value: 0x0014, lo: 0x96, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0014, lo: 0x98, hi: 0x9e},
	{value: 0x0034, lo: 0xa0, hi: 0xa0},
	{value: 0x0010, lo: 0xa1, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa2},
	{value: 0x0010, lo: 0xa3, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xac},
	{value: 0x0010, lo: 0xad, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb4},
	{value: 0x0024, lo: 0xb5, hi: 0xbc},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0x53, offset 0x244
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0004, lo: 0xa7, hi: 0xa7},
	{value: 0x0024, lo: 0xb0, hi: 0xb4},
	{value: 0x0034, lo: 0xb5, hi: 0xba},
	{value: 0x0024, lo: 0xbb, hi: 0xbc},
	{value: 0x0034, lo: 0xbd, hi: 0xbd},
	{value: 0x0014, lo: 0xbe, hi: 0xbe},
	// Block 0x54, offset 0x24c
	{value: 0x0014, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x55, offset 0x254
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x83},
	{value: 0x0030, lo: 0x84, hi: 0x84},
	{value: 0x0010, lo: 0x85, hi: 0x8b},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0024, lo: 0xab, hi: 0xab},
	{value: 0x0034, lo: 0xac, hi: 0xac},
	{value: 0x0024, lo: 0xad, hi: 0xb3},
	// Block 0x56, offset 0x25d
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa5},
	{value: 0x0010, lo: 0xa6, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa9},
	{value: 0x0030, lo: 0xaa, hi: 0xaa},
	{value: 0x0034, lo: 0xab, hi: 0xab},
	{value: 0x0014, lo: 0xac, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xbf},
	// Block 0x57, offset 0x266
	{value: 0x0010, lo: 0x80, hi: 0xa5},
	{value: 0x0034, lo: 0xa6, hi: 0xa6},
	{value: 0x0010, lo: 0xa7, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa9},
	{value: 0x0010, lo: 0xaa, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xae},
	{value: 0x0014, lo: 0xaf, hi: 0xb1},
	{value: 0x0030, lo: 0xb2, hi: 0xb3},
	// Block 0x58, offset 0x26f
	{value: 0x0010, lo: 0x80, hi: 0xab},
	{value: 0x0014, lo: 0xac, hi: 0xb3},
	{value: 0x0010, lo: 0xb4, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	// Block 0x59, offset 0x274
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x8d, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbd},
	// Block 0x5a, offset 0x277
	{value: 0x1a6a, lo: 0x80, hi: 0x80},
	{value: 0x1aea, lo: 0x81, hi: 0x81},
	{value: 0x1b6a, lo: 0x82, hi: 0x82},
	{value: 0x1bea, lo: 0x83, hi: 0x83},
	{value: 0x1c6a, lo: 0x84, hi: 0x84},
	{value: 0x1cea, lo: 0x85, hi: 0x85},
	{value: 0x1d6a, lo: 0x86, hi: 0x86},
	{value: 0x1dea, lo: 0x87, hi: 0x87},
	{value: 0x1e6a, lo: 0x88, hi: 0x88},
	// Block 0x5b, offset 0x280
	{value: 0x0024, lo: 0x90, hi: 0x92},
	{value: 0x0034, lo: 0x94, hi: 0x99},
	{value: 0x0024, lo: 0x9a, hi: 0x9b},
	{value: 0x0034, lo: 0x9c, hi: 0x9f},
	{value: 0x0024, lo: 0xa0, hi: 0xa0},
	{value: 0x0010, lo: 0xa1, hi: 0xa1},
	{value: 0x0034, lo: 0xa2, hi: 0xa8},
	{value: 0x0010, lo: 0xa9, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xb3},
	{value: 0x0024, lo: 0xb4, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb7},
	{value: 0x0024, lo: 0xb8, hi: 0xb9},
	// Block 0x5c, offset 0x28d
	{value: 0x0012, lo: 0x80, hi: 0xab},
	{value: 0x0015, lo: 0xac, hi: 0xbf},
	// Block 0x5d, offset 0x28f
	{value: 0x0015, lo: 0x80, hi: 0xaa},
	{value: 0x0012, lo: 0xab, hi: 0xb7},
	{value: 0x0015, lo: 0xb8, hi: 0xb8},
	{value: 0x8452, lo: 0xb9, hi: 0xb9},
	{value: 0x0012, lo: 0xba, hi: 0xbc},
	{value: 0x8852, lo: 0xbd, hi: 0xbd},
	{value: 0x0012, lo: 0xbe, hi: 0xbf},
	// Block 0x5e, offset 0x296
	{value: 0x0012, lo: 0x80, hi: 0x9a},
	{value: 0x0015, lo: 0x9b, hi: 0xbf},
	// Block 0x5f, offset 0x298
	{value: 0x0024, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0024, lo: 0x83, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0024, lo: 0x8b, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x90},
	{value: 0x0024, lo: 0x91, hi: 0xb5},
	{value: 0x0034, lo: 0xb6, hi: 0xb9},
	{value: 0x0024, lo: 0xbb, hi: 0xbb},
	{value: 0x0034, lo: 0xbc, hi: 0xbd},
	{value: 0x0024, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0x60, offset 0x2a4
	{value: 0x0117, lo: 0x80, hi: 0xbf},
	// Block 0x61, offset 0x2a5
	{value: 0x0117, lo: 0x80, hi: 0x95},
	{value: 0x1f1a, lo: 0x96, hi: 0x96},
	{value: 0x1fca, lo: 0x97, hi: 0x97},
	{value: 0x207a, lo: 0x98, hi: 0x98},
	{value: 0x212a, lo: 0x99, hi: 0x99},
	{value: 0x21da, lo: 0x9a, hi: 0x9a},
	{value: 0x228a, lo: 0x9b, hi: 0x9b},
	{value: 0x0012, lo: 0x9c, hi: 0x9d},
	{value: 0x233b, lo: 0x9e, hi: 0x9e},
	{value: 0x0012, lo: 0x9f, hi: 0x9f},
	{value: 0x0117, lo: 0xa0, hi: 0xbf},
	// Block 0x62, offset 0x2b0
	{value: 0x0812, lo: 0x80, hi: 0x87},
	{value: 0x0813, lo: 0x88, hi: 0x8f},
	{value: 0x0812, lo: 0x90, hi: 0x95},
	{value: 0x0813, lo: 0x98, hi: 0x9d},
	{value: 0x0812, lo: 0xa0, hi: 0xa7},
	{value: 0x0813, lo: 0xa8, hi: 0xaf},
	{value: 0x0812, lo: 0xb0, hi: 0xb7},
	{value: 0x0813, lo: 0xb8, hi: 0xbf},
	// Block 0x63, offset 0x2b8
	{value: 0x0004, lo: 0x8b, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8f},
	{value: 0x0054, lo: 0x98, hi: 0x99},
	{value: 0x0054, lo: 0xa4, hi: 0xa4},
	{value: 0x0054, lo: 0xa7, hi: 0xa7},
	{value: 0x0014, lo: 0xaa, hi: 0xae},
	{value: 0x0010, lo: 0xaf, hi: 0xaf},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x64, offset 0x2c0
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x94, hi: 0x94},
	{value: 0x0014, lo: 0xa0, hi: 0xa4},
	{value: 0x0014, lo: 0xa6, hi: 0xaf},
	{value: 0x0015, lo: 0xb1, hi: 0xb1},
	{value: 0x0015, lo: 0xbf, hi: 0xbf},
	// Block 0x65, offset 0x2c6
	{value: 0x0015, lo: 0x90, hi: 0x9c},
	// Block 0x66, offset 0x2c7
	{value: 0x0024, lo: 0x90, hi: 0x91},
	{value: 0x0034, lo: 0x92, hi: 0x93},
	{value: 0x0024, lo: 0x94, hi: 0x97},
	{value: 0x0034, lo: 0x98, hi: 0x9a},
	{value: 0x0024, lo: 0x9b, hi: 0x9c},
	{value: 0x0014, lo: 0x9d, hi: 0xa0},
	{value: 0x0024, lo: 0xa1, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa4},
	{value: 0x0034, lo: 0xa5, hi: 0xa6},
	{value: 0x0024, lo: 0xa7, hi: 0xa7},
	{value: 0x0034, lo: 0xa8, hi: 0xa8},
	{value: 0x0024, lo: 0xa9, hi: 0xa9},
	{value: 0x0034, lo: 0xaa, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb0},
	// Block 0x67, offset 0x2d5
	{value: 0x0016, lo: 0x85, hi: 0x86},
	{value: 0x0012, lo: 0x87, hi: 0x89},
	{value: 0x9d52, lo: 0x8e, hi: 0x8e},
	{value: 0x1013, lo: 0xa0, hi: 0xaf},
	{value: 0x1012, lo: 0xb0, hi: 0xbf},
	// Block 0x68, offset 0x2da
	{value: 0x0010, lo: 0x80, hi: 0x82},
	{value: 0x0716, lo: 0x83, hi: 0x84},
	{value: 0x0010, lo: 0x85, hi: 0x88},
	// Block 0x69, offset 0x2dd
	{value: 0xa053, lo: 0xb6, hi: 0xb7},
	{value: 0xa353, lo: 0xb8, hi: 0xb9},
	{value: 0xa653, lo: 0xba, hi: 0xbb},
	{value: 0xa353, lo: 0xbc, hi: 0xbd},
	{value: 0xa053, lo: 0xbe, hi: 0xbf},
	// Block 0x6a, offset 0x2e2
	{value: 0x3013, lo: 0x80, hi: 0x8f},
	{value: 0x6553, lo: 0x90, hi: 0x9f},
	{value: 0xa953, lo: 0xa0, hi: 0xae},
	{value: 0x3012, lo: 0xb0, hi: 0xbf},
	// Block 0x6b, offset 0x2e6
	{value: 0x0117, lo: 0x80, hi: 0xa3},
	{value: 0x0012, lo: 0xa4, hi: 0xa4},
	{value: 0x0716, lo: 0xab, hi: 0xac},
	{value: 0x0316, lo: 0xad, hi: 0xae},
	{value: 0x0024, lo: 0xaf, hi: 0xb1},
	{value: 0x0117, lo: 0xb2, hi: 0xb3},
	// Block 0x6c, offset 0x2ec
	{value: 0x6c52, lo: 0x80, hi: 0x9f},
	{value: 0x7052, lo: 0xa0, hi: 0xa5},
	{value: 0x7052, lo: 0xa7, hi: 0xa7},
	{value: 0x7052, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x6d, offset 0x2f1
	{value: 0x0010, lo: 0x80, hi: 0xa7},
	{value: 0x0014, lo: 0xaf, hi: 0xaf},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0x6e, offset 0x2f4
	{value: 0x0010, lo: 0x80, hi: 0x96},
	{value: 0x0010, lo: 0xa0, hi: 0xa6},
	{value: 0x0010, lo: 0xa8, hi: 0xae},
	{value: 0x0010, lo: 0xb0, hi: 0xb6},
	{value: 0x0010, lo: 0xb8, hi: 0xbe},
	// Block 0x6f, offset 0x2f9
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x88, hi: 0x8e},
	{value: 0x0010, lo: 0x90, hi: 0x96},
	{value: 0x0010, lo: 0x98, hi: 0x9e},
	{value: 0x0024, lo: 0xa0, hi: 0xbf},
	// Block 0x70, offset 0x2fe
	{value: 0x0014, lo: 0xaf, hi: 0xaf},
	// Block 0x71, offset 0x2ff
	{value: 0x0014, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0xaa, hi: 0xad},
	{value: 0x0030, lo: 0xae, hi: 0xaf},
	{value: 0x0004, lo: 0xb1, hi: 0xb5},
	{value: 0x0014, lo: 0xbb, hi: 0xbb},
	{value: 0x0010, lo: 0xbc, hi: 0xbc},
	// Block 0x72, offset 0x305
	{value: 0x0034, lo: 0x99, hi: 0x9a},
	{value: 0x0004, lo: 0x9b, hi: 0x9e},
	// Block 0x73, offset 0x307
	{value: 0x0004, lo: 0xbc, hi: 0xbe},
	// Block 0x74, offset 0x308
	{value: 0x0010, lo: 0x85, hi: 0xae},
	{value: 0x0010, lo: 0xb1, hi: 0xbf},
	// Block 0x75, offset 0x30a
	{value: 0x0010, lo: 0x80, hi: 0x8e},
	{value: 0x0010, lo: 0xa0, hi: 0xba},
	// Block 0x76, offset 0x30c
	{value: 0x0010, lo: 0x80, hi: 0x94},
	{value: 0x0014, lo: 0x95, hi: 0x95},
	{value: 0x0010, lo: 0x96, hi: 0xbf},
	// Block 0x77, offset 0x30f
	{value: 0x0010, lo: 0x80, hi: 0x8c},
	// Block 0x78, offset 0x310
	{value: 0x0010, lo: 0x90, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbd},
	// Block 0x79, offset 0x312
	{value: 0x0010, lo: 0x80, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0010, lo: 0x90, hi: 0xab},
	// Block 0x7a, offset 0x315
	{value: 0x0117, lo: 0x80, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xae},
	{value: 0x0024, lo: 0xaf, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb2},
	{value: 0x0024, lo: 0xb4, hi: 0xbd},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x7b, offset 0x31b
	{value: 0x0117, lo: 0x80, hi: 0x9b},
	{value: 0x0015, lo: 0x9c, hi: 0x9d},
	{value: 0x0024, lo: 0x9e, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0x7c, offset 0x31f
	{value: 0x0010, lo: 0x80, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb1},
	// Block 0x7d, offset 0x321
	{value: 0x0004, lo: 0x80, hi: 0x96},
	{value: 0x0014, lo: 0x97, hi: 0xa1},
	{value: 0x0117, lo: 0xa2, hi: 0xaf},
	{value: 0x0012, lo: 0xb0, hi: 0xb1},
	{value: 0x0117, lo: 0xb2, hi: 0xbf},
	// Block 0x7e, offset 0x326
	{value: 0x0117, lo: 0x80, hi: 0xaf},
	{value: 0x0015, lo: 0xb0, hi: 0xb0},
	{value: 0x0012, lo: 0xb1, hi: 0xb8},
	{value: 0x0316, lo: 0xb9, hi: 0xba},
	{value: 0x0716, lo: 0xbb, hi: 0xbc},
	{value: 0x8453, lo: 0xbd, hi: 0xbd},
	{value: 0x0117, lo: 0xbe, hi: 0xbf},
	// Block 0x7f, offset 0x32d
	{value: 0x0010, lo: 0xb7, hi: 0xb7},
	{value: 0x0015, lo: 0xb8, hi: 0xb9},
	{value: 0x0012, lo: 0xba, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbf},
	// Block 0x80, offset 0x331
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0014, lo: 0x82, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x8a},
	{value: 0x0014, lo: 0x8b, hi: 0x8b},
	{value: 0x0010, lo: 0x8c, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xa6},
	{value: 0x0010, lo: 0xa7, hi: 0xa7},
	// Block 0x81, offset 0x33a
	{value: 0x0010, lo: 0x80, hi: 0xb3},
	// Block 0x82, offset 0x33b
	{value: 0x0010, lo: 0x80, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x84},
	{value: 0x0014, lo: 0x85, hi: 0x85},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0024, lo: 0xa0, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xb7},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0010, lo: 0xbd, hi: 0xbd},
	// Block 0x83, offset 0x343
	{value: 0x0010, lo: 0x80, hi: 0xa5},
	{value: 0x0014, lo: 0xa6, hi: 0xaa},
	{value: 0x0034, lo: 0xab, hi: 0xad},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x84, offset 0x347
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0014, lo: 0x87, hi: 0x91},
	{value: 0x0010, lo: 0x92, hi: 0x92},
	{value: 0x0030, lo: 0x93, hi: 0x93},
	{value: 0x0010, lo: 0xa0, hi: 0xbc},
	// Block 0x85, offset 0x34c
	{value: 0x0014, lo: 0x80, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0xb2},
	{value: 0x0034, lo: 0xb3, hi: 0xb3},
	{value: 0x0010, lo: 0xb4, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xb9},
	{value: 0x0010, lo: 0xba, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0x86, offset 0x354
	{value: 0x0030, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0014, lo: 0xa5, hi: 0xa5},
	{value: 0x0004, lo: 0xa6, hi: 0xa6},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0x87, offset 0x35a
	{value: 0x0010, lo: 0x80, hi: 0xa8},
	{value: 0x0014, lo: 0xa9, hi: 0xae},
	{value: 0x0010, lo: 0xaf, hi: 0xb0},
	{value: 0x0014, lo: 0xb1, hi: 0xb2},
	{value: 0x0010, lo: 0xb3, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb6},
	// Block 0x88, offset 0x360
	{value: 0x0010, lo: 0x80, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0x8b},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0010, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0004, lo: 0xb0, hi: 0xb0},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbd},
	// Block 0x89, offset 0x36a
	{value: 0x0024, lo: 0xb0, hi: 0xb0},
	{value: 0x0024, lo: 0xb2, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0024, lo: 0xb7, hi: 0xb8},
	{value: 0x0024, lo: 0xbe, hi: 0xbf},
	// Block 0x8a, offset 0x36f
	{value: 0x0024, lo: 0x81, hi: 0x81},
	{value: 0x0004, lo: 0x9d, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xab},
	{value: 0x0014, lo: 0xac, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xaf},
	{value: 0x0010, lo: 0xb2, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb4},
	{value: 0x0010, lo: 0xb5, hi: 0xb5},
	{value: 0x0034, lo: 0xb6, hi: 0xb6},
	// Block 0x8b, offset 0x378
	{value: 0x0010, lo: 0x81, hi: 0x86},
	{value: 0x0010, lo: 0x89, hi: 0x8e},
	{value: 0x0010, lo: 0x91, hi: 0x96},
	{value: 0x0010, lo: 0xa0, hi: 0xa6},
	{value: 0x0010, lo: 0xa8, hi: 0xae},
	{value: 0x0012, lo: 0xb0, hi: 0xbf},
	// Block 0x8c, offset 0x37e
	{value: 0x0012, lo: 0x80, hi: 0x92},
	{value: 0xac52, lo: 0x93, hi: 0x93},
	{value: 0x0012, lo: 0x94, hi: 0x9a},
	{value: 0x0014, lo: 0x9b, hi: 0x9b},
	{value: 0x0015, lo: 0x9c, hi: 0x9f},
	{value: 0x0012, lo: 0xa0, hi: 0xa5},
	{value: 0x74d2, lo: 0xb0, hi: 0xbf},
	// Block 0x8d, offset 0x385
	{value: 0x78d2, lo: 0x80, hi: 0x8f},
	{value: 0x7cd2, lo: 0x90, hi: 0x9f},
	{value: 0x80d2, lo: 0xa0, hi: 0xaf},
	{value: 0x7cd2, lo: 0xb0, hi: 0xbf},
	// Block 0x8e, offset 0x389
	{value: 0x0010, lo: 0x80, hi: 0xa4},
	{value: 0x0014, lo: 0xa5, hi: 0xa5},
	{value: 0x0010, lo: 0xa6, hi: 0xa7},
	{value: 0x0014, lo: 0xa8, hi: 0xa8},
	{value: 0x0010, lo: 0xa9, hi: 0xaa},
	{value: 0x0010, lo: 0xac, hi: 0xac},
	{value: 0x0034, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0x8f, offset 0x391
	{value: 0x0010, lo: 0x80, hi: 0xa3},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0x90, offset 0x393
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x8b, hi: 0xbb},
	// Block 0x91, offset 0x395
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x83, hi: 0x84},
	{value: 0x0010, lo: 0x86, hi: 0xbf},
	// Block 0x92, offset 0x398
	{value: 0x0010, lo: 0x80, hi: 0xb1},
	{value: 0x0004, lo: 0xb2, hi: 0xbf},
	// Block 0x93, offset 0x39a
	{value: 0x0004, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x93, hi: 0xbf},
	// Block 0x94, offset 0x39c
	{value: 0x0010, lo: 0x80, hi: 0xbd},
	// Block 0x95, offset 0x39d
	{value: 0x0010, lo: 0x90, hi: 0xbf},
	// Block 0x96, offset 0x39e
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	{value: 0x0010, lo: 0x92, hi: 0xbf},
	// Block 0x97, offset 0x3a0
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0010, lo: 0xb0, hi: 0xbb},
	// Block 0x98, offset 0x3a2
	{value: 0x0014, lo: 0x80, hi: 0x8f},
	{value: 0x0054, lo: 0x93, hi: 0x93},
	{value: 0x0024, lo: 0xa0, hi: 0xa6},
	{value: 0x0034, lo: 0xa7, hi: 0xad},
	{value: 0x0024, lo: 0xae, hi: 0xaf},
	{value: 0x0010, lo: 0xb3, hi: 0xb4},
	// Block 0x99, offset 0x3a8
	{value: 0x0010, lo: 0x8d, hi: 0x8f},
	{value: 0x0054, lo: 0x92, hi: 0x92},
	{value: 0x0054, lo: 0x95, hi: 0x95},
	{value: 0x0010, lo: 0xb0, hi: 0xb4},
	{value: 0x0010, lo: 0xb6, hi: 0xbf},
	// Block 0x9a, offset 0x3ad
	{value: 0x0010, lo: 0x80, hi: 0xbc},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0x9b, offset 0x3af
	{value: 0x0054, lo: 0x87, hi: 0x87},
	{value: 0x0054, lo: 0x8e, hi: 0x8e},
	{value: 0x0054, lo: 0x9a, hi: 0x9a},
	{value: 0x5f53, lo: 0xa1, hi: 0xba},
	{value: 0x0004, lo: 0xbe, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0x9c, offset 0x3b5
	{value: 0x0004, lo: 0x80, hi: 0x80},
	{value: 0x5f52, lo: 0x81, hi: 0x9a},
	{value: 0x0004, lo: 0xb0, hi: 0xb0},
	// Block 0x9d, offset 0x3b8
	{value: 0x0014, lo: 0x9e, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xbe},
	// Block 0x9e, offset 0x3ba
	{value: 0x0010, lo: 0x82, hi: 0x87},
	{value: 0x0010, lo: 0x8a, hi: 0x8f},
	{value: 0x0010, lo: 0x92, hi: 0x97},
	{value: 0x0010, lo: 0x9a, hi: 0x9c},
	{value: 0x0004, lo: 0xa3, hi: 0xa3},
	{value: 0x0014, lo: 0xb9, hi: 0xbb},
	// Block 0x9f, offset 0x3c0
	{value: 0x0010, lo: 0x80, hi: 0x8b},
	{value: 0x0010, lo: 0x8d, hi: 0xa6},
	{value: 0x0010, lo: 0xa8, hi: 0xba},
	{value: 0x0010, lo: 0xbc, hi: 0xbd},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xa0, offset 0x3c5
	{value: 0x0010, lo: 0x80, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x9d},
	// Block 0xa1, offset 0x3c7
	{value: 0x0010, lo: 0x80, hi: 0xba},
	// Block 0xa2, offset 0x3c8
	{value: 0x0010, lo: 0x80, hi: 0xb4},
	// Block 0xa3, offset 0x3c9
	{value: 0x0034, lo: 0xbd, hi: 0xbd},
	// Block 0xa4, offset 0x3ca
	{value: 0x0010, lo: 0x80, hi: 0x9c},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0xa5, offset 0x3cc
	{value: 0x0010, lo: 0x80, hi: 0x90},
	{value: 0x0034, lo: 0xa0, hi: 0xa0},
	// Block 0xa6, offset 0x3ce
	{value: 0x0010, lo: 0x80, hi: 0x9f},
	{value: 0x0010, lo: 0xad, hi: 0xbf},
	// Block 0xa7, offset 0x3d0
	{value: 0x0010, lo: 0x80, hi: 0x8a},
	{value: 0x0010, lo: 0x90, hi: 0xb5},
	{value: 0x0024, lo: 0xb6, hi: 0xba},
	// Block 0xa8, offset 0x3d3
	{value: 0x0010, lo: 0x80, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xbf},
	// Block 0xa9, offset 0x3d5
	{value: 0x0010, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x88, hi: 0x8f},
	{value: 0x0010, lo: 0x91, hi: 0x95},
	// Block 0xaa, offset 0x3d8
	{value: 0x2813, lo: 0x80, hi: 0x87},
	{value: 0x3813, lo: 0x88, hi: 0x8f},
	{value: 0x2813, lo: 0x90, hi: 0x97},
	{value: 0xaf53, lo: 0x98, hi: 0x9f},
	{value: 0xb253, lo: 0xa0, hi: 0xa7},
	{value: 0x2812, lo: 0xa8, hi: 0xaf},
	{value: 0x3812, lo: 0xb0, hi: 0xb7},
	{value: 0x2812, lo: 0xb8, hi: 0xbf},
	// Block 0xab, offset 0x3e0
	{value: 0xaf52, lo: 0x80, hi: 0x87},
	{value: 0xb252, lo: 0x88, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0xbf},
	// Block 0xac, offset 0x3e3
	{value: 0x0010, lo: 0x80, hi: 0x9d},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0xb253, lo: 0xb0, hi: 0xb7},
	{value: 0xaf53, lo: 0xb8, hi: 0xbf},
	// Block 0xad, offset 0x3e7
	{value: 0x2813, lo: 0x80, hi: 0x87},
	{value: 0x3813, lo: 0x88, hi: 0x8f},
	{value: 0x2813, lo: 0x90, hi: 0x93},
	{value: 0xb252, lo: 0x98, hi: 0x9f},
	{value: 0xaf52, lo: 0xa0, hi: 0xa7},
	{value: 0x2812, lo: 0xa8, hi: 0xaf},
	{value: 0x3812, lo: 0xb0, hi: 0xb7},
	{value: 0x2812, lo: 0xb8, hi: 0xbb},
	// Block 0xae, offset 0x3ef
	{value: 0x0010, lo: 0x80, hi: 0xa7},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xaf, offset 0x3f1
	{value: 0x0010, lo: 0x80, hi: 0xa3},
	// Block 0xb0, offset 0x3f2
	{value: 0x0010, lo: 0x80, hi: 0xb6},
	// Block 0xb1, offset 0x3f3
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xa7},
	// Block 0xb2, offset 0x3f5
	{value: 0x0010, lo: 0x80, hi: 0x85},
	{value: 0x0010, lo: 0x88, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0xb5},
	{value: 0x0010, lo: 0xb7, hi: 0xb8},
	{value: 0x0010, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xb3, offset 0x3fb
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xb6},
	// Block 0xb4, offset 0x3fd
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	// Block 0xb5, offset 0x3fe
	{value: 0x0010, lo: 0xa0, hi: 0xb2},
	{value: 0x0010, lo: 0xb4, hi: 0xb5},
	// Block 0xb6, offset 0x400
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xb9},
	// Block 0xb7, offset 0x402
	{value: 0x0010, lo: 0x80, hi: 0xb7},
	{value: 0x0010, lo: 0xbe, hi: 0xbf},
	// Block 0xb8, offset 0x404
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x83},
	{value: 0x0014, lo: 0x85, hi: 0x86},
	{value: 0x0014, lo: 0x8c, hi: 0x8c},
	{value: 0x0034, lo: 0x8d, hi: 0x8d},
	{value: 0x0014, lo: 0x8e, hi: 0x8e},
	{value: 0x0024, lo: 0x8f, hi: 0x8f},
	{value: 0x0010, lo: 0x90, hi: 0x93},
	{value: 0x0010, lo: 0x95, hi: 0x97},
	{value: 0x0010, lo: 0x99, hi: 0xb3},
	{value: 0x0024, lo: 0xb8, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xb9, offset 0x411
	{value: 0x0010, lo: 0xa0, hi: 0xbc},
	// Block 0xba, offset 0x412
	{value: 0x0010, lo: 0x80, hi: 0x9c},
	// Block 0xbb, offset 0x413
	{value: 0x0010, lo: 0x80, hi: 0x87},
	{value: 0x0010, lo: 0x89, hi: 0xa4},
	{value: 0x0024, lo: 0xa5, hi: 0xa5},
	{value: 0x0034, lo: 0xa6, hi: 0xa6},
	// Block 0xbc, offset 0x417
	{value: 0x0010, lo: 0x80, hi: 0x95},
	{value: 0x0010, lo: 0xa0, hi: 0xb2},
	// Block 0xbd, offset 0x419
	{value: 0x0010, lo: 0x80, hi: 0x91},
	// Block 0xbe, offset 0x41a
	{value: 0x0010, lo: 0x80, hi: 0x88},
	// Block 0xbf, offset 0x41b
	{value: 0x5653, lo: 0x80, hi: 0xb2},
	// Block 0xc0, offset 0x41c
	{value: 0x5652, lo: 0x80, hi: 0xb2},
	// Block 0xc1, offset 0x41d
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbf},
	// Block 0xc2, offset 0x421
	{value: 0x0014, lo: 0x80, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0xa6, hi: 0xaf},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xc3, offset 0x425
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb6},
	{value: 0x0010, lo: 0xb7, hi: 0xb8},
	{value: 0x0034, lo: 0xb9, hi: 0xba},
	{value: 0x0014, lo: 0xbd, hi: 0xbd},
	// Block 0xc4, offset 0x42b
	{value: 0x0010, lo: 0x90, hi: 0xa8},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0xc5, offset 0x42d
	{value: 0x0024, lo: 0x80, hi: 0x82},
	{value: 0x0010, lo: 0x83, hi: 0xa6},
	{value: 0x0014, lo: 0xa7, hi: 0xab},
	{value: 0x0010, lo: 0xac, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xb2},
	{value: 0x0034, lo: 0xb3, hi: 0xb4},
	{value: 0x0010, lo: 0xb6, hi: 0xbf},
	// Block 0xc6, offset 0x434
	{value: 0x0010, lo: 0x90, hi: 0xb2},
	{value: 0x0034, lo: 0xb3, hi: 0xb3},
	{value: 0x0010, lo: 0xb6, hi: 0xb6},
	// Block 0xc7, offset 0x437
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0xb5},
	{value: 0x0014, lo: 0xb6, hi: 0xbe},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xc8, offset 0x43b
	{value: 0x0030, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x84},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0014, lo: 0x8b, hi: 0x8c},
	{value: 0x0010, lo: 0x90, hi: 0x9a},
	{value: 0x0010, lo: 0x9c, hi: 0x9c},
	// Block 0xc9, offset 0x441
	{value: 0x0010, lo: 0x80, hi: 0x91},
	{value: 0x0010, lo: 0x93, hi: 0xae},
	{value: 0x0014, lo: 0xaf, hi: 0xb1},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0014, lo: 0xb4, hi: 0xb4},
	{value: 0x0030, lo: 0xb5, hi: 0xb5},
	{value: 0x0034, lo: 0xb6, hi: 0xb6},
	{value: 0x0014, lo: 0xb7, hi: 0xb7},
	{value: 0x0014, lo: 0xbe, hi: 0xbe},
	// Block 0xca, offset 0x44a
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x88, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0x8d},
	{value: 0x0010, lo: 0x8f, hi: 0x9d},
	{value: 0x0010, lo: 0x9f, hi: 0xa8},
	{value: 0x0010, lo: 0xb0, hi: 0xbf},
	// Block 0xcb, offset 0x450
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	{value: 0x0014, lo: 0x9f, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa2},
	{value: 0x0014, lo: 0xa3, hi: 0xa8},
	{value: 0x0034, lo: 0xa9, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0xcc, offset 0x456
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0010, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x8c},
	{value: 0x0010, lo: 0x8f, hi: 0x90},
	{value: 0x0010, lo: 0x93, hi: 0xa8},
	{value: 0x0010, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb5, hi: 0xb9},
	{value: 0x0034, lo: 0xbc, hi: 0xbc},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0xcd, offset 0x460
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x84},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0010, lo: 0x8b, hi: 0x8c},
	{value: 0x0030, lo: 0x8d, hi: 0x8d},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0010, lo: 0x9d, hi: 0xa3},
	{value: 0x0024, lo: 0xa6, hi: 0xac},
	{value: 0x0024, lo: 0xb0, hi: 0xb4},
	// Block 0xce, offset 0x46a
	{value: 0x0010, lo: 0x80, hi: 0xb7},
	{value: 0x0014, lo: 0xb8, hi: 0xbf},
	// Block 0xcf, offset 0x46c
	{value: 0x0010, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x84},
	{value: 0x0010, lo: 0x85, hi: 0x85},
	{value: 0x0034, lo: 0x86, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x8a},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xd0, offset 0x473
	{value: 0x0010, lo: 0x80, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb8},
	{value: 0x0010, lo: 0xb9, hi: 0xb9},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbe},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0xd1, offset 0x479
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x81, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x83},
	{value: 0x0010, lo: 0x84, hi: 0x85},
	{value: 0x0010, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xd2, offset 0x47f
	{value: 0x0010, lo: 0x80, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb5},
	{value: 0x0010, lo: 0xb8, hi: 0xbb},
	{value: 0x0014, lo: 0xbc, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xd3, offset 0x485
	{value: 0x0034, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x98, hi: 0x9b},
	{value: 0x0014, lo: 0x9c, hi: 0x9d},
	// Block 0xd4, offset 0x488
	{value: 0x0010, lo: 0x80, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xba},
	{value: 0x0010, lo: 0xbb, hi: 0xbc},
	{value: 0x0014, lo: 0xbd, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xd5, offset 0x48e
	{value: 0x0014, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x84, hi: 0x84},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xd6, offset 0x491
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0014, lo: 0xab, hi: 0xab},
	{value: 0x0010, lo: 0xac, hi: 0xac},
	{value: 0x0014, lo: 0xad, hi: 0xad},
	{value: 0x0010, lo: 0xae, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb5},
	{value: 0x0030, lo: 0xb6, hi: 0xb6},
	{value: 0x0034, lo: 0xb7, hi: 0xb7},
	// Block 0xd7, offset 0x499
	{value: 0x0010, lo: 0x80, hi: 0x89},
	// Block 0xd8, offset 0x49a
	{value: 0x0014, lo: 0x9d, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa1},
	{value: 0x0014, lo: 0xa2, hi: 0xa5},
	{value: 0x0010, lo: 0xa6, hi: 0xa6},
	{value: 0x0014, lo: 0xa7, hi: 0xaa},
	{value: 0x0034, lo: 0xab, hi: 0xab},
	{value: 0x0010, lo: 0xb0, hi: 0xb9},
	// Block 0xd9, offset 0x4a1
	{value: 0x5f53, lo: 0xa0, hi: 0xbf},
	// Block 0xda, offset 0x4a2
	{value: 0x5f52, lo: 0x80, hi: 0x9f},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	{value: 0x0010, lo: 0xbf, hi: 0xbf},
	// Block 0xdb, offset 0x4a5
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0014, lo: 0x81, hi: 0x86},
	{value: 0x0010, lo: 0x87, hi: 0x88},
	{value: 0x0014, lo: 0x89, hi: 0x8a},
	{value: 0x0010, lo: 0x8b, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xb3},
	{value: 0x0034, lo: 0xb4, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb8},
	{value: 0x0010, lo: 0xb9, hi: 0xba},
	{value: 0x0014, lo: 0xbb, hi: 0xbe},
	// Block 0xdc, offset 0x4af
	{value: 0x0034, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0x90},
	{value: 0x0014, lo: 0x91, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x98},
	{value: 0x0014, lo: 0x99, hi: 0x9b},
	{value: 0x0010, lo: 0x9c, hi: 0xbf},
	// Block 0xdd, offset 0x4b5
	{value: 0x0010, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x86, hi: 0x89},
	{value: 0x0014, lo: 0x8a, hi: 0x96},
	{value: 0x0010, lo: 0x97, hi: 0x97},
	{value: 0x0014, lo: 0x98, hi: 0x98},
	{value: 0x0034, lo: 0x99, hi: 0x99},
	// Block 0xde, offset 0x4bb
	{value: 0x0010, lo: 0x80, hi: 0xb8},
	// Block 0xdf, offset 0x4bc
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x8a, hi: 0xaf},
	{value: 0x0014, lo: 0xb0, hi: 0xb6},
	{value: 0x0014, lo: 0xb8, hi: 0xbd},
	{value: 0x0010, lo: 0xbe, hi: 0xbe},
	{value: 0x0034, lo: 0xbf, hi: 0xbf},
	// Block 0xe0, offset 0x4c2
	{value: 0x0010, lo: 0x80, hi: 0x80},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0010, lo: 0xb2, hi: 0xbf},
	// Block 0xe1, offset 0x4c5
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	{value: 0x0014, lo: 0x92, hi: 0xa7},
	{value: 0x0010, lo: 0xa9, hi: 0xa9},
	{value: 0x0014, lo: 0xaa, hi: 0xb0},
	{value: 0x0010, lo: 0xb1, hi: 0xb1},
	{value: 0x0014, lo: 0xb2, hi: 0xb3},
	{value: 0x0010, lo: 0xb4, hi: 0xb4},
	{value: 0x0014, lo: 0xb5, hi: 0xb6},
	// Block 0xe2, offset 0x4cd
	{value: 0x0010, lo: 0x80, hi: 0x86},
	{value: 0x0010, lo: 0x88, hi: 0x89},
	{value: 0x0010, lo: 0x8b, hi: 0xb0},
	{value: 0x0014, lo: 0xb1, hi: 0xb6},
	{value: 0x0014, lo: 0xba, hi: 0xba},
	{value: 0x0014, lo: 0xbc, hi: 0xbd},
	{value: 0x0014, lo: 0xbf, hi: 0xbf},
	// Block 0xe3, offset 0x4d4
	{value: 0x0014, lo: 0x80, hi: 0x81},
	{value: 0x0034, lo: 0x82, hi: 0x82},
	{value: 0x0014, lo: 0x83, hi: 0x83},
	{value: 0x0034, lo: 0x84, hi: 0x85},
	{value: 0x0010, lo: 0x86, hi: 0x86},
	{value: 0x0014, lo: 0x87, hi: 0x87},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0xe4, offset 0x4db
	{value: 0x0010, lo: 0x80, hi: 0x99},
	// Block 0xe5, offset 0x4dc
	{value: 0x0010, lo: 0x80, hi: 0xae},
	// Block 0xe6, offset 0x4dd
	{value: 0x0010, lo: 0x80, hi: 0x83},
	// Block 0xe7, offset 0x4de
	{value: 0x0010, lo: 0x80, hi: 0x86},
	// Block 0xe8, offset 0x4df
	{value: 0x0010, lo: 0x80, hi: 0x9e},
	{value: 0x0010, lo: 0xa0, hi: 0xa9},
	// Block 0xe9, offset 0x4e1
	{value: 0x0010, lo: 0x90, hi: 0xad},
	{value: 0x0034, lo: 0xb0, hi: 0xb4},
	// Block 0xea, offset 0x4e3
	{value: 0x0010, lo: 0x80, hi: 0xaf},
	{value: 0x0024, lo: 0xb0, hi: 0xb6},
	// Block 0xeb, offset 0x4e5
	{value: 0x0014, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0010, lo: 0xa3, hi: 0xb7},
	{value: 0x0010, lo: 0xbd, hi: 0xbf},
	// Block 0xec, offset 0x4e9
	{value: 0x0010, lo: 0x80, hi: 0x8f},
	// Block 0xed, offset 0x4ea
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0010, lo: 0x90, hi: 0xbe},
	// Block 0xee, offset 0x4ec
	{value: 0x0014, lo: 0x8f, hi: 0x9f},
	// Block 0xef, offset 0x4ed
	{value: 0x0014, lo: 0xa0, hi: 0xa1},
	// Block 0xf0, offset 0x4ee
	{value: 0x0010, lo: 0x80, hi: 0xaa},
	{value: 0x0010, lo: 0xb0, hi: 0xbc},
	// Block 0xf1, offset 0x4f0
	{value: 0x0010, lo: 0x80, hi: 0x88},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	{value: 0x0014, lo: 0x9d, hi: 0x9d},
	{value: 0x0034, lo: 0x9e, hi: 0x9e},
	{value: 0x0014, lo: 0xa0, hi: 0xa3},
	// Block 0xf2, offset 0x4f5
	{value: 0x0030, lo: 0xa5, hi: 0xa6},
	{value: 0x0034, lo: 0xa7, hi: 0xa9},
	{value: 0x0030, lo: 0xad, hi: 0xb2},
	{value: 0x0014, lo: 0xb3, hi: 0xba},
	{value: 0x0034, lo: 0xbb, hi: 0xbf},
	// Block 0xf3, offset 0x4fa
	{value: 0x0034, lo: 0x80, hi: 0x82},
	{value: 0x0024, lo: 0x85, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8b},
	{value: 0x0024, lo: 0xaa, hi: 0xad},
	// Block 0xf4, offset 0x4fe
	{value: 0x0024, lo: 0x82, hi: 0x84},
	// Block 0xf5, offset 0x4ff
	{value: 0x0013, lo: 0x80, hi: 0x99},
	{value: 0x0012, lo: 0x9a, hi: 0xb3},
	{value: 0x0013, lo: 0xb4, hi: 0xbf},
	// Block 0xf6, offset 0x502
	{value: 0x0013, lo: 0x80, hi: 0x8d},
	{value: 0x0012, lo: 0x8e, hi: 0x94},
	{value: 0x0012, lo: 0x96, hi: 0xa7},
	{value: 0x0013, lo: 0xa8, hi: 0xbf},
	// Block 0xf7, offset 0x506
	{value: 0x0013, lo: 0x80, hi: 0x81},
	{value: 0x0012, lo: 0x82, hi: 0x9b},
	{value: 0x0013, lo: 0x9c, hi: 0x9c},
	{value: 0x0013, lo: 0x9e, hi: 0x9f},
	{value: 0x0013, lo: 0xa2, hi: 0xa2},
	{value: 0x0013, lo: 0xa5, hi: 0xa6},
	{value: 0x0013, lo: 0xa9, hi: 0xac},
	{value: 0x0013, lo: 0xae, hi: 0xb5},
	{value: 0x0012, lo: 0xb6, hi: 0xb9},
	{value: 0x0012, lo: 0xbb, hi: 0xbb},
	{value: 0x0012, lo: 0xbd, hi: 0xbf},
	// Block 0xf8, offset 0x511
	{value: 0x0012, lo: 0x80, hi: 0x83},
	{value: 0x0012, lo: 0x85, hi: 0x8f},
	{value: 0x0013, lo: 0x90, hi: 0xa9},
	{value: 0x0012, lo: 0xaa, hi: 0xbf},
	// Block 0xf9, offset 0x515
	{value: 0x0012, lo: 0x80, hi: 0x83},
	{value: 0x0013, lo: 0x84, hi: 0x85},
	{value: 0x0013, lo: 0x87, hi: 0x8a},
	{value: 0x0013, lo: 0x8d, hi: 0x94},
	{value: 0x0013, lo: 0x96, hi: 0x9c},
	{value: 0x0012, lo: 0x9e, hi: 0xb7},
	{value: 0x0013, lo: 0xb8, hi: 0xb9},
	{value: 0x0013, lo: 0xbb, hi: 0xbe},
	// Block 0xfa, offset 0x51d
	{value: 0x0013, lo: 0x80, hi: 0x84},
	{value: 0x0013, lo: 0x86, hi: 0x86},
	{value: 0x0013, lo: 0x8a, hi: 0x90},
	{value: 0x0012, lo: 0x92, hi: 0xab},
	{value: 0x0013, lo: 0xac, hi: 0xbf},
	// Block 0xfb, offset 0x522
	{value: 0x0013, lo: 0x80, hi: 0x85},
	{value: 0x0012, lo: 0x86, hi: 0x9f},
	{value: 0x0013, lo: 0xa0, hi: 0xb9},
	{value: 0x0012, lo: 0xba, hi: 0xbf},
	// Block 0xfc, offset 0x526
	{value: 0x0012, lo: 0x80, hi: 0x93},
	{value: 0x0013, lo: 0x94, hi: 0xad},
	{value: 0x0012, lo: 0xae, hi: 0xbf},
	// Block 0xfd, offset 0x529
	{value: 0x0012, lo: 0x80, hi: 0x87},
	{value: 0x0013, lo: 0x88, hi: 0xa1},
	{value: 0x0012, lo: 0xa2, hi: 0xbb},
	{value: 0x0013, lo: 0xbc, hi: 0xbf},
	// Block 0xfe, offset 0x52d
	{value: 0x0013, lo: 0x80, hi: 0x95},
	{value: 0x0012, lo: 0x96, hi: 0xaf},
	{value: 0x0013, lo: 0xb0, hi: 0xbf},
	// Block 0xff, offset 0x530
	{value: 0x0013, lo: 0x80, hi: 0x89},
	{value: 0x0012, lo: 0x8a, hi: 0xa5},
	{value: 0x0013, lo: 0xa8, hi: 0xbf},
	// Block 0x100, offset 0x533
	{value: 0x0013, lo: 0x80, hi: 0x80},
	{value: 0x0012, lo: 0x82, hi: 0x9a},
	{value: 0x0012, lo: 0x9c, hi: 0xa1},
	{value: 0x0013, lo: 0xa2, hi: 0xba},
	{value: 0x0012, lo: 0xbc, hi: 0xbf},
	// Block 0x101, offset 0x538
	{value: 0x0012, lo: 0x80, hi: 0x94},
	{value: 0x0012, lo: 0x96, hi: 0x9b},
	{value: 0x0013, lo: 0x9c, hi: 0xb4},
	{value: 0x0012, lo: 0xb6, hi: 0xbf},
	// Block 0x102, offset 0x53c
	{value: 0x0012, lo: 0x80, hi: 0x8e},
	{value: 0x0012, lo: 0x90, hi: 0x95},
	{value: 0x0013, lo: 0x96, hi: 0xae},
	{value: 0x0012, lo: 0xb0, hi: 0xbf},
	// Block 0x103, offset 0x540
	{value: 0x0012, lo: 0x80, hi: 0x88},
	{value: 0x0012, lo: 0x8a, hi: 0x8f},
	{value: 0x0013, lo: 0x90, hi: 0xa8},
	{value: 0x0012, lo: 0xaa, hi: 0xbf},
	// Block 0x104, offset 0x544
	{value: 0x0012, lo: 0x80, hi: 0x82},
	{value: 0x0012, lo: 0x84, hi: 0x89},
	{value: 0x0017, lo: 0x8a, hi: 0x8b},
	{value: 0x0010, lo: 0x8e, hi: 0xbf},
	// Block 0x105, offset 0x548
	{value: 0x0014, lo: 0x80, hi: 0xb6},
	{value: 0x0014, lo: 0xbb, hi: 0xbf},
	// Block 0x106, offset 0x54a
	{value: 0x0014, lo: 0x80, hi: 0xac},
	{value: 0x0014, lo: 0xb5, hi: 0xb5},
	// Block 0x107, offset 0x54c
	{value: 0x0014, lo: 0x84, hi: 0x84},
	{value: 0x0014, lo: 0x9b, hi: 0x9f},
	{value: 0x0014, lo: 0xa1, hi: 0xaf},
	// Block 0x108, offset 0x54f
	{value: 0x0024, lo: 0x80, hi: 0x86},
	{value: 0x0024, lo: 0x88, hi: 0x98},
	{value: 0x0024, lo: 0x9b, hi: 0xa1},
	{value: 0x0024, lo: 0xa3, hi: 0xa4},
	{value: 0x0024, lo: 0xa6, hi: 0xaa},
	// Block 0x109, offset 0x554
	{value: 0x0010, lo: 0x80, hi: 0x84},
	{value: 0x0034, lo: 0x90, hi: 0x96},
	// Block 0x10a, offset 0x556
	{value: 0xb552, lo: 0x80, hi: 0x81},
	{value: 0xb852, lo: 0x82, hi: 0x83},
	{value: 0x0024, lo: 0x84, hi: 0x89},
	{value: 0x0034, lo: 0x8a, hi: 0x8a},
	{value: 0x0010, lo: 0x90, hi: 0x99},
	// Block 0x10b, offset 0x55b
	{value: 0x0010, lo: 0x80, hi: 0x83},
	{value: 0x0010, lo: 0x85, hi: 0x9f},
	{value: 0x0010, lo: 0xa1, hi: 0xa2},
	{value: 0x0010, lo: 0xa4, hi: 0xa4},
	{value: 0x0010, lo: 0xa7, hi: 0xa7},
	{value: 0x0010, lo: 0xa9, hi: 0xb2},
	{value: 0x0010, lo: 0xb4, hi: 0xb7},
	{value: 0x0010, lo: 0xb9, hi: 0xb9},
	{value: 0x0010, lo: 0xbb, hi: 0xbb},
	// Block 0x10c, offset 0x564
	{value: 0x0010, lo: 0x80, hi: 0x89},
	{value: 0x0010, lo: 0x8b, hi: 0x9b},
	{value: 0x0010, lo: 0xa1, hi: 0xa3},
	{value: 0x0010, lo: 0xa5, hi: 0xa9},
	{value: 0x0010, lo: 0xab, hi: 0xbb},
	// Block 0x10d, offset 0x569
	{value: 0x0013, lo: 0xb0, hi: 0xbf},
	// Block 0x10e, offset 0x56a
	{value: 0x0013, lo: 0x80, hi: 0x89},
	{value: 0x0013, lo: 0x90, hi: 0xa9},
	{value: 0x0013, lo: 0xb0, hi: 0xbf},
	// Block 0x10f, offset 0x56d
	{value: 0x0013, lo: 0x80, hi: 0x89},
	// Block 0x110, offset 0x56e
	{value: 0x0004, lo: 0xbb, hi: 0xbf},
	// Block 0x111, offset 0x56f
	{value: 0x0014, lo: 0x81, hi: 0x81},
	{value: 0x0014, lo: 0xa0, hi: 0xbf},
	// Block 0x112, offset 0x571
	{value: 0x0014, lo: 0x80, hi: 0xbf},
	// Block 0x113, offset 0x572
	{value: 0x0014, lo: 0x80, hi: 0xaf},
}

// Total table size 14177 bytes (13KiB); checksum: F17D40E8
