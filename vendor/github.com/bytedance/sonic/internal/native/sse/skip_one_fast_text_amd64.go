// +build amd64
// Code generated by asm2asm, DO NOT EDIT.

package sse

var _text_skip_one_fast = []byte{
	// .p2align 4, 0x00
	// LCPI0_0
	0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, 0x2c, // QUAD $0x2c2c2c2c2c2c2c2c; QUAD $0x2c2c2c2c2c2c2c2c  // .space 16, ',,,,,,,,,,,,,,,,'
	//0x00000010 LCPI0_1
	0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, 0xdf, //0x00000010 QUAD $0xdfdfdfdfdfdfdfdf; QUAD $0xdfdfdfdfdfdfdfdf  // .space 16, '\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf\xdf'
	//0x00000020 LCPI0_2
	0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, 0x5d, //0x00000020 QUAD $0x5d5d5d5d5d5d5d5d; QUAD $0x5d5d5d5d5d5d5d5d  // .space 16, ']]]]]]]]]]]]]]]]'
	//0x00000030 LCPI0_3
	0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, //0x00000030 QUAD $0x2222222222222222; QUAD $0x2222222222222222  // .space 16, '""""""""""""""""'
	//0x00000040 LCPI0_4
	0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, //0x00000040 QUAD $0x5c5c5c5c5c5c5c5c; QUAD $0x5c5c5c5c5c5c5c5c  // .space 16, '\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'
	//0x00000050 LCPI0_5
	0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, //0x00000050 QUAD $0x7b7b7b7b7b7b7b7b; QUAD $0x7b7b7b7b7b7b7b7b  // .space 16, '{{{{{{{{{{{{{{{{'
	//0x00000060 LCPI0_6
	0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, //0x00000060 QUAD $0x7d7d7d7d7d7d7d7d; QUAD $0x7d7d7d7d7d7d7d7d  // .space 16, '}}}}}}}}}}}}}}}}'
	//0x00000070 LCPI0_7
	0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, 0x5b, //0x00000070 QUAD $0x5b5b5b5b5b5b5b5b; QUAD $0x5b5b5b5b5b5b5b5b  // .space 16, '[[[[[[[[[[[[[[[['
	//0x00000080 .p2align 4, 0x90
	//0x00000080 _skip_one_fast
	0x55, //0x00000080 pushq        %rbp
	0x48, 0x89, 0xe5, //0x00000081 movq         %rsp, %rbp
	0x41, 0x57, //0x00000084 pushq        %r15
	0x41, 0x56, //0x00000086 pushq        %r14
	0x41, 0x55, //0x00000088 pushq        %r13
	0x41, 0x54, //0x0000008a pushq        %r12
	0x53, //0x0000008c pushq        %rbx
	0x48, 0x83, 0xec, 0x58, //0x0000008d subq         $88, %rsp
	0x4c, 0x8b, 0x0f, //0x00000091 movq         (%rdi), %r9
	0x48, 0x8b, 0x57, 0x08, //0x00000094 movq         $8(%rdi), %rdx
	0x48, 0x8b, 0x0e, //0x00000098 movq         (%rsi), %rcx
	0x48, 0x39, 0xd1, //0x0000009b cmpq         %rdx, %rcx
	0x0f, 0x83, 0x26, 0x00, 0x00, 0x00, //0x0000009e jae          LBB0_5
	0x41, 0x8a, 0x04, 0x09, //0x000000a4 movb         (%r9,%rcx), %al
	0x3c, 0x0d, //0x000000a8 cmpb         $13, %al
	0x0f, 0x84, 0x1a, 0x00, 0x00, 0x00, //0x000000aa je           LBB0_5
	0x3c, 0x20, //0x000000b0 cmpb         $32, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000000b2 je           LBB0_5
	0x04, 0xf7, //0x000000b8 addb         $-9, %al
	0x3c, 0x01, //0x000000ba cmpb         $1, %al
	0x0f, 0x86, 0x08, 0x00, 0x00, 0x00, //0x000000bc jbe          LBB0_5
	0x49, 0x89, 0xcc, //0x000000c2 movq         %rcx, %r12
	0xe9, 0xfe, 0x00, 0x00, 0x00, //0x000000c5 jmp          LBB0_27
	//0x000000ca LBB0_5
	0x4c, 0x8d, 0x61, 0x01, //0x000000ca leaq         $1(%rcx), %r12
	0x49, 0x39, 0xd4, //0x000000ce cmpq         %rdx, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x000000d1 jae          LBB0_9
	0x43, 0x8a, 0x04, 0x21, //0x000000d7 movb         (%r9,%r12), %al
	0x3c, 0x0d, //0x000000db cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x000000dd je           LBB0_9
	0x3c, 0x20, //0x000000e3 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x000000e5 je           LBB0_9
	0x04, 0xf7, //0x000000eb addb         $-9, %al
	0x3c, 0x01, //0x000000ed cmpb         $1, %al
	0x0f, 0x87, 0xd3, 0x00, 0x00, 0x00, //0x000000ef ja           LBB0_27
	//0x000000f5 LBB0_9
	0x4c, 0x8d, 0x61, 0x02, //0x000000f5 leaq         $2(%rcx), %r12
	0x49, 0x39, 0xd4, //0x000000f9 cmpq         %rdx, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x000000fc jae          LBB0_13
	0x43, 0x8a, 0x04, 0x21, //0x00000102 movb         (%r9,%r12), %al
	0x3c, 0x0d, //0x00000106 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000108 je           LBB0_13
	0x3c, 0x20, //0x0000010e cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x00000110 je           LBB0_13
	0x04, 0xf7, //0x00000116 addb         $-9, %al
	0x3c, 0x01, //0x00000118 cmpb         $1, %al
	0x0f, 0x87, 0xa8, 0x00, 0x00, 0x00, //0x0000011a ja           LBB0_27
	//0x00000120 LBB0_13
	0x4c, 0x8d, 0x61, 0x03, //0x00000120 leaq         $3(%rcx), %r12
	0x49, 0x39, 0xd4, //0x00000124 cmpq         %rdx, %r12
	0x0f, 0x83, 0x1e, 0x00, 0x00, 0x00, //0x00000127 jae          LBB0_17
	0x43, 0x8a, 0x04, 0x21, //0x0000012d movb         (%r9,%r12), %al
	0x3c, 0x0d, //0x00000131 cmpb         $13, %al
	0x0f, 0x84, 0x12, 0x00, 0x00, 0x00, //0x00000133 je           LBB0_17
	0x3c, 0x20, //0x00000139 cmpb         $32, %al
	0x0f, 0x84, 0x0a, 0x00, 0x00, 0x00, //0x0000013b je           LBB0_17
	0x04, 0xf7, //0x00000141 addb         $-9, %al
	0x3c, 0x01, //0x00000143 cmpb         $1, %al
	0x0f, 0x87, 0x7d, 0x00, 0x00, 0x00, //0x00000145 ja           LBB0_27
	//0x0000014b LBB0_17
	0x48, 0x83, 0xc1, 0x04, //0x0000014b addq         $4, %rcx
	0x48, 0x39, 0xca, //0x0000014f cmpq         %rcx, %rdx
	0x0f, 0x86, 0x42, 0x00, 0x00, 0x00, //0x00000152 jbe          LBB0_23
	0x48, 0x39, 0xca, //0x00000158 cmpq         %rcx, %rdx
	0x0f, 0x84, 0x48, 0x00, 0x00, 0x00, //0x0000015b je           LBB0_24
	0x4d, 0x8d, 0x04, 0x11, //0x00000161 leaq         (%r9,%rdx), %r8
	0x48, 0xbb, 0x00, 0x26, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, //0x00000165 movabsq      $4294977024, %rbx
	0x90, //0x0000016f .p2align 4, 0x90
	//0x00000170 LBB0_20
	0x41, 0x0f, 0xbe, 0x04, 0x09, //0x00000170 movsbl       (%r9,%rcx), %eax
	0x83, 0xf8, 0x20, //0x00000175 cmpl         $32, %eax
	0x0f, 0x87, 0x37, 0x00, 0x00, 0x00, //0x00000178 ja           LBB0_26
	0x48, 0x0f, 0xa3, 0xc3, //0x0000017e btq          %rax, %rbx
	0x0f, 0x83, 0x2d, 0x00, 0x00, 0x00, //0x00000182 jae          LBB0_26
	0x48, 0x83, 0xc1, 0x01, //0x00000188 addq         $1, %rcx
	0x48, 0x39, 0xca, //0x0000018c cmpq         %rcx, %rdx
	0x0f, 0x85, 0xdb, 0xff, 0xff, 0xff, //0x0000018f jne          LBB0_20
	0xe9, 0x15, 0x00, 0x00, 0x00, //0x00000195 jmp          LBB0_25
	//0x0000019a LBB0_23
	0x48, 0x89, 0x0e, //0x0000019a movq         %rcx, (%rsi)
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x0000019d movq         $-1, %r14
	0xe9, 0x2c, 0x01, 0x00, 0x00, //0x000001a4 jmp          LBB0_44
	//0x000001a9 LBB0_24
	0x4c, 0x01, 0xc9, //0x000001a9 addq         %r9, %rcx
	0x49, 0x89, 0xc8, //0x000001ac movq         %rcx, %r8
	//0x000001af LBB0_25
	0x4d, 0x29, 0xc8, //0x000001af subq         %r9, %r8
	0x4c, 0x89, 0xc1, //0x000001b2 movq         %r8, %rcx
	//0x000001b5 LBB0_26
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000001b5 movq         $-1, %r14
	0x49, 0x89, 0xcc, //0x000001bc movq         %rcx, %r12
	0x48, 0x39, 0xd1, //0x000001bf cmpq         %rdx, %rcx
	0x0f, 0x83, 0x0d, 0x01, 0x00, 0x00, //0x000001c2 jae          LBB0_44
	//0x000001c8 LBB0_27
	0x49, 0x8d, 0x54, 0x24, 0x01, //0x000001c8 leaq         $1(%r12), %rdx
	0x48, 0x89, 0x16, //0x000001cd movq         %rdx, (%rsi)
	0x43, 0x0f, 0xbe, 0x04, 0x21, //0x000001d0 movsbl       (%r9,%r12), %eax
	0x83, 0xf8, 0x7b, //0x000001d5 cmpl         $123, %eax
	0x0f, 0x87, 0x1d, 0x01, 0x00, 0x00, //0x000001d8 ja           LBB0_46
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x000001de movq         $-1, %r14
	0x48, 0x8d, 0x0d, 0xa8, 0x0b, 0x00, 0x00, //0x000001e5 leaq         $2984(%rip), %rcx  /* LJTI0_0+0(%rip) */
	0x48, 0x63, 0x04, 0x81, //0x000001ec movslq       (%rcx,%rax,4), %rax
	0x48, 0x01, 0xc8, //0x000001f0 addq         %rcx, %rax
	0xff, 0xe0, //0x000001f3 jmpq         *%rax
	//0x000001f5 LBB0_29
	0x48, 0x8b, 0x47, 0x08, //0x000001f5 movq         $8(%rdi), %rax
	0x48, 0x89, 0xc1, //0x000001f9 movq         %rax, %rcx
	0x48, 0x29, 0xd1, //0x000001fc subq         %rdx, %rcx
	0x48, 0x83, 0xf9, 0x10, //0x000001ff cmpq         $16, %rcx
	0x0f, 0x82, 0x40, 0x0b, 0x00, 0x00, //0x00000203 jb           LBB0_117
	0x4c, 0x89, 0xe1, //0x00000209 movq         %r12, %rcx
	0x48, 0xf7, 0xd1, //0x0000020c notq         %rcx
	0xf3, 0x0f, 0x6f, 0x05, 0xe9, 0xfd, 0xff, 0xff, //0x0000020f movdqu       $-535(%rip), %xmm0  /* LCPI0_0+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xf1, 0xfd, 0xff, 0xff, //0x00000217 movdqu       $-527(%rip), %xmm1  /* LCPI0_1+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x15, 0xf9, 0xfd, 0xff, 0xff, //0x0000021f movdqu       $-519(%rip), %xmm2  /* LCPI0_2+0(%rip) */
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000227 .p2align 4, 0x90
	//0x00000230 LBB0_31
	0xf3, 0x41, 0x0f, 0x6f, 0x1c, 0x11, //0x00000230 movdqu       (%r9,%rdx), %xmm3
	0x66, 0x0f, 0x6f, 0xe3, //0x00000236 movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x0000023a pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xdb, 0xd9, //0x0000023e pand         %xmm1, %xmm3
	0x66, 0x0f, 0x74, 0xda, //0x00000242 pcmpeqb      %xmm2, %xmm3
	0x66, 0x0f, 0xeb, 0xdc, //0x00000246 por          %xmm4, %xmm3
	0x66, 0x0f, 0xd7, 0xfb, //0x0000024a pmovmskb     %xmm3, %edi
	0x85, 0xff, //0x0000024e testl        %edi, %edi
	0x0f, 0x85, 0x6f, 0x00, 0x00, 0x00, //0x00000250 jne          LBB0_41
	0x48, 0x83, 0xc2, 0x10, //0x00000256 addq         $16, %rdx
	0x48, 0x8d, 0x3c, 0x08, //0x0000025a leaq         (%rax,%rcx), %rdi
	0x48, 0x83, 0xc7, 0xf0, //0x0000025e addq         $-16, %rdi
	0x48, 0x83, 0xc1, 0xf0, //0x00000262 addq         $-16, %rcx
	0x48, 0x83, 0xff, 0x0f, //0x00000266 cmpq         $15, %rdi
	0x0f, 0x87, 0xc0, 0xff, 0xff, 0xff, //0x0000026a ja           LBB0_31
	0x4c, 0x89, 0xca, //0x00000270 movq         %r9, %rdx
	0x48, 0x29, 0xca, //0x00000273 subq         %rcx, %rdx
	0x48, 0x01, 0xc8, //0x00000276 addq         %rcx, %rax
	0x48, 0x89, 0xc1, //0x00000279 movq         %rax, %rcx
	0x48, 0x85, 0xc9, //0x0000027c testq        %rcx, %rcx
	0x0f, 0x84, 0x35, 0x00, 0x00, 0x00, //0x0000027f je           LBB0_40
	//0x00000285 LBB0_34
	0x48, 0x8d, 0x3c, 0x0a, //0x00000285 leaq         (%rdx,%rcx), %rdi
	0x31, 0xc0, //0x00000289 xorl         %eax, %eax
	//0x0000028b LBB0_35
	0x0f, 0xb6, 0x1c, 0x02, //0x0000028b movzbl       (%rdx,%rax), %ebx
	0x80, 0xfb, 0x2c, //0x0000028f cmpb         $44, %bl
	0x0f, 0x84, 0x94, 0x0a, 0x00, 0x00, //0x00000292 je           LBB0_115
	0x80, 0xfb, 0x7d, //0x00000298 cmpb         $125, %bl
	0x0f, 0x84, 0x8b, 0x0a, 0x00, 0x00, //0x0000029b je           LBB0_115
	0x80, 0xfb, 0x5d, //0x000002a1 cmpb         $93, %bl
	0x0f, 0x84, 0x82, 0x0a, 0x00, 0x00, //0x000002a4 je           LBB0_115
	0x48, 0x83, 0xc0, 0x01, //0x000002aa addq         $1, %rax
	0x48, 0x39, 0xc1, //0x000002ae cmpq         %rax, %rcx
	0x0f, 0x85, 0xd4, 0xff, 0xff, 0xff, //0x000002b1 jne          LBB0_35
	0x48, 0x89, 0xfa, //0x000002b7 movq         %rdi, %rdx
	//0x000002ba LBB0_40
	0x4c, 0x29, 0xca, //0x000002ba subq         %r9, %rdx
	0x48, 0x89, 0x16, //0x000002bd movq         %rdx, (%rsi)
	0xe9, 0x0d, 0x00, 0x00, 0x00, //0x000002c0 jmp          LBB0_43
	//0x000002c5 LBB0_41
	0x66, 0x0f, 0xbc, 0xc7, //0x000002c5 bsfw         %di, %ax
	0x0f, 0xb7, 0xc0, //0x000002c9 movzwl       %ax, %eax
	0x48, 0x29, 0xc8, //0x000002cc subq         %rcx, %rax
	//0x000002cf LBB0_42
	0x48, 0x89, 0x06, //0x000002cf movq         %rax, (%rsi)
	//0x000002d2 LBB0_43
	0x4d, 0x89, 0xe6, //0x000002d2 movq         %r12, %r14
	//0x000002d5 LBB0_44
	0x4c, 0x89, 0xf0, //0x000002d5 movq         %r14, %rax
	0x48, 0x83, 0xc4, 0x58, //0x000002d8 addq         $88, %rsp
	0x5b, //0x000002dc popq         %rbx
	0x41, 0x5c, //0x000002dd popq         %r12
	0x41, 0x5d, //0x000002df popq         %r13
	0x41, 0x5e, //0x000002e1 popq         %r14
	0x41, 0x5f, //0x000002e3 popq         %r15
	0x5d, //0x000002e5 popq         %rbp
	0xc3, //0x000002e6 retq         
	//0x000002e7 LBB0_45
	0x49, 0x8d, 0x44, 0x24, 0x04, //0x000002e7 leaq         $4(%r12), %rax
	0x48, 0x3b, 0x47, 0x08, //0x000002ec cmpq         $8(%rdi), %rax
	0x0f, 0x86, 0xd9, 0xff, 0xff, 0xff, //0x000002f0 jbe          LBB0_42
	0xe9, 0xda, 0xff, 0xff, 0xff, //0x000002f6 jmp          LBB0_44
	//0x000002fb LBB0_46
	0x4c, 0x89, 0x26, //0x000002fb movq         %r12, (%rsi)
	0x49, 0xc7, 0xc6, 0xfe, 0xff, 0xff, 0xff, //0x000002fe movq         $-2, %r14
	0xe9, 0xcb, 0xff, 0xff, 0xff, //0x00000305 jmp          LBB0_44
	//0x0000030a LBB0_47
	0x4c, 0x8b, 0x47, 0x08, //0x0000030a movq         $8(%rdi), %r8
	0x4d, 0x89, 0xc7, //0x0000030e movq         %r8, %r15
	0x49, 0x29, 0xd7, //0x00000311 subq         %rdx, %r15
	0x49, 0x83, 0xff, 0x20, //0x00000314 cmpq         $32, %r15
	0x0f, 0x8c, 0x3c, 0x0a, 0x00, 0x00, //0x00000318 jl           LBB0_118
	0x4f, 0x8d, 0x14, 0x21, //0x0000031e leaq         (%r9,%r12), %r10
	0x4d, 0x29, 0xe0, //0x00000322 subq         %r12, %r8
	0x41, 0xbd, 0x1f, 0x00, 0x00, 0x00, //0x00000325 movl         $31, %r13d
	0x45, 0x31, 0xff, //0x0000032b xorl         %r15d, %r15d
	0xf3, 0x0f, 0x6f, 0x05, 0xfa, 0xfc, 0xff, 0xff, //0x0000032e movdqu       $-774(%rip), %xmm0  /* LCPI0_3+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x02, 0xfd, 0xff, 0xff, //0x00000336 movdqu       $-766(%rip), %xmm1  /* LCPI0_4+0(%rip) */
	0x45, 0x31, 0xdb, //0x0000033e xorl         %r11d, %r11d
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x00000341 .p2align 4, 0x90
	//0x00000350 LBB0_49
	0xf3, 0x43, 0x0f, 0x6f, 0x54, 0x3a, 0x01, //0x00000350 movdqu       $1(%r10,%r15), %xmm2
	0xf3, 0x43, 0x0f, 0x6f, 0x5c, 0x3a, 0x11, //0x00000357 movdqu       $17(%r10,%r15), %xmm3
	0x66, 0x0f, 0x6f, 0xe2, //0x0000035e movdqa       %xmm2, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x00000362 pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xcc, //0x00000366 pmovmskb     %xmm4, %ecx
	0x66, 0x0f, 0x6f, 0xe3, //0x0000036a movdqa       %xmm3, %xmm4
	0x66, 0x0f, 0x74, 0xe0, //0x0000036e pcmpeqb      %xmm0, %xmm4
	0x66, 0x0f, 0xd7, 0xc4, //0x00000372 pmovmskb     %xmm4, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x00000376 shlq         $16, %rax
	0x48, 0x09, 0xc8, //0x0000037a orq          %rcx, %rax
	0x66, 0x0f, 0x74, 0xd1, //0x0000037d pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000381 pmovmskb     %xmm2, %ebx
	0x66, 0x0f, 0x74, 0xd9, //0x00000385 pcmpeqb      %xmm1, %xmm3
	0x66, 0x0f, 0xd7, 0xcb, //0x00000389 pmovmskb     %xmm3, %ecx
	0x48, 0xc1, 0xe1, 0x10, //0x0000038d shlq         $16, %rcx
	0x48, 0x09, 0xd9, //0x00000391 orq          %rbx, %rcx
	0x48, 0x89, 0xcb, //0x00000394 movq         %rcx, %rbx
	0x4c, 0x09, 0xdb, //0x00000397 orq          %r11, %rbx
	0x0f, 0x84, 0x40, 0x00, 0x00, 0x00, //0x0000039a je           LBB0_51
	0x44, 0x89, 0xdb, //0x000003a0 movl         %r11d, %ebx
	0xf7, 0xd3, //0x000003a3 notl         %ebx
	0x21, 0xcb, //0x000003a5 andl         %ecx, %ebx
	0x8d, 0x14, 0x1b, //0x000003a7 leal         (%rbx,%rbx), %edx
	0x44, 0x09, 0xda, //0x000003aa orl          %r11d, %edx
	0x89, 0xd7, //0x000003ad movl         %edx, %edi
	0xf7, 0xd7, //0x000003af notl         %edi
	0x21, 0xcf, //0x000003b1 andl         %ecx, %edi
	0x81, 0xe7, 0xaa, 0xaa, 0xaa, 0xaa, //0x000003b3 andl         $-1431655766, %edi
	0x45, 0x31, 0xdb, //0x000003b9 xorl         %r11d, %r11d
	0x01, 0xdf, //0x000003bc addl         %ebx, %edi
	0x41, 0x0f, 0x92, 0xc3, //0x000003be setb         %r11b
	0x01, 0xff, //0x000003c2 addl         %edi, %edi
	0x81, 0xf7, 0x55, 0x55, 0x55, 0x55, //0x000003c4 xorl         $1431655765, %edi
	0x21, 0xd7, //0x000003ca andl         %edx, %edi
	0xf7, 0xd7, //0x000003cc notl         %edi
	0x21, 0xf8, //0x000003ce andl         %edi, %eax
	0xe9, 0x0e, 0x00, 0x00, 0x00, //0x000003d0 jmp          LBB0_52
	0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, 0x90, //0x000003d5 .p2align 4, 0x90
	//0x000003e0 LBB0_51
	0x45, 0x31, 0xdb, //0x000003e0 xorl         %r11d, %r11d
	//0x000003e3 LBB0_52
	0x48, 0x85, 0xc0, //0x000003e3 testq        %rax, %rax
	0x0f, 0x85, 0xe6, 0x08, 0x00, 0x00, //0x000003e6 jne          LBB0_110
	0x49, 0x83, 0xc7, 0x20, //0x000003ec addq         $32, %r15
	0x4b, 0x8d, 0x04, 0x28, //0x000003f0 leaq         (%r8,%r13), %rax
	0x48, 0x83, 0xc0, 0xe0, //0x000003f4 addq         $-32, %rax
	0x49, 0x83, 0xc5, 0xe0, //0x000003f8 addq         $-32, %r13
	0x48, 0x83, 0xf8, 0x3f, //0x000003fc cmpq         $63, %rax
	0x0f, 0x8f, 0x4a, 0xff, 0xff, 0xff, //0x00000400 jg           LBB0_49
	0x4d, 0x85, 0xdb, //0x00000406 testq        %r11, %r11
	0x0f, 0x85, 0x5f, 0x09, 0x00, 0x00, //0x00000409 jne          LBB0_120
	0x4b, 0x8d, 0x14, 0x17, //0x0000040f leaq         (%r15,%r10), %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00000413 addq         $1, %rdx
	0x49, 0xf7, 0xd7, //0x00000417 notq         %r15
	0x4d, 0x01, 0xc7, //0x0000041a addq         %r8, %r15
	//0x0000041d LBB0_56
	0x4d, 0x85, 0xff, //0x0000041d testq        %r15, %r15
	0x0f, 0x8e, 0xaf, 0xfe, 0xff, 0xff, //0x00000420 jle          LBB0_44
	0x49, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, //0x00000426 movq         $-1, %r14
	0xe9, 0xca, 0x08, 0x00, 0x00, //0x0000042d jmp          LBB0_112
	//0x00000432 LBB0_58
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000432 movabsq      $6148914691236517205, %r10
	0x48, 0x8b, 0x47, 0x08, //0x0000043c movq         $8(%rdi), %rax
	0x48, 0x29, 0xd0, //0x00000440 subq         %rdx, %rax
	0x49, 0x01, 0xd1, //0x00000443 addq         %rdx, %r9
	0x45, 0x31, 0xed, //0x00000446 xorl         %r13d, %r13d
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xee, 0xfb, 0xff, 0xff, //0x00000449 movdqu       $-1042(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0xd6, 0xfb, 0xff, 0xff, //0x00000452 movdqu       $-1066(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x0000045a pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0x09, 0xfc, 0xff, 0xff, //0x0000045f movdqu       $-1015(%rip), %xmm3  /* LCPI0_7+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xb1, 0xfb, 0xff, 0xff, //0x00000467 movdqu       $-1103(%rip), %xmm4  /* LCPI0_2+0(%rip) */
	0x45, 0x0f, 0x57, 0xc0, //0x0000046f xorps        %xmm8, %xmm8
	0x45, 0x31, 0xff, //0x00000473 xorl         %r15d, %r15d
	0x31, 0xc9, //0x00000476 xorl         %ecx, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x00000478 movq         %rcx, $-64(%rbp)
	0x45, 0x31, 0xdb, //0x0000047c xorl         %r11d, %r11d
	0xe9, 0x60, 0x00, 0x00, 0x00, //0x0000047f jmp          LBB0_60
	//0x00000484 LBB0_59
	0x49, 0xc1, 0xff, 0x3f, //0x00000484 sarq         $63, %r15
	0x4c, 0x89, 0xc0, //0x00000488 movq         %r8, %rax
	0x48, 0xd1, 0xe8, //0x0000048b shrq         %rax
	0x4c, 0x21, 0xd0, //0x0000048e andq         %r10, %rax
	0x49, 0x29, 0xc0, //0x00000491 subq         %rax, %r8
	0x4c, 0x89, 0xc0, //0x00000494 movq         %r8, %rax
	0x4c, 0x21, 0xe8, //0x00000497 andq         %r13, %rax
	0x49, 0xc1, 0xe8, 0x02, //0x0000049a shrq         $2, %r8
	0x4d, 0x21, 0xe8, //0x0000049e andq         %r13, %r8
	0x49, 0x01, 0xc0, //0x000004a1 addq         %rax, %r8
	0x4c, 0x89, 0xc0, //0x000004a4 movq         %r8, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x000004a7 shrq         $4, %rax
	0x4c, 0x01, 0xc0, //0x000004ab addq         %r8, %rax
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000004ae movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xc8, //0x000004b8 andq         %rcx, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000004bb movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x000004c5 imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x000004c9 shrq         $56, %rax
	0x48, 0x01, 0x45, 0xc0, //0x000004cd addq         %rax, $-64(%rbp)
	0x49, 0x83, 0xc1, 0x40, //0x000004d1 addq         $64, %r9
	0x48, 0x8b, 0x45, 0xd0, //0x000004d5 movq         $-48(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x000004d9 addq         $-64, %rax
	0x4d, 0x89, 0xfd, //0x000004dd movq         %r15, %r13
	0x4c, 0x8b, 0x7d, 0xc8, //0x000004e0 movq         $-56(%rbp), %r15
	//0x000004e4 LBB0_60
	0x48, 0x83, 0xf8, 0x40, //0x000004e4 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xd0, //0x000004e8 movq         %rax, $-48(%rbp)
	0x0f, 0x8c, 0x2c, 0x02, 0x00, 0x00, //0x000004ec jl           LBB0_67
	//0x000004f2 LBB0_61
	0xf3, 0x41, 0x0f, 0x6f, 0x01, //0x000004f2 movdqu       (%r9), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x69, 0x10, //0x000004f7 movdqu       $16(%r9), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x79, 0x20, //0x000004fd movdqu       $32(%r9), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x71, 0x30, //0x00000503 movdqu       $48(%r9), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00000509 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x0000050d pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00000512 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd5, //0x00000516 movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x0000051a pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x0000051f pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00000523 movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000527 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x0000052c pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd6, //0x00000530 movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000534 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000539 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000053d shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x00000541 shlq         $32, %rcx
	0x48, 0x09, 0xd9, //0x00000545 orq          %rbx, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00000548 shlq         $16, %rax
	0x48, 0x09, 0xc8, //0x0000054c orq          %rcx, %rax
	0x48, 0x09, 0xc2, //0x0000054f orq          %rax, %rdx
	0x48, 0x89, 0xd0, //0x00000552 movq         %rdx, %rax
	0x4c, 0x09, 0xf8, //0x00000555 orq          %r15, %rax
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00000558 jne          LBB0_63
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000055e movq         $-1, %rdx
	0x31, 0xc0, //0x00000565 xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xc8, //0x00000567 movq         %rax, $-56(%rbp)
	0xe9, 0x3e, 0x00, 0x00, 0x00, //0x0000056b jmp          LBB0_64
	//0x00000570 LBB0_63
	0x4c, 0x89, 0xf8, //0x00000570 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00000573 notq         %rax
	0x48, 0x21, 0xd0, //0x00000576 andq         %rdx, %rax
	0x4c, 0x8d, 0x04, 0x00, //0x00000579 leaq         (%rax,%rax), %r8
	0x4d, 0x09, 0xf8, //0x0000057d orq          %r15, %r8
	0x4c, 0x89, 0xc1, //0x00000580 movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x00000583 notq         %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x00000586 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xda, //0x00000590 andq         %rbx, %rdx
	0x48, 0x21, 0xca, //0x00000593 andq         %rcx, %rdx
	0x31, 0xc9, //0x00000596 xorl         %ecx, %ecx
	0x48, 0x01, 0xc2, //0x00000598 addq         %rax, %rdx
	0x0f, 0x92, 0xc1, //0x0000059b setb         %cl
	0x48, 0x89, 0x4d, 0xc8, //0x0000059e movq         %rcx, $-56(%rbp)
	0x48, 0x01, 0xd2, //0x000005a2 addq         %rdx, %rdx
	0x4c, 0x31, 0xd2, //0x000005a5 xorq         %r10, %rdx
	0x4c, 0x21, 0xc2, //0x000005a8 andq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x000005ab notq         %rdx
	//0x000005ae LBB0_64
	0x66, 0x0f, 0x6f, 0xd6, //0x000005ae movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000005b2 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000005b6 pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x000005ba shlq         $48, %rax
	0x66, 0x0f, 0x6f, 0xd7, //0x000005be movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000005c2 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x000005c6 pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x000005ca shlq         $32, %rcx
	0x48, 0x09, 0xc1, //0x000005ce orq          %rax, %rcx
	0x66, 0x0f, 0x6f, 0xd5, //0x000005d1 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000005d5 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000005d9 pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x000005dd shlq         $16, %rax
	0x48, 0x09, 0xc8, //0x000005e1 orq          %rcx, %rax
	0x66, 0x0f, 0x6f, 0xd0, //0x000005e4 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000005e8 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x000005ec pmovmskb     %xmm2, %ecx
	0x48, 0x09, 0xc1, //0x000005f0 orq          %rax, %rcx
	0x48, 0x21, 0xd1, //0x000005f3 andq         %rdx, %rcx
	0x66, 0x48, 0x0f, 0x6e, 0xd1, //0x000005f6 movq         %rcx, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x000005fb pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd7, //0x00000602 movq         %xmm2, %r15
	0x4d, 0x31, 0xef, //0x00000607 xorq         %r13, %r15
	0x66, 0x0f, 0x6f, 0xd0, //0x0000060a movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x0000060e pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x00000612 pmovmskb     %xmm2, %r8d
	0x66, 0x0f, 0x6f, 0xd5, //0x00000617 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x0000061b pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x0000061f pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00000623 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000627 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x0000062b pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd6, //0x0000062f movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000633 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000637 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000063b shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x0000063f shlq         $32, %rcx
	0x48, 0x09, 0xd9, //0x00000643 orq          %rbx, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00000646 shlq         $16, %rax
	0x48, 0x09, 0xc8, //0x0000064a orq          %rcx, %rax
	0x49, 0x09, 0xc0, //0x0000064d orq          %rax, %r8
	0x4c, 0x89, 0xf8, //0x00000650 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00000653 notq         %rax
	0x49, 0x21, 0xc0, //0x00000656 andq         %rax, %r8
	0x66, 0x0f, 0x74, 0xc4, //0x00000659 pcmpeqb      %xmm4, %xmm0
	0x66, 0x0f, 0xd7, 0xc8, //0x0000065d pmovmskb     %xmm0, %ecx
	0x66, 0x0f, 0x74, 0xec, //0x00000661 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000665 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00000669 pcmpeqb      %xmm4, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x0000066d pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x74, 0xf4, //0x00000672 pcmpeqb      %xmm4, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xee, //0x00000676 pmovmskb     %xmm6, %r13d
	0x49, 0xc1, 0xe5, 0x30, //0x0000067b shlq         $48, %r13
	0x49, 0xc1, 0xe2, 0x20, //0x0000067f shlq         $32, %r10
	0x4d, 0x09, 0xea, //0x00000683 orq          %r13, %r10
	0x48, 0xc1, 0xe3, 0x10, //0x00000686 shlq         $16, %rbx
	0x4c, 0x09, 0xd3, //0x0000068a orq          %r10, %rbx
	0x48, 0x09, 0xd9, //0x0000068d orq          %rbx, %rcx
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000690 movabsq      $6148914691236517205, %r10
	0x49, 0xbd, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x0000069a movabsq      $3689348814741910323, %r13
	0x48, 0x21, 0xc1, //0x000006a4 andq         %rax, %rcx
	0x0f, 0x84, 0xd7, 0xfd, 0xff, 0xff, //0x000006a7 je           LBB0_59
	0x90, 0x90, 0x90, //0x000006ad .p2align 4, 0x90
	//0x000006b0 LBB0_65
	0x48, 0x8d, 0x51, 0xff, //0x000006b0 leaq         $-1(%rcx), %rdx
	0x48, 0x89, 0xd3, //0x000006b4 movq         %rdx, %rbx
	0x4c, 0x21, 0xc3, //0x000006b7 andq         %r8, %rbx
	0x48, 0x89, 0xd8, //0x000006ba movq         %rbx, %rax
	0x48, 0xd1, 0xe8, //0x000006bd shrq         %rax
	0x4c, 0x21, 0xd0, //0x000006c0 andq         %r10, %rax
	0x48, 0x29, 0xc3, //0x000006c3 subq         %rax, %rbx
	0x48, 0x89, 0xd8, //0x000006c6 movq         %rbx, %rax
	0x4c, 0x21, 0xe8, //0x000006c9 andq         %r13, %rax
	0x48, 0xc1, 0xeb, 0x02, //0x000006cc shrq         $2, %rbx
	0x4c, 0x21, 0xeb, //0x000006d0 andq         %r13, %rbx
	0x48, 0x01, 0xc3, //0x000006d3 addq         %rax, %rbx
	0x48, 0x89, 0xd8, //0x000006d6 movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x000006d9 shrq         $4, %rax
	0x48, 0x01, 0xd8, //0x000006dd addq         %rbx, %rax
	0x48, 0xbb, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000006e0 movabsq      $1085102592571150095, %rbx
	0x48, 0x21, 0xd8, //0x000006ea andq         %rbx, %rax
	0x48, 0xbb, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000006ed movabsq      $72340172838076673, %rbx
	0x48, 0x0f, 0xaf, 0xc3, //0x000006f7 imulq        %rbx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x000006fb shrq         $56, %rax
	0x48, 0x03, 0x45, 0xc0, //0x000006ff addq         $-64(%rbp), %rax
	0x4c, 0x39, 0xd8, //0x00000703 cmpq         %r11, %rax
	0x0f, 0x86, 0x92, 0x05, 0x00, 0x00, //0x00000706 jbe          LBB0_109
	0x49, 0x83, 0xc3, 0x01, //0x0000070c addq         $1, %r11
	0x48, 0x21, 0xd1, //0x00000710 andq         %rdx, %rcx
	0x0f, 0x85, 0x97, 0xff, 0xff, 0xff, //0x00000713 jne          LBB0_65
	0xe9, 0x66, 0xfd, 0xff, 0xff, //0x00000719 jmp          LBB0_59
	//0x0000071e LBB0_67
	0x48, 0x85, 0xc0, //0x0000071e testq        %rax, %rax
	0x0f, 0x8e, 0x3b, 0x06, 0x00, 0x00, //0x00000721 jle          LBB0_119
	0x4c, 0x89, 0xf9, //0x00000727 movq         %r15, %rcx
	0x44, 0x0f, 0x11, 0x45, 0xb0, //0x0000072a movups       %xmm8, $-80(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0xa0, //0x0000072f movups       %xmm8, $-96(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x90, //0x00000734 movups       %xmm8, $-112(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x80, //0x00000739 movups       %xmm8, $-128(%rbp)
	0x44, 0x89, 0xc8, //0x0000073e movl         %r9d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00000741 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00000746 cmpl         $4033, %eax
	0x0f, 0x82, 0x31, 0x00, 0x00, 0x00, //0x0000074b jb           LBB0_71
	0x48, 0x83, 0x7d, 0xd0, 0x20, //0x00000751 cmpq         $32, $-48(%rbp)
	0x0f, 0x82, 0x38, 0x00, 0x00, 0x00, //0x00000756 jb           LBB0_72
	0x41, 0x0f, 0x10, 0x01, //0x0000075c movups       (%r9), %xmm0
	0x0f, 0x11, 0x45, 0x80, //0x00000760 movups       %xmm0, $-128(%rbp)
	0x41, 0x0f, 0x10, 0x41, 0x10, //0x00000764 movups       $16(%r9), %xmm0
	0x0f, 0x11, 0x45, 0x90, //0x00000769 movups       %xmm0, $-112(%rbp)
	0x49, 0x83, 0xc1, 0x20, //0x0000076d addq         $32, %r9
	0x48, 0x8b, 0x45, 0xd0, //0x00000771 movq         $-48(%rbp), %rax
	0x48, 0x8d, 0x50, 0xe0, //0x00000775 leaq         $-32(%rax), %rdx
	0x4c, 0x8d, 0x45, 0xa0, //0x00000779 leaq         $-96(%rbp), %r8
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x0000077d jmp          LBB0_73
	//0x00000782 LBB0_71
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000782 movabsq      $6148914691236517205, %r10
	0x49, 0x89, 0xcf, //0x0000078c movq         %rcx, %r15
	0xe9, 0x5e, 0xfd, 0xff, 0xff, //0x0000078f jmp          LBB0_61
	//0x00000794 LBB0_72
	0x4c, 0x8d, 0x45, 0x80, //0x00000794 leaq         $-128(%rbp), %r8
	0x48, 0x8b, 0x55, 0xd0, //0x00000798 movq         $-48(%rbp), %rdx
	//0x0000079c LBB0_73
	0x48, 0x83, 0xfa, 0x10, //0x0000079c cmpq         $16, %rdx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x000007a0 jb           LBB0_74
	0x41, 0x0f, 0x10, 0x01, //0x000007a6 movups       (%r9), %xmm0
	0x41, 0x0f, 0x11, 0x00, //0x000007aa movups       %xmm0, (%r8)
	0x49, 0x83, 0xc1, 0x10, //0x000007ae addq         $16, %r9
	0x49, 0x83, 0xc0, 0x10, //0x000007b2 addq         $16, %r8
	0x48, 0x83, 0xc2, 0xf0, //0x000007b6 addq         $-16, %rdx
	0x48, 0x83, 0xfa, 0x08, //0x000007ba cmpq         $8, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000007be jae          LBB0_81
	//0x000007c4 LBB0_75
	0x48, 0x83, 0xfa, 0x04, //0x000007c4 cmpq         $4, %rdx
	0x0f, 0x8c, 0x47, 0x00, 0x00, 0x00, //0x000007c8 jl           LBB0_76
	//0x000007ce LBB0_82
	0x41, 0x8b, 0x01, //0x000007ce movl         (%r9), %eax
	0x41, 0x89, 0x00, //0x000007d1 movl         %eax, (%r8)
	0x49, 0x83, 0xc1, 0x04, //0x000007d4 addq         $4, %r9
	0x49, 0x83, 0xc0, 0x04, //0x000007d8 addq         $4, %r8
	0x48, 0x83, 0xc2, 0xfc, //0x000007dc addq         $-4, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x000007e0 cmpq         $2, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x000007e4 jae          LBB0_77
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x000007ea jmp          LBB0_78
	//0x000007ef LBB0_74
	0x48, 0x83, 0xfa, 0x08, //0x000007ef cmpq         $8, %rdx
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x000007f3 jb           LBB0_75
	//0x000007f9 LBB0_81
	0x49, 0x8b, 0x01, //0x000007f9 movq         (%r9), %rax
	0x49, 0x89, 0x00, //0x000007fc movq         %rax, (%r8)
	0x49, 0x83, 0xc1, 0x08, //0x000007ff addq         $8, %r9
	0x49, 0x83, 0xc0, 0x08, //0x00000803 addq         $8, %r8
	0x48, 0x83, 0xc2, 0xf8, //0x00000807 addq         $-8, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x0000080b cmpq         $4, %rdx
	0x0f, 0x8d, 0xb9, 0xff, 0xff, 0xff, //0x0000080f jge          LBB0_82
	//0x00000815 LBB0_76
	0x48, 0x83, 0xfa, 0x02, //0x00000815 cmpq         $2, %rdx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00000819 jb           LBB0_78
	//0x0000081f LBB0_77
	0x41, 0x0f, 0xb7, 0x01, //0x0000081f movzwl       (%r9), %eax
	0x66, 0x41, 0x89, 0x00, //0x00000823 movw         %ax, (%r8)
	0x49, 0x83, 0xc1, 0x02, //0x00000827 addq         $2, %r9
	0x49, 0x83, 0xc0, 0x02, //0x0000082b addq         $2, %r8
	0x48, 0x83, 0xc2, 0xfe, //0x0000082f addq         $-2, %rdx
	//0x00000833 LBB0_78
	0x4c, 0x89, 0xc8, //0x00000833 movq         %r9, %rax
	0x4c, 0x8d, 0x4d, 0x80, //0x00000836 leaq         $-128(%rbp), %r9
	0x48, 0x85, 0xd2, //0x0000083a testq        %rdx, %rdx
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x0000083d movabsq      $6148914691236517205, %r10
	0x49, 0x89, 0xcf, //0x00000847 movq         %rcx, %r15
	0x0f, 0x84, 0xa2, 0xfc, 0xff, 0xff, //0x0000084a je           LBB0_61
	0x8a, 0x00, //0x00000850 movb         (%rax), %al
	0x41, 0x88, 0x00, //0x00000852 movb         %al, (%r8)
	0x4c, 0x8d, 0x4d, 0x80, //0x00000855 leaq         $-128(%rbp), %r9
	0xe9, 0x94, 0xfc, 0xff, 0xff, //0x00000859 jmp          LBB0_61
	//0x0000085e LBB0_83
	0x49, 0x8d, 0x44, 0x24, 0x05, //0x0000085e leaq         $5(%r12), %rax
	0x48, 0x3b, 0x47, 0x08, //0x00000863 cmpq         $8(%rdi), %rax
	0x0f, 0x86, 0x62, 0xfa, 0xff, 0xff, //0x00000867 jbe          LBB0_42
	0xe9, 0x63, 0xfa, 0xff, 0xff, //0x0000086d jmp          LBB0_44
	//0x00000872 LBB0_84
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000872 movabsq      $6148914691236517205, %r10
	0x48, 0x8b, 0x47, 0x08, //0x0000087c movq         $8(%rdi), %rax
	0x48, 0x29, 0xd0, //0x00000880 subq         %rdx, %rax
	0x49, 0x01, 0xd1, //0x00000883 addq         %rdx, %r9
	0x45, 0x31, 0xed, //0x00000886 xorl         %r13d, %r13d
	0xf3, 0x44, 0x0f, 0x6f, 0x15, 0xae, 0xf7, 0xff, 0xff, //0x00000889 movdqu       $-2130(%rip), %xmm10  /* LCPI0_4+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x0d, 0x96, 0xf7, 0xff, 0xff, //0x00000892 movdqu       $-2154(%rip), %xmm1  /* LCPI0_3+0(%rip) */
	0x66, 0x45, 0x0f, 0x76, 0xc9, //0x0000089a pcmpeqd      %xmm9, %xmm9
	0xf3, 0x0f, 0x6f, 0x1d, 0xa9, 0xf7, 0xff, 0xff, //0x0000089f movdqu       $-2135(%rip), %xmm3  /* LCPI0_5+0(%rip) */
	0xf3, 0x0f, 0x6f, 0x25, 0xb1, 0xf7, 0xff, 0xff, //0x000008a7 movdqu       $-2127(%rip), %xmm4  /* LCPI0_6+0(%rip) */
	0x45, 0x0f, 0x57, 0xc0, //0x000008af xorps        %xmm8, %xmm8
	0x45, 0x31, 0xff, //0x000008b3 xorl         %r15d, %r15d
	0x31, 0xc9, //0x000008b6 xorl         %ecx, %ecx
	0x48, 0x89, 0x4d, 0xc0, //0x000008b8 movq         %rcx, $-64(%rbp)
	0x45, 0x31, 0xdb, //0x000008bc xorl         %r11d, %r11d
	0xe9, 0x60, 0x00, 0x00, 0x00, //0x000008bf jmp          LBB0_86
	//0x000008c4 LBB0_85
	0x49, 0xc1, 0xff, 0x3f, //0x000008c4 sarq         $63, %r15
	0x4c, 0x89, 0xc0, //0x000008c8 movq         %r8, %rax
	0x48, 0xd1, 0xe8, //0x000008cb shrq         %rax
	0x4c, 0x21, 0xd0, //0x000008ce andq         %r10, %rax
	0x49, 0x29, 0xc0, //0x000008d1 subq         %rax, %r8
	0x4c, 0x89, 0xc0, //0x000008d4 movq         %r8, %rax
	0x4c, 0x21, 0xe8, //0x000008d7 andq         %r13, %rax
	0x49, 0xc1, 0xe8, 0x02, //0x000008da shrq         $2, %r8
	0x4d, 0x21, 0xe8, //0x000008de andq         %r13, %r8
	0x49, 0x01, 0xc0, //0x000008e1 addq         %rax, %r8
	0x4c, 0x89, 0xc0, //0x000008e4 movq         %r8, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x000008e7 shrq         $4, %rax
	0x4c, 0x01, 0xc0, //0x000008eb addq         %r8, %rax
	0x48, 0xb9, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x000008ee movabsq      $1085102592571150095, %rcx
	0x48, 0x21, 0xc8, //0x000008f8 andq         %rcx, %rax
	0x48, 0xb9, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x000008fb movabsq      $72340172838076673, %rcx
	0x48, 0x0f, 0xaf, 0xc1, //0x00000905 imulq        %rcx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00000909 shrq         $56, %rax
	0x48, 0x01, 0x45, 0xc0, //0x0000090d addq         %rax, $-64(%rbp)
	0x49, 0x83, 0xc1, 0x40, //0x00000911 addq         $64, %r9
	0x48, 0x8b, 0x45, 0xd0, //0x00000915 movq         $-48(%rbp), %rax
	0x48, 0x83, 0xc0, 0xc0, //0x00000919 addq         $-64, %rax
	0x4d, 0x89, 0xfd, //0x0000091d movq         %r15, %r13
	0x4c, 0x8b, 0x7d, 0xc8, //0x00000920 movq         $-56(%rbp), %r15
	//0x00000924 LBB0_86
	0x48, 0x83, 0xf8, 0x40, //0x00000924 cmpq         $64, %rax
	0x48, 0x89, 0x45, 0xd0, //0x00000928 movq         %rax, $-48(%rbp)
	0x0f, 0x8c, 0x2c, 0x02, 0x00, 0x00, //0x0000092c jl           LBB0_93
	//0x00000932 LBB0_87
	0xf3, 0x41, 0x0f, 0x6f, 0x01, //0x00000932 movdqu       (%r9), %xmm0
	0xf3, 0x41, 0x0f, 0x6f, 0x69, 0x10, //0x00000937 movdqu       $16(%r9), %xmm5
	0xf3, 0x41, 0x0f, 0x6f, 0x79, 0x20, //0x0000093d movdqu       $32(%r9), %xmm7
	0xf3, 0x41, 0x0f, 0x6f, 0x71, 0x30, //0x00000943 movdqu       $48(%r9), %xmm6
	0x66, 0x0f, 0x6f, 0xd0, //0x00000949 movdqa       %xmm0, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x0000094d pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xd2, //0x00000952 pmovmskb     %xmm2, %edx
	0x66, 0x0f, 0x6f, 0xd5, //0x00000956 movdqa       %xmm5, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x0000095a pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x0000095f pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00000963 movdqa       %xmm7, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000967 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x0000096c pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd6, //0x00000970 movdqa       %xmm6, %xmm2
	0x66, 0x41, 0x0f, 0x74, 0xd2, //0x00000974 pcmpeqb      %xmm10, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000979 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x0000097d shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x00000981 shlq         $32, %rcx
	0x48, 0x09, 0xd9, //0x00000985 orq          %rbx, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00000988 shlq         $16, %rax
	0x48, 0x09, 0xc8, //0x0000098c orq          %rcx, %rax
	0x48, 0x09, 0xc2, //0x0000098f orq          %rax, %rdx
	0x48, 0x89, 0xd0, //0x00000992 movq         %rdx, %rax
	0x4c, 0x09, 0xf8, //0x00000995 orq          %r15, %rax
	0x0f, 0x85, 0x12, 0x00, 0x00, 0x00, //0x00000998 jne          LBB0_89
	0x48, 0xc7, 0xc2, 0xff, 0xff, 0xff, 0xff, //0x0000099e movq         $-1, %rdx
	0x31, 0xc0, //0x000009a5 xorl         %eax, %eax
	0x48, 0x89, 0x45, 0xc8, //0x000009a7 movq         %rax, $-56(%rbp)
	0xe9, 0x3e, 0x00, 0x00, 0x00, //0x000009ab jmp          LBB0_90
	//0x000009b0 LBB0_89
	0x4c, 0x89, 0xf8, //0x000009b0 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x000009b3 notq         %rax
	0x48, 0x21, 0xd0, //0x000009b6 andq         %rdx, %rax
	0x4c, 0x8d, 0x04, 0x00, //0x000009b9 leaq         (%rax,%rax), %r8
	0x4d, 0x09, 0xf8, //0x000009bd orq          %r15, %r8
	0x4c, 0x89, 0xc1, //0x000009c0 movq         %r8, %rcx
	0x48, 0xf7, 0xd1, //0x000009c3 notq         %rcx
	0x48, 0xbb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, //0x000009c6 movabsq      $-6148914691236517206, %rbx
	0x48, 0x21, 0xda, //0x000009d0 andq         %rbx, %rdx
	0x48, 0x21, 0xca, //0x000009d3 andq         %rcx, %rdx
	0x31, 0xc9, //0x000009d6 xorl         %ecx, %ecx
	0x48, 0x01, 0xc2, //0x000009d8 addq         %rax, %rdx
	0x0f, 0x92, 0xc1, //0x000009db setb         %cl
	0x48, 0x89, 0x4d, 0xc8, //0x000009de movq         %rcx, $-56(%rbp)
	0x48, 0x01, 0xd2, //0x000009e2 addq         %rdx, %rdx
	0x4c, 0x31, 0xd2, //0x000009e5 xorq         %r10, %rdx
	0x4c, 0x21, 0xc2, //0x000009e8 andq         %r8, %rdx
	0x48, 0xf7, 0xd2, //0x000009eb notq         %rdx
	//0x000009ee LBB0_90
	0x66, 0x0f, 0x6f, 0xd6, //0x000009ee movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x000009f2 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x000009f6 pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x30, //0x000009fa shlq         $48, %rax
	0x66, 0x0f, 0x6f, 0xd7, //0x000009fe movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00000a02 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000a06 pmovmskb     %xmm2, %ecx
	0x48, 0xc1, 0xe1, 0x20, //0x00000a0a shlq         $32, %rcx
	0x48, 0x09, 0xc1, //0x00000a0e orq          %rax, %rcx
	0x66, 0x0f, 0x6f, 0xd5, //0x00000a11 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00000a15 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000a19 pmovmskb     %xmm2, %eax
	0x48, 0xc1, 0xe0, 0x10, //0x00000a1d shlq         $16, %rax
	0x48, 0x09, 0xc8, //0x00000a21 orq          %rcx, %rax
	0x66, 0x0f, 0x6f, 0xd0, //0x00000a24 movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd1, //0x00000a28 pcmpeqb      %xmm1, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000a2c pmovmskb     %xmm2, %ecx
	0x48, 0x09, 0xc1, //0x00000a30 orq          %rax, %rcx
	0x48, 0x21, 0xd1, //0x00000a33 andq         %rdx, %rcx
	0x66, 0x48, 0x0f, 0x6e, 0xd1, //0x00000a36 movq         %rcx, %xmm2
	0x66, 0x41, 0x0f, 0x3a, 0x44, 0xd1, 0x00, //0x00000a3b pclmulqdq    $0, %xmm9, %xmm2
	0x66, 0x49, 0x0f, 0x7e, 0xd7, //0x00000a42 movq         %xmm2, %r15
	0x4d, 0x31, 0xef, //0x00000a47 xorq         %r13, %r15
	0x66, 0x0f, 0x6f, 0xd0, //0x00000a4a movdqa       %xmm0, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000a4e pcmpeqb      %xmm3, %xmm2
	0x66, 0x44, 0x0f, 0xd7, 0xc2, //0x00000a52 pmovmskb     %xmm2, %r8d
	0x66, 0x0f, 0x6f, 0xd5, //0x00000a57 movdqa       %xmm5, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000a5b pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xc2, //0x00000a5f pmovmskb     %xmm2, %eax
	0x66, 0x0f, 0x6f, 0xd7, //0x00000a63 movdqa       %xmm7, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000a67 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xca, //0x00000a6b pmovmskb     %xmm2, %ecx
	0x66, 0x0f, 0x6f, 0xd6, //0x00000a6f movdqa       %xmm6, %xmm2
	0x66, 0x0f, 0x74, 0xd3, //0x00000a73 pcmpeqb      %xmm3, %xmm2
	0x66, 0x0f, 0xd7, 0xda, //0x00000a77 pmovmskb     %xmm2, %ebx
	0x48, 0xc1, 0xe3, 0x30, //0x00000a7b shlq         $48, %rbx
	0x48, 0xc1, 0xe1, 0x20, //0x00000a7f shlq         $32, %rcx
	0x48, 0x09, 0xd9, //0x00000a83 orq          %rbx, %rcx
	0x48, 0xc1, 0xe0, 0x10, //0x00000a86 shlq         $16, %rax
	0x48, 0x09, 0xc8, //0x00000a8a orq          %rcx, %rax
	0x49, 0x09, 0xc0, //0x00000a8d orq          %rax, %r8
	0x4c, 0x89, 0xf8, //0x00000a90 movq         %r15, %rax
	0x48, 0xf7, 0xd0, //0x00000a93 notq         %rax
	0x49, 0x21, 0xc0, //0x00000a96 andq         %rax, %r8
	0x66, 0x0f, 0x74, 0xc4, //0x00000a99 pcmpeqb      %xmm4, %xmm0
	0x66, 0x0f, 0xd7, 0xc8, //0x00000a9d pmovmskb     %xmm0, %ecx
	0x66, 0x0f, 0x74, 0xec, //0x00000aa1 pcmpeqb      %xmm4, %xmm5
	0x66, 0x0f, 0xd7, 0xdd, //0x00000aa5 pmovmskb     %xmm5, %ebx
	0x66, 0x0f, 0x74, 0xfc, //0x00000aa9 pcmpeqb      %xmm4, %xmm7
	0x66, 0x44, 0x0f, 0xd7, 0xd7, //0x00000aad pmovmskb     %xmm7, %r10d
	0x66, 0x0f, 0x74, 0xf4, //0x00000ab2 pcmpeqb      %xmm4, %xmm6
	0x66, 0x44, 0x0f, 0xd7, 0xee, //0x00000ab6 pmovmskb     %xmm6, %r13d
	0x49, 0xc1, 0xe5, 0x30, //0x00000abb shlq         $48, %r13
	0x49, 0xc1, 0xe2, 0x20, //0x00000abf shlq         $32, %r10
	0x4d, 0x09, 0xea, //0x00000ac3 orq          %r13, %r10
	0x48, 0xc1, 0xe3, 0x10, //0x00000ac6 shlq         $16, %rbx
	0x4c, 0x09, 0xd3, //0x00000aca orq          %r10, %rbx
	0x48, 0x09, 0xd9, //0x00000acd orq          %rbx, %rcx
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000ad0 movabsq      $6148914691236517205, %r10
	0x49, 0xbd, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, //0x00000ada movabsq      $3689348814741910323, %r13
	0x48, 0x21, 0xc1, //0x00000ae4 andq         %rax, %rcx
	0x0f, 0x84, 0xd7, 0xfd, 0xff, 0xff, //0x00000ae7 je           LBB0_85
	0x90, 0x90, 0x90, //0x00000aed .p2align 4, 0x90
	//0x00000af0 LBB0_91
	0x48, 0x8d, 0x51, 0xff, //0x00000af0 leaq         $-1(%rcx), %rdx
	0x48, 0x89, 0xd3, //0x00000af4 movq         %rdx, %rbx
	0x4c, 0x21, 0xc3, //0x00000af7 andq         %r8, %rbx
	0x48, 0x89, 0xd8, //0x00000afa movq         %rbx, %rax
	0x48, 0xd1, 0xe8, //0x00000afd shrq         %rax
	0x4c, 0x21, 0xd0, //0x00000b00 andq         %r10, %rax
	0x48, 0x29, 0xc3, //0x00000b03 subq         %rax, %rbx
	0x48, 0x89, 0xd8, //0x00000b06 movq         %rbx, %rax
	0x4c, 0x21, 0xe8, //0x00000b09 andq         %r13, %rax
	0x48, 0xc1, 0xeb, 0x02, //0x00000b0c shrq         $2, %rbx
	0x4c, 0x21, 0xeb, //0x00000b10 andq         %r13, %rbx
	0x48, 0x01, 0xc3, //0x00000b13 addq         %rax, %rbx
	0x48, 0x89, 0xd8, //0x00000b16 movq         %rbx, %rax
	0x48, 0xc1, 0xe8, 0x04, //0x00000b19 shrq         $4, %rax
	0x48, 0x01, 0xd8, //0x00000b1d addq         %rbx, %rax
	0x48, 0xbb, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, //0x00000b20 movabsq      $1085102592571150095, %rbx
	0x48, 0x21, 0xd8, //0x00000b2a andq         %rbx, %rax
	0x48, 0xbb, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, //0x00000b2d movabsq      $72340172838076673, %rbx
	0x48, 0x0f, 0xaf, 0xc3, //0x00000b37 imulq        %rbx, %rax
	0x48, 0xc1, 0xe8, 0x38, //0x00000b3b shrq         $56, %rax
	0x48, 0x03, 0x45, 0xc0, //0x00000b3f addq         $-64(%rbp), %rax
	0x4c, 0x39, 0xd8, //0x00000b43 cmpq         %r11, %rax
	0x0f, 0x86, 0x52, 0x01, 0x00, 0x00, //0x00000b46 jbe          LBB0_109
	0x49, 0x83, 0xc3, 0x01, //0x00000b4c addq         $1, %r11
	0x48, 0x21, 0xd1, //0x00000b50 andq         %rdx, %rcx
	0x0f, 0x85, 0x97, 0xff, 0xff, 0xff, //0x00000b53 jne          LBB0_91
	0xe9, 0x66, 0xfd, 0xff, 0xff, //0x00000b59 jmp          LBB0_85
	//0x00000b5e LBB0_93
	0x48, 0x85, 0xc0, //0x00000b5e testq        %rax, %rax
	0x0f, 0x8e, 0xfb, 0x01, 0x00, 0x00, //0x00000b61 jle          LBB0_119
	0x4c, 0x89, 0xf9, //0x00000b67 movq         %r15, %rcx
	0x44, 0x0f, 0x11, 0x45, 0xb0, //0x00000b6a movups       %xmm8, $-80(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0xa0, //0x00000b6f movups       %xmm8, $-96(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x90, //0x00000b74 movups       %xmm8, $-112(%rbp)
	0x44, 0x0f, 0x11, 0x45, 0x80, //0x00000b79 movups       %xmm8, $-128(%rbp)
	0x44, 0x89, 0xc8, //0x00000b7e movl         %r9d, %eax
	0x25, 0xff, 0x0f, 0x00, 0x00, //0x00000b81 andl         $4095, %eax
	0x3d, 0xc1, 0x0f, 0x00, 0x00, //0x00000b86 cmpl         $4033, %eax
	0x0f, 0x82, 0x31, 0x00, 0x00, 0x00, //0x00000b8b jb           LBB0_97
	0x48, 0x83, 0x7d, 0xd0, 0x20, //0x00000b91 cmpq         $32, $-48(%rbp)
	0x0f, 0x82, 0x38, 0x00, 0x00, 0x00, //0x00000b96 jb           LBB0_98
	0x41, 0x0f, 0x10, 0x01, //0x00000b9c movups       (%r9), %xmm0
	0x0f, 0x11, 0x45, 0x80, //0x00000ba0 movups       %xmm0, $-128(%rbp)
	0x41, 0x0f, 0x10, 0x41, 0x10, //0x00000ba4 movups       $16(%r9), %xmm0
	0x0f, 0x11, 0x45, 0x90, //0x00000ba9 movups       %xmm0, $-112(%rbp)
	0x49, 0x83, 0xc1, 0x20, //0x00000bad addq         $32, %r9
	0x48, 0x8b, 0x45, 0xd0, //0x00000bb1 movq         $-48(%rbp), %rax
	0x48, 0x8d, 0x50, 0xe0, //0x00000bb5 leaq         $-32(%rax), %rdx
	0x4c, 0x8d, 0x45, 0xa0, //0x00000bb9 leaq         $-96(%rbp), %r8
	0xe9, 0x1a, 0x00, 0x00, 0x00, //0x00000bbd jmp          LBB0_99
	//0x00000bc2 LBB0_97
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000bc2 movabsq      $6148914691236517205, %r10
	0x49, 0x89, 0xcf, //0x00000bcc movq         %rcx, %r15
	0xe9, 0x5e, 0xfd, 0xff, 0xff, //0x00000bcf jmp          LBB0_87
	//0x00000bd4 LBB0_98
	0x4c, 0x8d, 0x45, 0x80, //0x00000bd4 leaq         $-128(%rbp), %r8
	0x48, 0x8b, 0x55, 0xd0, //0x00000bd8 movq         $-48(%rbp), %rdx
	//0x00000bdc LBB0_99
	0x48, 0x83, 0xfa, 0x10, //0x00000bdc cmpq         $16, %rdx
	0x0f, 0x82, 0x49, 0x00, 0x00, 0x00, //0x00000be0 jb           LBB0_100
	0x41, 0x0f, 0x10, 0x01, //0x00000be6 movups       (%r9), %xmm0
	0x41, 0x0f, 0x11, 0x00, //0x00000bea movups       %xmm0, (%r8)
	0x49, 0x83, 0xc1, 0x10, //0x00000bee addq         $16, %r9
	0x49, 0x83, 0xc0, 0x10, //0x00000bf2 addq         $16, %r8
	0x48, 0x83, 0xc2, 0xf0, //0x00000bf6 addq         $-16, %rdx
	0x48, 0x83, 0xfa, 0x08, //0x00000bfa cmpq         $8, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00000bfe jae          LBB0_107
	//0x00000c04 LBB0_101
	0x48, 0x83, 0xfa, 0x04, //0x00000c04 cmpq         $4, %rdx
	0x0f, 0x8c, 0x47, 0x00, 0x00, 0x00, //0x00000c08 jl           LBB0_102
	//0x00000c0e LBB0_108
	0x41, 0x8b, 0x01, //0x00000c0e movl         (%r9), %eax
	0x41, 0x89, 0x00, //0x00000c11 movl         %eax, (%r8)
	0x49, 0x83, 0xc1, 0x04, //0x00000c14 addq         $4, %r9
	0x49, 0x83, 0xc0, 0x04, //0x00000c18 addq         $4, %r8
	0x48, 0x83, 0xc2, 0xfc, //0x00000c1c addq         $-4, %rdx
	0x48, 0x83, 0xfa, 0x02, //0x00000c20 cmpq         $2, %rdx
	0x0f, 0x83, 0x35, 0x00, 0x00, 0x00, //0x00000c24 jae          LBB0_103
	0xe9, 0x44, 0x00, 0x00, 0x00, //0x00000c2a jmp          LBB0_104
	//0x00000c2f LBB0_100
	0x48, 0x83, 0xfa, 0x08, //0x00000c2f cmpq         $8, %rdx
	0x0f, 0x82, 0xcb, 0xff, 0xff, 0xff, //0x00000c33 jb           LBB0_101
	//0x00000c39 LBB0_107
	0x49, 0x8b, 0x01, //0x00000c39 movq         (%r9), %rax
	0x49, 0x89, 0x00, //0x00000c3c movq         %rax, (%r8)
	0x49, 0x83, 0xc1, 0x08, //0x00000c3f addq         $8, %r9
	0x49, 0x83, 0xc0, 0x08, //0x00000c43 addq         $8, %r8
	0x48, 0x83, 0xc2, 0xf8, //0x00000c47 addq         $-8, %rdx
	0x48, 0x83, 0xfa, 0x04, //0x00000c4b cmpq         $4, %rdx
	0x0f, 0x8d, 0xb9, 0xff, 0xff, 0xff, //0x00000c4f jge          LBB0_108
	//0x00000c55 LBB0_102
	0x48, 0x83, 0xfa, 0x02, //0x00000c55 cmpq         $2, %rdx
	0x0f, 0x82, 0x14, 0x00, 0x00, 0x00, //0x00000c59 jb           LBB0_104
	//0x00000c5f LBB0_103
	0x41, 0x0f, 0xb7, 0x01, //0x00000c5f movzwl       (%r9), %eax
	0x66, 0x41, 0x89, 0x00, //0x00000c63 movw         %ax, (%r8)
	0x49, 0x83, 0xc1, 0x02, //0x00000c67 addq         $2, %r9
	0x49, 0x83, 0xc0, 0x02, //0x00000c6b addq         $2, %r8
	0x48, 0x83, 0xc2, 0xfe, //0x00000c6f addq         $-2, %rdx
	//0x00000c73 LBB0_104
	0x4c, 0x89, 0xc8, //0x00000c73 movq         %r9, %rax
	0x4c, 0x8d, 0x4d, 0x80, //0x00000c76 leaq         $-128(%rbp), %r9
	0x48, 0x85, 0xd2, //0x00000c7a testq        %rdx, %rdx
	0x49, 0xba, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, //0x00000c7d movabsq      $6148914691236517205, %r10
	0x49, 0x89, 0xcf, //0x00000c87 movq         %rcx, %r15
	0x0f, 0x84, 0xa2, 0xfc, 0xff, 0xff, //0x00000c8a je           LBB0_87
	0x8a, 0x00, //0x00000c90 movb         (%rax), %al
	0x41, 0x88, 0x00, //0x00000c92 movb         %al, (%r8)
	0x4c, 0x8d, 0x4d, 0x80, //0x00000c95 leaq         $-128(%rbp), %r9
	0xe9, 0x94, 0xfc, 0xff, 0xff, //0x00000c99 jmp          LBB0_87
	//0x00000c9e LBB0_109
	0x48, 0x8b, 0x47, 0x08, //0x00000c9e movq         $8(%rdi), %rax
	0x48, 0x0f, 0xbc, 0xc9, //0x00000ca2 bsfq         %rcx, %rcx
	0x48, 0x2b, 0x4d, 0xd0, //0x00000ca6 subq         $-48(%rbp), %rcx
	0x48, 0x01, 0xc8, //0x00000caa addq         %rcx, %rax
	0x48, 0x83, 0xc0, 0x01, //0x00000cad addq         $1, %rax
	0x48, 0x89, 0x06, //0x00000cb1 movq         %rax, (%rsi)
	0x48, 0x8b, 0x4f, 0x08, //0x00000cb4 movq         $8(%rdi), %rcx
	0x48, 0x39, 0xc8, //0x00000cb8 cmpq         %rcx, %rax
	0x48, 0x0f, 0x47, 0xc1, //0x00000cbb cmovaq       %rcx, %rax
	0x48, 0x89, 0x06, //0x00000cbf movq         %rax, (%rsi)
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000cc2 movq         $-1, %rax
	0x4c, 0x0f, 0x47, 0xe0, //0x00000cc9 cmovaq       %rax, %r12
	0xe9, 0x00, 0xf6, 0xff, 0xff, //0x00000ccd jmp          LBB0_43
	//0x00000cd2 LBB0_110
	0x0f, 0xbc, 0xc0, //0x00000cd2 bsfl         %eax, %eax
	0x4c, 0x01, 0xe0, //0x00000cd5 addq         %r12, %rax
	0x4c, 0x01, 0xf8, //0x00000cd8 addq         %r15, %rax
	0x48, 0x83, 0xc0, 0x02, //0x00000cdb addq         $2, %rax
	0xe9, 0xeb, 0xf5, 0xff, 0xff, //0x00000cdf jmp          LBB0_42
	//0x00000ce4 LBB0_111
	0x48, 0xc7, 0xc0, 0xfe, 0xff, 0xff, 0xff, //0x00000ce4 movq         $-2, %rax
	0xb9, 0x02, 0x00, 0x00, 0x00, //0x00000ceb movl         $2, %ecx
	0x48, 0x01, 0xca, //0x00000cf0 addq         %rcx, %rdx
	0x49, 0x01, 0xc7, //0x00000cf3 addq         %rax, %r15
	0x0f, 0x8e, 0xd9, 0xf5, 0xff, 0xff, //0x00000cf6 jle          LBB0_44
	//0x00000cfc LBB0_112
	0x0f, 0xb6, 0x02, //0x00000cfc movzbl       (%rdx), %eax
	0x3c, 0x5c, //0x00000cff cmpb         $92, %al
	0x0f, 0x84, 0xdd, 0xff, 0xff, 0xff, //0x00000d01 je           LBB0_111
	0x3c, 0x22, //0x00000d07 cmpb         $34, %al
	0x0f, 0x84, 0x2b, 0x00, 0x00, 0x00, //0x00000d09 je           LBB0_116
	0x48, 0xc7, 0xc0, 0xff, 0xff, 0xff, 0xff, //0x00000d0f movq         $-1, %rax
	0xb9, 0x01, 0x00, 0x00, 0x00, //0x00000d16 movl         $1, %ecx
	0x48, 0x01, 0xca, //0x00000d1b addq         %rcx, %rdx
	0x49, 0x01, 0xc7, //0x00000d1e addq         %rax, %r15
	0x0f, 0x8f, 0xd5, 0xff, 0xff, 0xff, //0x00000d21 jg           LBB0_112
	0xe9, 0xa9, 0xf5, 0xff, 0xff, //0x00000d27 jmp          LBB0_44
	//0x00000d2c LBB0_115
	0x4c, 0x29, 0xca, //0x00000d2c subq         %r9, %rdx
	0x48, 0x01, 0xc2, //0x00000d2f addq         %rax, %rdx
	0x48, 0x89, 0x16, //0x00000d32 movq         %rdx, (%rsi)
	0xe9, 0x98, 0xf5, 0xff, 0xff, //0x00000d35 jmp          LBB0_43
	//0x00000d3a LBB0_116
	0x4c, 0x29, 0xca, //0x00000d3a subq         %r9, %rdx
	0x48, 0x83, 0xc2, 0x01, //0x00000d3d addq         $1, %rdx
	0x48, 0x89, 0x16, //0x00000d41 movq         %rdx, (%rsi)
	0xe9, 0x89, 0xf5, 0xff, 0xff, //0x00000d44 jmp          LBB0_43
	//0x00000d49 LBB0_117
	0x4c, 0x01, 0xca, //0x00000d49 addq         %r9, %rdx
	0x48, 0x85, 0xc9, //0x00000d4c testq        %rcx, %rcx
	0x0f, 0x85, 0x30, 0xf5, 0xff, 0xff, //0x00000d4f jne          LBB0_34
	0xe9, 0x60, 0xf5, 0xff, 0xff, //0x00000d55 jmp          LBB0_40
	//0x00000d5a LBB0_118
	0x4c, 0x01, 0xca, //0x00000d5a addq         %r9, %rdx
	0xe9, 0xbb, 0xf6, 0xff, 0xff, //0x00000d5d jmp          LBB0_56
	//0x00000d62 LBB0_119
	0x48, 0x8b, 0x47, 0x08, //0x00000d62 movq         $8(%rdi), %rax
	0x48, 0x89, 0x06, //0x00000d66 movq         %rax, (%rsi)
	0xe9, 0x67, 0xf5, 0xff, 0xff, //0x00000d69 jmp          LBB0_44
	//0x00000d6e LBB0_120
	0x49, 0x8d, 0x40, 0xff, //0x00000d6e leaq         $-1(%r8), %rax
	0x4c, 0x39, 0xf8, //0x00000d72 cmpq         %r15, %rax
	0x0f, 0x84, 0x5a, 0xf5, 0xff, 0xff, //0x00000d75 je           LBB0_44
	0x4b, 0x8d, 0x14, 0x17, //0x00000d7b leaq         (%r15,%r10), %rdx
	0x48, 0x83, 0xc2, 0x02, //0x00000d7f addq         $2, %rdx
	0x4d, 0x29, 0xf8, //0x00000d83 subq         %r15, %r8
	0x49, 0x83, 0xc0, 0xfe, //0x00000d86 addq         $-2, %r8
	0x4d, 0x89, 0xc7, //0x00000d8a movq         %r8, %r15
	0xe9, 0x8b, 0xf6, 0xff, 0xff, //0x00000d8d jmp          LBB0_56
	0x90, 0x90, //0x00000d92 .p2align 2, 0x90
	// // .set L0_0_set_44, LBB0_44-LJTI0_0
	// // .set L0_0_set_46, LBB0_46-LJTI0_0
	// // .set L0_0_set_47, LBB0_47-LJTI0_0
	// // .set L0_0_set_29, LBB0_29-LJTI0_0
	// // .set L0_0_set_58, LBB0_58-LJTI0_0
	// // .set L0_0_set_83, LBB0_83-LJTI0_0
	// // .set L0_0_set_45, LBB0_45-LJTI0_0
	// // .set L0_0_set_84, LBB0_84-LJTI0_0
	//0x00000d94 LJTI0_0
	0x41, 0xf5, 0xff, 0xff, //0x00000d94 .long L0_0_set_44
	0x67, 0xf5, 0xff, 0xff, //0x00000d98 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000d9c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000da0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000da4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000da8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dac .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000db0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000db4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000db8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dbc .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dc0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dc4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dc8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dcc .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dd0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dd4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dd8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ddc .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000de0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000de4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000de8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dec .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000df0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000df4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000df8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000dfc .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e00 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e04 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e08 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e0c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e10 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e14 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e18 .long L0_0_set_46
	0x76, 0xf5, 0xff, 0xff, //0x00000e1c .long L0_0_set_47
	0x67, 0xf5, 0xff, 0xff, //0x00000e20 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e24 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e28 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e2c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e30 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e34 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e38 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e3c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e40 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e44 .long L0_0_set_46
	0x61, 0xf4, 0xff, 0xff, //0x00000e48 .long L0_0_set_29
	0x67, 0xf5, 0xff, 0xff, //0x00000e4c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e50 .long L0_0_set_46
	0x61, 0xf4, 0xff, 0xff, //0x00000e54 .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e58 .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e5c .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e60 .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e64 .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e68 .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e6c .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e70 .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e74 .long L0_0_set_29
	0x61, 0xf4, 0xff, 0xff, //0x00000e78 .long L0_0_set_29
	0x67, 0xf5, 0xff, 0xff, //0x00000e7c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e80 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e84 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e88 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e8c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e90 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e94 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e98 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000e9c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ea0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ea4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ea8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000eac .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000eb0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000eb4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000eb8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ebc .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ec0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ec4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ec8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ecc .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ed0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ed4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ed8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000edc .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ee0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ee4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ee8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000eec .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ef0 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ef4 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000ef8 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000efc .long L0_0_set_46
	0x9e, 0xf6, 0xff, 0xff, //0x00000f00 .long L0_0_set_58
	0x67, 0xf5, 0xff, 0xff, //0x00000f04 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f08 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f0c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f10 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f14 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f18 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f1c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f20 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f24 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f28 .long L0_0_set_46
	0xca, 0xfa, 0xff, 0xff, //0x00000f2c .long L0_0_set_83
	0x67, 0xf5, 0xff, 0xff, //0x00000f30 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f34 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f38 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f3c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f40 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f44 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f48 .long L0_0_set_46
	0x53, 0xf5, 0xff, 0xff, //0x00000f4c .long L0_0_set_45
	0x67, 0xf5, 0xff, 0xff, //0x00000f50 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f54 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f58 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f5c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f60 .long L0_0_set_46
	0x53, 0xf5, 0xff, 0xff, //0x00000f64 .long L0_0_set_45
	0x67, 0xf5, 0xff, 0xff, //0x00000f68 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f6c .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f70 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f74 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f78 .long L0_0_set_46
	0x67, 0xf5, 0xff, 0xff, //0x00000f7c .long L0_0_set_46
	0xde, 0xfa, 0xff, 0xff, //0x00000f80 .long L0_0_set_84
	//0x00000f84 .p2align 2, 0x00
	//0x00000f84 _MASK_USE_NUMBER
	0x02, 0x00, 0x00, 0x00, //0x00000f84 .long 2
}
 
