package payments

import (
	"context"
	"crypto/x509"
	"fmt"
	nethttp "net/http"
	neturl "net/url"
	"os"
	"time"

	"git.sho/ComputeHub/ComputeHub/utils"
	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/consts"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	wxUtils "github.com/wechatpay-apiv3/wechatpay-go/utils"

	"devgitlab.lianoid.com/jasonni/eaas-common/log"
	hub "git.sho/ComputeHub/ComputeHub"
	"git.sho/ComputeHub/ComputeHub/pkg/models"
)

type WcpayService struct {
	Client        *core.Client
	cfg           *hub.WcpayConfig
	notifyHandler *notify.Handler
}

func NewWcpayService(cfg *hub.WcpayConfig) (*WcpayService, error) {
	privateKey, err := wxUtils.LoadPrivateKeyWithPath(cfg.PrivateKeyPath)
	if err != nil {
		log.Errorf("load wechat private key error: %v", err)
		return nil, hub.WcpayLoadPrivateKeyErr.FromError(err)
	}
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(cfg.MchID, cfg.APICertSN, privateKey, cfg.APIv3Key),
	}

	client, err := core.NewClient(context.Background(), opts...)
	if err != nil {
		log.Errorf("create wechat pay client error: %v", err)
		return nil, hub.WcpayCreateClientErr.FromError(err)
	}

	certContent, err := os.ReadFile(cfg.PlatformCertPath)
	if err != nil {
		log.Errorf("read wechat platform cert file error: %v", err)
		return nil, hub.WcpayReadCertFileErr.FromError(err)
	}

	platformCert, err := wxUtils.LoadCertificate(string(certContent))
	if err != nil {
		log.Errorf("load wechat platform cert error: %v", err)
		return nil, hub.WcpayLoadCertErr.FromError(err)
	}
	log.Debugf("load wechat platform cert: %v", wxUtils.GetCertificateSerialNumber(*platformCert))

	certificateVisitor := core.NewCertificateMapWithList([]*x509.Certificate{platformCert})
	handler := notify.NewNotifyHandler(cfg.APIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))

	return &WcpayService{Client: client, cfg: cfg, notifyHandler: handler}, nil
}

type Detail struct {
	// 1.商户侧一张小票订单可能被分多次支付，订单原价用于记录整张小票的交易金额。 2.当订单原价与支付金额不相等，则不享受优惠。 3.该字段主要用于防止同一张小票分多次支付，以享受多次优惠的情况，正常支付订单不必上传此参数。
	CostPrice *int64 `json:"cost_price,omitempty"`
	// 商家小票ID。
	InvoiceID   *string             `json:"invoice_id,omitempty"`
	GoodsDetail []jsapi.GoodsDetail `json:"goods_detail,omitempty"`
}

type SceneInfo struct {
	// 用户终端IP
	PayerClientIP *string `json:"payer_client_ip"`
	// 商户端设备号
	DeviceID  *string    `json:"device_id,omitempty"`
	StoreInfo *StoreInfo `json:"store_info,omitempty"`
}

type StoreInfo struct {
	// 商户侧门店编号
	ID *string `json:"id"`
	// 商户侧门店名称
	Name *string `json:"name,omitempty"`
	// 地区编码，详细请见微信支付提供的文档
	AreaCode *string `json:"area_code,omitempty"`
	// 详细的商户门店地址
	Address *string `json:"address,omitempty"`
}

type SettleInfo struct {
	// 是否指定分账
	ProfitSharing *bool `json:"profit_sharing,omitempty"`
}

type PrepayRequest struct {
	AppID         *string       `json:"appid"`
	MchID         *string       `json:"mchid"`
	Description   *string       `json:"description"`
	OutTradeNo    *string       `json:"out_trade_no"`
	TimeExpire    *time.Time    `json:"time_expire,omitempty"`
	Attach        *string       `json:"attach,omitempty"`
	NotifyURL     *string       `json:"notify_url"`
	GoodsTag      *string       `json:"goods_tag,omitempty"`
	LimitPay      []string      `json:"limit_pay,omitempty"`
	SupportFapiao *bool         `json:"support_fapiao,omitempty"`
	Amount        *jsapi.Amount `json:"amount"`
	Detail        *Detail       `json:"detail,omitempty"`
	SettleInfo    *SettleInfo   `json:"settle_info,omitempty"`
	SceneInfo     *SceneInfo    `json:"scene_info,omitempty"`
}

type PrepayResponse struct {
	CodeURL string `json:"code_url"`
}

func (s *WcpayService) Prepay(c *gin.Context, order *models.Order) (codeURL string, err error) {
	var (
		localVarHTTPMethod   = nethttp.MethodPost
		localVarPostBody     interface{}
		localVarQueryParams  neturl.Values
		localVarHeaderParams = nethttp.Header{}
	)

	localVarPath := fmt.Sprintf("%s/v3/pay/transactions/native", consts.WechatPayAPIServer)

	amount := jsapi.Amount{
		Total: &order.Amount,
	}

	goodsDetail := make([]jsapi.GoodsDetail, 0)
	if len(order.GoodsDetail) > 0 {
		for _, detail := range order.GoodsDetail {
			goodsId := fmt.Sprintf("%d", detail.GoodsID)
			goodsDetail = append(goodsDetail, jsapi.GoodsDetail{
				MerchantGoodsId:  &goodsId,
				WechatpayGoodsId: &detail.PaymentGoodsID,
				GoodsName:        &detail.GoodsName,
				Quantity:         &detail.Quantity,
				UnitPrice:        &detail.UnitPrice,
			})
		}
	}

	detail := Detail{
		GoodsDetail: goodsDetail,
	}

	req := PrepayRequest{
		OutTradeNo:  &order.No,
		Amount:      &amount,
		Attach:      &order.Attach,
		GoodsTag:    &order.GoodsTag,
		Description: &order.Description,
		Detail:      &detail,
	}

	req.MchID = &s.cfg.MchID
	req.AppID = &s.cfg.AppID
	req.NotifyURL = &s.cfg.WcpayPaymentNotifyCbUrl
	localVarPostBody = req

	localVarHTTPContentTypes := []string{"application/json"}
	localVarHTTPContentType := core.SelectHeaderContentType(localVarHTTPContentTypes)

	result, err := s.Client.Request(c.Request.Context(), localVarHTTPMethod, localVarPath, localVarHeaderParams, localVarQueryParams, localVarPostBody, localVarHTTPContentType)
	if err != nil {
		return "", fmt.Errorf("request wechat prepay error: %w", err)
	}

	log.Tracef("wechat prepay response: %v", result.Response)

	resp := new(PrepayResponse)
	err = core.UnMarshalResponse(result.Response, resp)
	if err != nil {
		return "", fmt.Errorf("unmarshal wechat prepay response error: %w", err)
	}
	return resp.CodeURL, nil
}

func (s *WcpayService) CallbackPaymentNotifyRequest(c *gin.Context, persistOrder func(order *models.Order) error) {
	transaction := new(payments.Transaction)
	notifyReq, err := s.notifyHandler.ParseNotifyRequest(c.Request.Context(), c.Request, transaction)
	if err != nil {
		log.Errorf("parse wechat notify request error: %v", err)
		c.JSON(nethttp.StatusInternalServerError, gin.H{"code": "FAIL", "message": err.Error()})
		return
	}
	if notifyReq != nil {
		log.Infof("wechat notify request: %v", notifyReq)
	}

	var order models.Order
	order.No = *transaction.OutTradeNo
	order.Status = models.OrderStatusPayed
	order.PaymentTradeNo = *transaction.TransactionId
	order.PayerOpenID = *transaction.Payer.Openid

	if transaction.SuccessTime != nil {
		if successTime, parseErr := utils.ParseLocationRFC3339(*transaction.SuccessTime); parseErr == nil {
			order.PayedAt = successTime
		} else {
			log.Errorf("parse wechat notify successTime error: %v, using callback time", parseErr)
			order.PayedAt = time.Now()
		}
	} else {
		log.Warn("wcpay notify missing SuccessTime, using callback time")
		order.PayedAt = time.Now()
	}

	order.UpdatedAt = time.Now()

	if err = persistOrder(&order); err != nil {
		c.JSON(nethttp.StatusInternalServerError, gin.H{"code": "FAIL", "message": err.Error()})
		return
	}
	c.JSON(nethttp.StatusOK, nil)
}

type QueryOrderByIdRequest struct {
	TransactionID *string `json:"transaction_id"`
	MchID         *string `json:"mchid"`
}

func (s *WcpayService) QueryOrderById(ctx context.Context, req QueryOrderByIdRequest) (resp *payments.Transaction, result *core.APIResult, _ *hub.ComputeHubError) {
	var (
		localVarHTTPMethod   = nethttp.MethodGet
		localVarPostBody     interface{}
		localVarQueryParams  neturl.Values
		localVarHeaderParams = nethttp.Header{}
	)

	if req.TransactionID == nil {
		return nil, nil, hub.WcpayQueryOrderErr.FromError(fmt.Errorf("field `TransactionID` is required and must be specified in QueryOrderByIdRequest"))
	}

	localVarPath := fmt.Sprintf("%s/v3/pay/transactions/id/%s", consts.WechatPayAPIServer, neturl.PathEscape(core.ParameterToString(*req.TransactionID, "")))

	req.MchID = &s.cfg.MchID

	localVarQueryParams = neturl.Values{}
	localVarQueryParams.Add("mchid", core.ParameterToString(*req.MchID, ""))

	localVarHTTPContentTypes := []string{}
	localVarHTTPContentType := core.SelectHeaderContentType(localVarHTTPContentTypes)

	result, err := s.Client.Request(ctx, localVarHTTPMethod, localVarPath, localVarHeaderParams, localVarQueryParams, localVarPostBody, localVarHTTPContentType)
	if err != nil {
		log.Errorf("request wechat query order by id error: %v", err)
		return nil, result, hub.WcpayQueryOrderErr.FromError(err)
	}

	resp = new(payments.Transaction)
	err = core.UnMarshalResponse(result.Response, resp)
	if err != nil {
		log.Errorf("unmarshal query order by id response error: %v", err)
		return nil, result, hub.WcpayQueryOrderErr.FromError(err)
	}
	return resp, result, nil
}

type QueryOrderByOutTradeNoRequest struct {
	OutTradeNo *string `json:"out_trade_no"`
	MchID      *string `json:"mchid"`
}

func (s *WcpayService) QueryRemoteOrder(c *gin.Context, order *models.Order) (remoteOrder any, err error) {
	var (
		localVarHTTPMethod   = nethttp.MethodGet
		localVarPostBody     interface{}
		localVarQueryParams  neturl.Values
		localVarHeaderParams = nethttp.Header{}
	)

	localVarPath := fmt.Sprintf("%s/v3/pay/transactions/out-trade-no/%s", consts.WechatPayAPIServer, neturl.PathEscape(core.ParameterToString(order.No, "")))

	localVarQueryParams = neturl.Values{}
	localVarQueryParams.Add("mchid", core.ParameterToString(s.cfg.MchID, ""))

	localVarHTTPContentTypes := []string{}
	localVarHTTPContentType := core.SelectHeaderContentType(localVarHTTPContentTypes)

	result, err := s.Client.Request(c.Request.Context(), localVarHTTPMethod, localVarPath, localVarHeaderParams, localVarQueryParams, localVarPostBody, localVarHTTPContentType)
	if err != nil {
		log.Errorf("request wechat query order by out trade no error: %v", err)
		return nil, hub.WcpayQueryOrderErr.FromError(err)
	}

	log.Tracef("wechat query order by out trade no response: %v", result.Response)

	resp := new(payments.Transaction)
	err = core.UnMarshalResponse(result.Response, resp)
	if err != nil {
		log.Errorf("unmarshal query order by out trade no response error: %v", err)
		return nil, hub.WcpayQueryOrderErr.FromError(err)
	}
	return resp, nil
}

type CloseOrderRequest struct {
	OutTradeNo *string `json:"out_trade_no"`
	MchID      *string `json:"mchid"`
}

type CloseOrderBody struct {
	MchID *string `json:"mchid"`
}

// CloseOrder 关闭订单
//
// 以下情况需要调用关单接口（来自微信文档）：
// 1. 商户订单支付失败需要生成新单号重新发起支付，要对原订单号调用关单，避免重复支付；
// 2. 系统下单后，用户支付超时，系统退出不再受理，避免用户继续，请调用关单接口。
func (s *WcpayService) CloseOrder(c *gin.Context, orderNo string) error {
	var (
		localVarHTTPMethod   = nethttp.MethodPost
		localVarPostBody     interface{}
		localVarQueryParams  neturl.Values
		localVarHeaderParams = nethttp.Header{}
	)

	localVarPath := fmt.Sprintf("%s/v3/pay/transactions/out-trade-no/%s/close", consts.WechatPayAPIServer, neturl.PathEscape(core.ParameterToString(orderNo, "")))

	localVarPostBody = &CloseOrderBody{
		MchID: &s.cfg.MchID,
	}

	localVarHTTPContentTypes := []string{"application/json"}
	localVarHTTPContentType := core.SelectHeaderContentType(localVarHTTPContentTypes)

	result, err := s.Client.Request(c.Request.Context(), localVarHTTPMethod, localVarPath, localVarHeaderParams, localVarQueryParams, localVarPostBody, localVarHTTPContentType)
	if err != nil {
		return fmt.Errorf("request wechat close order by out trade no error: %v", err)
	}

	log.Tracef("wechat close order by out trade no response: %v", result.Response)

	return nil
}

type RefundAmount struct {
	Refund           *int64       `json:"refund"`
	Total            *int64       `json:"total"`
	Currency         *string      `json:"currency,omitempty"`
	From             []RefundFrom `json:"from,omitempty"`
	PayerTotal       *int64       `json:"payer_total,omitempty"`
	PayerRefund      *int64       `json:"payer_refund,omitempty"`
	SettlementRefund *int64       `json:"settlement_refund,omitempty"`
	SettlementTotal  *int64       `json:"settlement_total,omitempty"`
	DiscountRefund   *int64       `json:"discount_refund,omitempty"`
	RefundFee        *int64       `json:"refund_fee,omitempty"`
}

type RefundFrom struct {
	Account *string `json:"account"`
	Amount  *int64  `json:"amount"`
}

type PromotionDetail struct {
	PromotionID  *string       `json:"promotion_id"`
	Scope        *string       `json:"scope"`
	Type         *string       `json:"type"`
	Amount       *int64        `json:"amount"`
	RefundAmount *int64        `json:"refund_amount"`
	GoodsDetail  []GoodsDetail `json:"goods_detail,omitempty"`
}

type GoodsDetail struct {
	MerchantGoodsID  *string `json:"merchant_goods_id"`
	WechatpayGoodsID *string `json:"wechatpay_goods_id"`
	GoodsName        *string `json:"goods_name"`
	UnitPrice        *int64  `json:"unit_price"`
	RefundAmount     *int64  `json:"refund_amount"`
	RefundQuantity   *int64  `json:"refund_quantity"`
}

type RefundRequest struct {
	TransactionID *string       `json:"transaction_id,omitempty"` // Must provide either TransactionID or OutTradeNo
	OutTradeNo    *string       `json:"out_trade_no,omitempty"`
	OutRefundNo   *string       `json:"out_refund_no"`
	Reason        *string       `json:"reason,omitempty"`
	NotifyURL     *string       `json:"notify_url,omitempty"`
	Amount        *RefundAmount `json:"amount"`
}

type RefundResponse struct {
	RefundID            *string           `json:"refund_id"`
	OutRefundNo         *string           `json:"out_refund_no"`
	TransactionID       *string           `json:"transaction_id"`
	OutTradeNo          *string           `json:"out_trade_no"`
	Channel             *string           `json:"channel"`
	UserReceivedAccount *string           `json:"user_received_account"`
	SuccessTime         *string           `json:"success_time"`
	CreateTime          *string           `json:"create_time"`
	Status              *string           `json:"status"`
	FundsAccount        *string           `json:"funds_account"`
	Amount              *RefundAmount     `json:"amount"`
	PromotionDetail     []PromotionDetail `json:"promotion_detail,omitempty"`
}

func (s *WcpayService) Refund(c *gin.Context, refundOrder *models.RefundOrder, order *models.Order) error {
	var (
		localVarHTTPMethod   = nethttp.MethodPost
		localVarPostBody     *RefundRequest
		localVarQueryParams  neturl.Values
		localVarHeaderParams = nethttp.Header{}
	)

	cny := CURRENCY_CNY

	amount := RefundAmount{
		Total:    &order.Amount,
		Refund:   &refundOrder.RefundAmount,
		Currency: &cny,
	}

	req := RefundRequest{
		OutTradeNo:  &order.No,
		OutRefundNo: &refundOrder.No,
		Amount:      &amount,
		Reason:      &refundOrder.Reason,
	}
	localVarPath := fmt.Sprintf("%s/v3/refund/domestic/refunds", consts.WechatPayAPIServer)
	localVarPostBody = &req
	localVarPostBody.NotifyURL = &s.cfg.WcpayRefundNotifyCbUrl

	localVarHTTPContentTypes := []string{"application/json"}
	localVarHTTPContentType := core.SelectHeaderContentType(localVarHTTPContentTypes)

	var ctx context.Context
	if c != nil {
		ctx = c.Request.Context()
	}

	result, err := s.Client.Request(ctx, localVarHTTPMethod, localVarPath, localVarHeaderParams, localVarQueryParams, localVarPostBody, localVarHTTPContentType)
	if err != nil {
		log.Errorf("request wechat refund error: %v", err)
		return hub.WcpayRefundErr.FromError(err)
	}

	resp := new(RefundResponse)
	err = core.UnMarshalResponse(result.Response, resp)
	if err != nil {
		log.Errorf("unmarshal wechat refund response error: %v", err)
		return hub.WcpayRefundErr.FromError(err)
	}
	refundOrder.WcRefundID = *resp.RefundID
	return nil
}

type RefundNotification struct {
	MchID               string       `json:"mchid"`
	TransactionID       string       `json:"transaction_id"`
	OutTradeNo          string       `json:"out_trade_no"`
	RefundID            string       `json:"refund_id"`
	OutRefundNo         string       `json:"out_refund_no"`
	RefundStatus        string       `json:"refund_status"`
	SuccessTime         string       `json:"success_time"`
	UserReceivedAccount string       `json:"user_received_account"`
	Amount              RefundAmount `json:"amount"`
}

func (s *WcpayService) CallbackRefundNotifyRequest(c *gin.Context, persistOrder func(refundOrder *models.RefundOrder) error) {
	refundNotification := new(RefundNotification)
	notifyReq, err := s.notifyHandler.ParseNotifyRequest(c.Request.Context(), c.Request, refundNotification)
	if err != nil {
		err = fmt.Errorf("parse wechat refund notify request error: %v", err)
		c.JSON(nethttp.StatusInternalServerError, gin.H{"code": "FAIL", "message": err})
		log.Error(err)
	}
	if notifyReq != nil {
		log.Infof("wechat refund notify request: %v", notifyReq)
	}

	var refundOrder models.RefundOrder

	successTime, parseErr := utils.ParseLocationRFC3339(refundNotification.SuccessTime)
	if parseErr != nil {
		log.Errorf("parse wechat refund notify successTime error: %v, will use the callback time as refundedAt", parseErr)
		successTime = time.Now()
	}
	refundOrder.No = refundNotification.OutRefundNo
	refundOrder.WcRefundID = refundNotification.RefundID

	if refundNotification.Amount.Refund != nil {
		refundOrder.ActualRefundedAmount = *refundNotification.Amount.Refund
	}
	refundOrder.RefundedAt = successTime

	refundOrder.UpdatedAt = time.Now()
	refundOrder.Status = models.OrderStatusCompleted

	if err := persistOrder(&refundOrder); err != nil {
		c.JSON(nethttp.StatusInternalServerError, gin.H{"code": "FAIL", "message": err.Error()})
		log.Errorf("process refund order error: %v", err)
		return
	}
	c.JSON(nethttp.StatusOK, nil)
}
