package models

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	hub "git.sho/ComputeHub/ComputeHub"
)

const (
	DefaultCheckRemoteStateIntervalBySeconds uint = 10
	DefaultSyncSpecIntervalBySeconds         uint = 10

	DefaultDynamicPayCheckIntervalBySeconds   uint = 30
	DefaultDynamicPayBillingIntervalBySeconds uint = 3600 // 1 hour
	DefaultDynamicPayMinimalDurationBySeconds uint = 300
)

const (
	SysCfgCheckRemoteStateIntervalBySeconds = "check_remote_state_interval_by_seconds"
	SysCfgSyncSpecIntervalBySeconds         = "sync_spec_interval_by_seconds"
	SysCfgMaxAppDurationByHours             = "max_app_duration_by_hours"

	SysCfgGlobalOverdraftLimitInCents        = "global_overdraft_limit_in_cents"
	SysCfgDynamicPayCheckIntervalBySeconds   = "dynamic_pay_check_interval_by_seconds"
	SysCfgDynamicPayBillingIntervalBySeconds = "dynamic_pay_billing_interval_by_seconds"
	SysCfgDynamicPayMinimalDurationBySeconds = "dynamic_pay_minimal_duration_by_seconds"
)

type SystemConfig struct {
	Id uint `json:"id" gorm:"column:id; PRIMARY_KEY; AUTO_INCREMENT"`

	Name        string `json:"name" gorm:"unique_index; not null"`
	Value       string `json:"value"`
	DisplayName string `json:"displayName" gorm:"unique_index; not null"`
	Comment     string `json:"comment"`

	CreatedAt time.Time `json:"createdAt" gorm:"not null"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"not null"`
}

func (sc *SystemConfig) TrimSpace() {
	sc.Name = strings.TrimSpace(sc.Name)
	sc.Value = strings.TrimSpace(sc.Value)
	sc.DisplayName = strings.TrimSpace(sc.DisplayName)
	sc.Comment = strings.TrimSpace(sc.Comment)
}

func GetSystemConfigs(db *gorm.DB) (map[string]any, error) {
	systemConfigs := []*SystemConfig{}
	if err := db.Model(&SystemConfig{}).Find(&systemConfigs).Error; err != nil {
		return nil, hub.QueryDatabaseErr.FromError(err)
	}

	cfgs := map[string]any{
		SysCfgMaxAppDurationByHours:              uint(0),
		SysCfgSyncSpecIntervalBySeconds:          DefaultSyncSpecIntervalBySeconds,
		SysCfgCheckRemoteStateIntervalBySeconds:  DefaultCheckRemoteStateIntervalBySeconds,
		SysCfgGlobalOverdraftLimitInCents:        uint(0),
		SysCfgDynamicPayCheckIntervalBySeconds:   DefaultDynamicPayCheckIntervalBySeconds,
		SysCfgDynamicPayBillingIntervalBySeconds: DefaultDynamicPayBillingIntervalBySeconds,
		SysCfgDynamicPayMinimalDurationBySeconds: DefaultDynamicPayMinimalDurationBySeconds,
	}
	for _, systemConfig := range systemConfigs {
		switch systemConfig.Name {
		case
			SysCfgMaxAppDurationByHours,
			SysCfgSyncSpecIntervalBySeconds,
			SysCfgCheckRemoteStateIntervalBySeconds,
			SysCfgGlobalOverdraftLimitInCents,
			SysCfgDynamicPayCheckIntervalBySeconds,
			SysCfgDynamicPayBillingIntervalBySeconds,
			SysCfgDynamicPayMinimalDurationBySeconds:
			// parse as uint
			if val, err := strconv.ParseUint(systemConfig.Value, 10, 64); err != nil {
				return nil, fmt.Errorf("failed to parse system config (%+v), %v", systemConfig, err)
			} else {
				cfgs[systemConfig.Name] = uint(val)
			}
		}
	}

	return cfgs, nil
}
