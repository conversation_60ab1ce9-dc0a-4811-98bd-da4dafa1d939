package suanlink

import (
	"github.com/gaoxing520/errors"
)

var (
	Unknown   = errors.Unknown
	ErrSystem = errors.ErrSystem
)

var (
	Success = errors.Success
)

var (
	ErrUnmarshal = errors.NewAppError(9999, "unmarshal failed")
)

var (
	ErrLoadConfig  = errors.NewAppError(4501, "load config failed")
	ErrParseConfig = errors.NewAppError(4502, "parse config failed")
	ErrRequest     = errors.NewAppError(4503, "invalid request")
	ErrApiRequest  = errors.NewAppError(4504, "request api failed")
	ErrParameter   = errors.NewAppError(4505, "invalid parameter")
)

var (
	Info = errors.Info
)
