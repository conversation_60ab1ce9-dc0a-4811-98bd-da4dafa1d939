package utils

import (
	"fmt"
	"strings"
	"time"

	"github.com/shopspring/decimal"

	"git.sho/ComputeHub/ComputeHub/pkg/models"
	"git.sho/ComputeHub/SuanLink/pkg/consts"
)

// FormatAppState 格式化应用状态显示
func FormatAppState(state string) string {
	switch state {
	case models.AppStateRunning:
		return "运行中"
	case models.AppStateStopped:
		return "已停止"
	case models.AppStateStarting:
		return "启动中"
	case models.AppStateStopping:
		return "停止中"
	case models.AppStateRebooting:
		return "重启中"
	case models.AppStateError:
		return "错误"
	case models.AppStateCreated:
		return "已创建"
	case models.AppStateReleased:
		return "已释放"
	default:
		return state
	}
}

// FormatSupport 格式化支持与否
func FormatSupport(support bool) string {
	if support {
		return "支持"
	} else {
		return "不支持"
	}
}

// FormatBillingMethods 格式化计费方式数组显示
func FormatBillingMethods(methods []string) string {
	if len(methods) == 0 {
		return "-"
	}

	var formattedMethods []string
	for _, method := range methods {
		switch method {
		case models.BillingMethodDynamicPay:
			formattedMethods = append(formattedMethods, "按量付费")
		case models.BillingMethodPrepayByHours:
			formattedMethods = append(formattedMethods, "按小时预付费")
		case models.BillingMethodPrepayByDays:
			formattedMethods = append(formattedMethods, "按天预付费")
		case models.BillingMethodPrepayByWeeks:
			formattedMethods = append(formattedMethods, "按周预付费")
		case models.BillingMethodPrepayByMonths:
			formattedMethods = append(formattedMethods, "按月预付费")
		default:
			formattedMethods = append(formattedMethods, method)
		}
	}

	return strings.Join(formattedMethods, ", ")
}

// FormatBillingMethod 格式化计费方式显示
func FormatBillingMethod(method string) string {
	switch method {
	case models.BillingMethodDynamicPay:
		return "按量付费"
	case models.BillingMethodPrepayByHours:
		return "按小时预付费"
	case models.BillingMethodPrepayByDays:
		return "按天预付费"
	case models.BillingMethodPrepayByWeeks:
		return "按周预付费"
	case models.BillingMethodPrepayByMonths:
		return "按月预付费"
	default:
		return method
	}
}

// FormatOperationDescription 获取操作描述
func FormatOperationDescription(operation string) string {
	switch operation {
	case "start":
		return "启动应用"
	case "start-with-simple-mode":
		return "经济模式启动应用"
	case "stop":
		return "停止应用"
	case "restart":
		return "重启应用"
	case "release":
		return "释放应用"
	case "renew":
		return "续费应用"
	default:
		return operation
	}
}

// FormatServiceTypeName 格式化服务类型显示名称
func FormatServiceTypeName(serviceType string) string {
	switch strings.ToLower(serviceType) {
	case models.ServiceNameSsh:
		return "SSH远程终端"
	case models.ServiceNameVnc:
		return "VNC远程桌面"
	case models.ServiceNameRdp:
		return "RDP远程桌面"
	default:
		return strings.ToUpper(serviceType)
	}
}

// FormatPriceCentsIntoYuan 格式化价格从分到元
func FormatPriceCentsIntoYuan(priceInCents int64) string {
	return decimal.NewFromInt(priceInCents).Div(decimal.NewFromInt(100)).String()
}

func FormatDateTime(t time.Time, layout string) string {
	if t.IsZero() {
		return ""
	}

	if t.Before(Year2020) {
		return ""
	}

	return t.Format(layout)
}

func FormatCommonTime(t time.Time) string {
	return FormatDateTime(t, consts.CommonTimeFormat)
}

func FormatOrderStatus(status string) string {
	statusMap := map[string]string{
		models.OrderStatusPending:    "待支付",
		models.OrderStatusProcessing: "处理中",
		models.OrderStatusPayed:      "已支付",
		models.OrderStatusCompleted:  "已完成",
		models.OrderStatusCancelled:  "已取消",
		models.OrderStatusRefunded:   "已退款",
		models.OrderStatusFailed:     "失败",
	}

	if chinese, ok := statusMap[status]; ok {
		return chinese
	}
	return status
}

func FormatRefundStatus(status string) string {
	statusMap := map[string]string{
		models.OrderStatusPending:    "待处理",
		models.OrderStatusProcessing: "处理中",
		models.OrderStatusCompleted:  "已完成",
		models.OrderStatusFailed:     "失败",
	}

	if chinese, ok := statusMap[status]; ok {
		return chinese
	}
	return status
}

func FormatBillType(billType string) string {
	typeMap := map[string]string{
		models.BillTypeUserAccountDeposit: "账户充值",
		models.BillTypeCreateApp:          "创建应用",
		models.BillTypeCreateAppRefund:    "创建应用退款",
		models.BillTypeRenewApp:           "续费应用",
		models.BillTypeRenewAppRefund:     "续费应用退款",
		models.BillTypeStopApp:            "停止应用",
		models.BillTypeAppDeduct:          "应用扣费",
		models.BillTypeErrorAppRefund:     "错误应用退款",
	}

	if chinese, ok := typeMap[billType]; ok {
		return chinese
	}
	return billType
}

// FormatPlatformRole 格式化平台角色显示
func FormatPlatformRole(role uint8) string {
	switch role {
	case models.PlatformRoleUser:
		return "普通用户"
	case models.PlatformRoleAdmin:
		return "管理员"
	default:
		return fmt.Sprintf("未知角色(%d)", role)
	}
}
